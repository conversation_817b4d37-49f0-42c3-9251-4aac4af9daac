# Laser Cutting Calculator

A professional Flutter application for laser cutting price estimation and project management.

## Features

- **Calculator**: Comprehensive cost estimation tool with material and gas type selection
- **Client Management**: Database for managing client information and project history
- **Calculation History**: Track and manage all past calculations with filtering and sorting
- **Configuration Panel**: Customize material rates, gas costs, and pricing parameters

## Project Structure

```
lib/
├── models/          # Data models (Client, Calculation, JobConfig)
├── providers/       # State management (Calculator, Client, History)
├── screens/         # Main application screens
├── widgets/         # Reusable UI components
├── constants/       # Application constants and mock data
└── main.dart        # Application entry point
```

## Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the application:
   ```bash
   flutter run
   ```

## Features Overview

### Calculator Screen
- Client information management
- Design service configuration
- Material specification (Inox, Steel, Aluminum, Copper, etc.)
- Gas type selection (Air, Oxygen, Nitrogen)
- Real-time price calculation
- Project quote generation

### Client Management
- Add, edit, and delete clients
- Search and filter functionality
- Project history tracking
- Contact information management

### Calculation History
- View all past calculations
- Filter by status (Completed, Pending, Cancelled)
- Sort by date, price, or client name
- Export and delete functionality

### Configuration Panel
- Material rate management
- Gas cost configuration by thickness
- Setup fee customization
- Tariff structure modification

## Technology Stack

- **Framework**: Flutter
- **State Management**: Provider
- **UI**: Material Design 3
- **Date Formatting**: intl package

## Usage

1. **Start Calculation**: Use the calculator screen to input project details
2. **Manage Clients**: Add new clients or select existing ones from the database
3. **Review History**: Check past calculations and their status
4. **Configure Settings**: Adjust pricing parameters in the configuration panel

The application provides a complete solution for laser cutting businesses to estimate costs, manage clients, and track project history efficiently.
