#!/bin/bash

echo "=== LICENSE SERVER DEBUG SCRIPT ==="
echo ""

echo "1. Testing server connectivity..."
curl -v http://localhost:8000/ 2>&1 | head -20
echo ""

echo "2. Testing main endpoint with GET..."
curl -v -H "Accept: application/json" http://localhost:8000/api/v1/validate 2>&1 | head -20
echo ""

echo "3. Testing main endpoint with POST..."
curl -v -X POST \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"apiKey":"test","payload":"test"}' \
  http://localhost:8000/api/v1/validate 2>&1 | head -20
echo ""

echo "4. Testing if server is running on other common ports..."
for port in 3000 5000 8080 8081 9000; do
  echo "  Testing port $port..."
  timeout 2 curl -s http://localhost:$port/ >/dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "  ✓ Server responding on port $port"
    curl -s http://localhost:$port/ | head -5
  else
    echo "  ✗ No response on port $port"
  fi
done
echo ""

echo "5. Checking what's listening on port 8000..."
netstat -tulpn | grep :8000 2>/dev/null || echo "  No service found listening on port 8000"
lsof -i :8000 2>/dev/null || echo "  No process found using port 8000"
echo ""

echo "=== INSTRUCTIONS ==="
echo ""
echo "If your Python client works, please:"
echo "1. Check what URL your Python client uses"
echo "2. Check what port your server is actually running on"  
echo "3. Try running your Python client to confirm it still works"
echo "4. Check if the server needs to be started or configured differently"
echo ""
echo "The server is currently returning HTTP 400 for all requests,"
echo "which suggests it's not properly configured or not the right server."
