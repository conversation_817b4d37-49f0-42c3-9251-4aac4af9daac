# License Validation System

This license validation system provides secure license checking for your Flutter laser cutting calculator application. It includes client-side validation with server verification and caching.

## Components

### 1. License Validation Service (`lib/services/license_validation_service.dart`)
- Handles communication with the license server
- Generates hardware-specific identifiers
- Caches validation results for 24 hours
- Supports custom server URLs

### 2. License Provider (`lib/providers/license_provider.dart`)
- Manages license state throughout the application
- Provides reactive updates to UI components
- Handles background validation

### 3. License Guard Widget (`lib/widgets/license_guard.dart`)
- Protects screens/features behind license validation
- Shows appropriate UI based on license status
- Provides easy-to-use mixins and extensions

### 4. License Validation Screen (`lib/screens/license_validation_screen.dart`)
- User interface for entering license credentials
- Shows license status and validation history
- Supports advanced configuration options

## Setup and Usage

### 1. Dependencies
The system requires these Flutter dependencies (already added to pubspec.yaml):
```yaml
dependencies:
  http: ^1.2.0
  crypto: ^3.0.5
  device_info_plus: ^10.1.2
  shared_preferences: ^2.3.2
```

### 2. Initialize License Provider
The license provider is automatically initialized in `main.dart` and available throughout the app.

### 3. Protect Your App Content
Wrap sensitive parts of your application with the `LicenseGuard`:

```dart
// Protect entire app
LicenseGuard(
  child: MyAppContent(),
)

// Protect specific features
LicenseGuard(
  child: PremiumFeatureScreen(),
  lockedMessage: "Premium license required for this feature",
)
```

### 4. Check License Programmatically
Use the context extension for quick license checks:

```dart
// Check if license is valid
if (context.hasValidLicense) {
  // Execute premium feature
} else {
  // Show license requirement
}

// Require license before action
context.requireLicense(() {
  // This only executes if license is valid
  performPremiumAction();
});
```

### 5. Manual License Validation
Navigate to the license validation screen:

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => LicenseValidationScreen(),
  ),
);
```

## License Server

### Test Server
A Python test server is provided (`license_server.py`) for development and testing:

```bash
# Install dependencies
pip install cryptography

# Run server
python license_server.py

# Server will run on http://localhost:8000
```

### Test Credentials
For testing, use these credentials:
- **API Key:** `73bde6e0-1cca-4093-a785-d12e2ce67d7e`
- **Serial Numbers:** `EXAUY-J1VPW-WN71A-AKRBJ` or `ABCDE-FGHIJ-KLMNO-PQRST`

### Server API Endpoint
```
POST /api/v1/validate
Content-Type: application/json

{
  "apiKey": "73bde6e0-1cca-4093-a785-d12e2ce67d7e",
  "payload": "base64-encoded-payload",
  "hwid": "hardware-id",
  "serial": "EXAUY-J1VPW-WN71A-AKRBJ"
}
```

### Response Format
```json
{
  "Code": "SUCCESS",
  "Message": "License validated successfully"
}
```

## Response Codes

### Success Codes
- `SUCCESS` / `OKAY` - License validated successfully

### Error Codes
- `INVALID_LICENSE` - Invalid license key
- `EXPIRED_LICENSE` - License has expired
- `INVALID_HWID` - Hardware ID mismatch
- `QUOTA_EXCEEDED` - License usage quota exceeded
- `INVALID_API_KEY` - Invalid API key
- `TIMEOUT` - Request timeout
- `NO_CONNECTION` - Cannot connect to server

## Security Features

### Hardware Binding
The system generates unique hardware identifiers based on:
- **Android:** Device ID, model, manufacturer
- **iOS:** Identifier for vendor, model  
- **Linux:** Machine ID, hostname
- **Windows:** Computer name, core count
- **macOS:** System GUID, computer name

### Validation Caching
- Valid licenses are cached for 24 hours
- Reduces server load and improves offline functionality
- Cache can be manually cleared

### Secure Storage
- License credentials are stored using SharedPreferences
- Hardware ID is hashed for privacy
- Server communication uses HTTPS (configurable)

## Integration Examples

### Protect Calculator Features
```dart
class CalculatorScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LicenseGuard(
      child: Scaffold(
        body: CalculatorContent(),
      ),
    );
  }
}
```

### Conditional Feature Access
```dart
class ConfigurationScreen extends StatelessWidget with LicenseAware {
  void onAdvancedSettings() {
    if (isLicenseValid) {
      Navigator.push(context, AdvancedSettingsScreen());
    } else {
      showLicenseRequiredSnackBar();
    }
  }
}
```

### License Status in UI
```dart
Consumer<LicenseProvider>(
  builder: (context, licenseProvider, _) {
    return ListTile(
      leading: Icon(
        licenseProvider.isLicenseValid ? Icons.check : Icons.error,
        color: licenseProvider.isLicenseValid ? Colors.green : Colors.red,
      ),
      title: Text('License Status'),
      subtitle: Text(licenseProvider.getStatusMessage()),
    );
  },
)
```

## Deployment Notes

1. **Replace Test Server:** In production, replace the test server URL with your actual license server
2. **Update Public Key:** Replace the public key in `LicenseValidationService` with your production key
3. **Configure Timeout:** Adjust validation timeout based on your server response time
4. **Error Handling:** Customize error messages and UI based on your app's design

## Testing

Run the license validation tests:
```bash
flutter test test/license_validation_test.dart
```

## Troubleshooting

### Common Issues
1. **Network connectivity** - Ensure device has internet access
2. **Server unavailable** - Verify license server is running and accessible
3. **Invalid credentials** - Double-check API key and serial number format
4. **Hardware ID mismatch** - License might be bound to different device

### Debug Information
Enable debug logging to see validation details:
```dart
// License validation service automatically logs debug information
// Check Flutter console for validation details
```

This license validation system provides a robust foundation for protecting your Flutter application while maintaining good user experience through caching and clear error handling.
