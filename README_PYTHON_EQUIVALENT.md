# Python to Flutter/Dart Authentication Equivalent

This document shows the direct Flutter/Dart equivalent of your Python authentication code.

## Python Original Code

```python
import base64
import requests
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization

def authentication(public_key, api_key, serial, hwid):
    """
    Connects to the server and authenticates the license
    """
    plaintexts = bytes(serial + ':' + hwid, 'utf-8')
    if isinstance(public_key, rsa.RSAPublicKey):
        payload = public_key.encrypt(
            plaintexts,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        final_payload = base64.b64encode(payload).decode('utf-8')
        request_path = 'http://localhost:8000/api/v1/validate'
        server_request = requests.post(request_path, json={
            "apiKey": api_key, "payload": final_payload
        }, timeout=10)
        response_code = server_request.json()['Code']
        success_codes=['OKAY', 'SUCCESS']
        if any(response_code in i for i in success_codes):
            print(f"Authentication: {response_code}")
            return True
        print(f"Error: {response_code}")
        return False

if __name__ == "__main__":
    PUB_KEY = """-----BEGIN PUBLIC KEY-----..."""
    PUBLIC_KEY = serialization.load_pem_public_key(str.encode(PUB_KEY))
    API_KEY = 'a080015b-b827-48f8-a96d-dc3ccc650bc8'
    SERIAL = 'O4T1K-RW2I9-HDAZY-K98BQ'
    HWID = 'MEHDI'
    authentication(PUBLIC_KEY, API_KEY, SERIAL, HWID)
```

## Flutter/Dart Equivalent

### Dependencies Required

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  http: ^1.2.0
  pointycastle: ^4.0.0
  basic_utils: ^5.8.1
```

### Implementation

```dart
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

class PythonEquivalentAuth {
  static const String publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

  static const String apiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
  static const String requestPath = 'http://localhost:8000/api/v1/validate';

  /// Direct equivalent of Python authentication function
  static Future<bool> authentication(
    RSAPublicKey publicKey,
    String apiKey,
    String serial,
    String hwid,
  ) async {
    try {
      // Python: plaintexts = bytes(serial + ':' + hwid, 'utf-8')
      final plaintexts = '$serial:$hwid';
      final plaintextBytes = Uint8List.fromList(utf8.encode(plaintexts));

      // Python: payload = public_key.encrypt(plaintexts, padding.OAEP(...))
      final cipher = OAEPEncoding.withSHA256(RSAEngine());
      cipher.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
      final payload = cipher.process(plaintextBytes);

      // Python: final_payload = base64.b64encode(payload).decode('utf-8')
      final finalPayload = base64Encode(payload);

      // Python: server_request = requests.post(...)
      final serverRequest = await http.post(
        Uri.parse(requestPath),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'apiKey': apiKey,
          'payload': finalPayload,
        }),
      ).timeout(const Duration(seconds: 10));

      // Python: response_code = server_request.json()['Code']
      final responseData = jsonDecode(serverRequest.body) as Map<String, dynamic>;
      final responseCode = responseData['Code'] as String?;

      // Python: success_codes=['OKAY', 'SUCCESS']
      const successCodes = ['OKAY', 'SUCCESS'];

      // Python: if any(response_code in i for i in success_codes):
      if (responseCode != null && successCodes.contains(responseCode)) {
        print('Authentication: $responseCode');
        return true;
      }

      print('Error: $responseCode');
      return false;
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  static RSAPublicKey parsePublicKeyFromPem(String pemString) {
    return CryptoUtils.rsaPublicKeyFromPem(pemString);
  }

  /// Equivalent to Python's if __name__ == "__main__":
  static Future<void> main() async {
    final publicKey = parsePublicKeyFromPem(publicKeyPem);
    const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
    const hwid = 'MEHDI';
    await authentication(publicKey, apiKey, serial, hwid);
  }
}
```

## Usage Examples

### Basic Usage (equivalent to running Python script)

```dart
void main() async {
  await PythonEquivalentAuth.main();
}
```

### Custom Authentication

```dart
void main() async {
  final publicKey = PythonEquivalentAuth.parsePublicKeyFromPem(
    PythonEquivalentAuth.publicKeyPem,
  );
  
  final result = await PythonEquivalentAuth.authentication(
    publicKey,
    'your-api-key',
    'your-serial',
    'your-hwid',
  );
  
  if (result) {
    print('✅ Authentication successful!');
  } else {
    print('❌ Authentication failed!');
  }
}
```

## Key Differences & Equivalents

| Python | Flutter/Dart | Notes |
|--------|--------------|-------|
| `bytes(serial + ':' + hwid, 'utf-8')` | `utf8.encode('$serial:$hwid')` | String to bytes conversion |
| `padding.OAEP(mgf=MGF1(SHA256), algorithm=SHA256)` | `OAEPEncoding.withSHA256(RSAEngine())` | RSA OAEP padding with SHA256 |
| `base64.b64encode(payload).decode('utf-8')` | `base64Encode(payload)` | Base64 encoding |
| `requests.post(url, json=data, timeout=10)` | `http.post(Uri.parse(url), body=jsonEncode(data)).timeout(Duration(seconds: 10))` | HTTP POST request |
| `serialization.load_pem_public_key()` | `CryptoUtils.rsaPublicKeyFromPem()` | PEM key parsing |

## Testing

Run the tests to verify the implementation:

```bash
flutter test test/python_equivalent_auth_test.dart
```

Run the example:

```bash
dart run example/python_equivalent_usage.dart
```

## Files Created

1. `lib/services/python_equivalent_auth.dart` - Main implementation
2. `test/python_equivalent_auth_test.dart` - Tests
3. `example/python_equivalent_usage.dart` - Usage examples

This implementation provides a 1:1 equivalent of your Python authentication function in Flutter/Dart.
