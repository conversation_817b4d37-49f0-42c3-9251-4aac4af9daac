# Database Seeding

This document explains how to use the database seeding functionality in the Laser Cutting Calculator app.

## Overview

The seeding system automatically populates the database with initial data when the app is first launched. This includes:

- **Materials**: Default material types with pricing rates
- **Gas Types**: Gas types with thickness-based pricing
- **Configuration**: Application settings and defaults
- **Sample Clients**: 5 demo clients with realistic data
- **Sample Calculations**: 25 demo calculations with various statuses

## Automatic Seeding

The app automatically checks if the database needs seeding on startup:

1. If this is the first launch (database is empty), seeding runs automatically
2. If the database has already been seeded, seeding is skipped
3. The seeding status is tracked in the database configuration

## Manual Seeding Operations

### Using Development Tools (Debug Mode Only)

In debug mode, the Configuration screen includes development tools:

1. **Status**: Check database seeding status and statistics
2. **Re-seed**: Force re-seed the database (overwrites existing data)
3. **Reset**: Reset database to initial state (clear + seed)
4. **Clear All**: Remove all data without seeding

### Programmatic Access

```dart
import 'package:laser_cutting_calculator/database/database_seeder.dart';
import 'package:laser_cutting_calculator/utils/dev_database_utils.dart';

// Check if seeding is needed
final needsSeeding = await DatabaseSeeder.needsSeeding();

// Run seeding
await DatabaseSeeder.run();

// Force re-seed
await DatabaseSeeder.run(force: true);

// Reset to initial state
await DatabaseSeeder.reset();

// Development utilities
await DevDatabaseUtils.checkStatus();
await DevDatabaseUtils.forceSeed();
await DevDatabaseUtils.resetDatabase();
await DevDatabaseUtils.clearAllData();
```

## Seeded Data Details

### Materials
- **Acier (Steel)**: 25.0 DH
- **Inox (Stainless Steel)**: 35.0 DH
- **Cuivre (Copper)**: 45.0 DH
- **Aluminum**: 30.0 DH
- **Tôle Galvanisée**: 22.0 DH

### Gas Types with Thickness Pricing

**Oxygène (O₂)**:
- 1-5 mm: 8.0 DH
- 5-10 mm: 12.0 DH
- 10-15 mm: 18.0 DH
- >15 mm: 25.0 DH

**Azote (N₂)**:
- 1-5 mm: 6.0 DH
- 5-10 mm: 9.0 DH
- 10-15 mm: 14.0 DH
- >15 mm: 20.0 DH

**Air Comprimé**:
- 1-5 mm: 4.0 DH
- 5-10 mm: 6.0 DH
- 10-15 mm: 9.0 DH
- >15 mm: 12.0 DH

### Configuration Settings
- Default gas type: Oxygène (O₂)
- Default material: Acier
- Default thickness: 5.0 mm
- Default cutting speed: 100.0
- Design service price: 50.0 DH
- Price per meter: 15.0 DH
- Currency: EUR
- Tax rate: 20.0%
- Minimum order amount: 25.0 DH

### Sample Clients
5 realistic client profiles with:
- French names and companies
- Email addresses and phone numbers
- Project counts and total spending history
- Creation dates spanning the last 6 months

### Sample Calculations
25 realistic calculations with:
- Random material and gas combinations
- Varied thicknesses (1-20mm)
- Random linear meters (5-55m)
- Mixed completion statuses
- Realistic pricing calculations
- Some with design services included

## Testing

The seeding functionality is thoroughly tested:

```bash
# Run seeding tests
flutter test test/seed_test.dart

# Run specific test
flutter test test/seed_test.dart --plain-name "should seed database with initial data"
```

## File Structure

```
lib/
├── database/
│   └── database_seeder.dart      # Main seeding interface
├── services/
│   └── seed_service.dart         # Core seeding logic
├── utils/
│   └── dev_database_utils.dart   # Development utilities
└── main.dart                     # Automatic seeding on startup

test/
└── seed_test.dart                # Seeding tests
```

## Customizing Seed Data

To modify the seed data:

1. Edit `lib/services/seed_service.dart`
2. Update the respective seeding methods:
   - `_seedMaterials()` for material data
   - `_seedGasTypes()` for gas type data
   - `_seedConfiguration()` for app settings
   - `_seedClients()` for sample clients
   - `_seedCalculations()` for sample calculations

3. For materials and gas types, the data format follows the database structure used by the respective services.

## Best Practices

1. **Production**: Seeding runs once automatically, then is skipped
2. **Development**: Use development tools for testing and resetting data
3. **Testing**: Set test mode before seeding: `DatabaseService.setTestMode(true)`
4. **Customization**: Modify seed data in `SeedService` for different scenarios

## Troubleshooting

### Database Already Seeded
If you need to re-seed:
- Use force option: `DatabaseSeeder.run(force: true)`
- Or use development tools in the Configuration screen

### Test Mode Issues
Ensure test mode is set correctly:
```dart
DatabaseService.setTestMode(true);  // For tests
DatabaseService.setTestMode(false); // For normal operation
```

### Console Output
In debug mode, seeding operations print progress and statistics to the console.
