import 'package:flutter/material.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';

/// Demo showing how expiry dates appear in the license management system
void main() {
  runApp(const LicenseExpiryDemo());
}

class LicenseExpiryDemo extends StatelessWidget {
  const LicenseExpiryDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'License Expiry Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const LicenseExpiryDemoScreen(),
    );
  }
}

class LicenseExpiryDemoScreen extends StatefulWidget {
  const LicenseExpiryDemoScreen({super.key});

  @override
  State<LicenseExpiryDemoScreen> createState() => _LicenseExpiryDemoScreenState();
}

class _LicenseExpiryDemoScreenState extends State<LicenseExpiryDemoScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('License Expiry Date Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'License Expiry Date Examples',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Valid license with distant expiry
            _buildLicenseCard(
              'Valid License (1 Year)',
              LicenseValidationResult(
                isValid: true,
                responseCode: 'SUCCESS',
                message: 'License validated successfully',
                timestamp: DateTime.now(),
                expiryDate: DateTime.now().add(const Duration(days: 365)),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // License expiring soon
            _buildLicenseCard(
              'License Expiring Soon (15 Days)',
              LicenseValidationResult(
                isValid: true,
                responseCode: 'SUCCESS',
                message: 'License validated successfully (expires in 15 days)',
                timestamp: DateTime.now(),
                expiryDate: DateTime.now().add(const Duration(days: 15)),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // License expiring very soon
            _buildLicenseCard(
              'License Expiring Very Soon (3 Days)',
              LicenseValidationResult(
                isValid: true,
                responseCode: 'SUCCESS',
                message: 'License validated successfully (expires in 3 days)',
                timestamp: DateTime.now(),
                expiryDate: DateTime.now().add(const Duration(days: 3)),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Expired license
            _buildLicenseCard(
              'Expired License',
              LicenseValidationResult(
                isValid: false,
                responseCode: 'ERR_KEY_EXPIRED',
                message: 'License has expired',
                timestamp: DateTime.now(),
                expiryDate: DateTime.now().subtract(const Duration(days: 5)),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // License without expiry date
            _buildLicenseCard(
              'License Without Expiry Date',
              LicenseValidationResult(
                isValid: true,
                responseCode: 'SUCCESS',
                message: 'License validated successfully',
                timestamp: DateTime.now(),
                expiryDate: null,
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Server Response Format',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Text(
                '''Based on your Python server code, the response includes:

{
  "Code": "SUCCESS",
  "Message": "SUCCESS :: Your registration was successful!",
  "ExpiryDate": "2024-12-31T23:59:59Z"
}

The Flutter app now parses the ExpiryDate field and displays:
• Days until expiry
• Warning when expiring soon (≤30 days)
• Error state when expired
• Formatted expiry date in the UI''',
                style: TextStyle(fontFamily: 'monospace', fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLicenseCard(String title, LicenseValidationResult result) {
    Color statusColor;
    IconData statusIcon;
    
    if (result.isExpired) {
      statusColor = Colors.red;
      statusIcon = Icons.error_outline;
    } else if (result.isExpiringSoon) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning_amber_outlined;
    } else if (result.isValid) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle_outline;
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.error_outline;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // Status row
            Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  result.isValid ? 'Valid' : 'Invalid',
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // Message
            Text(
              result.message,
              style: TextStyle(color: statusColor),
            ),
            
            const SizedBox(height: 8),
            
            // Expiry information
            if (result.expiryDate != null) ...[
              _buildExpiryInfo(result),
            ] else ...[
              const Text(
                'No expiry date provided',
                style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
              ),
            ],
            
            const SizedBox(height: 8),
            
            // Technical details
            Text(
              'Response Code: ${result.responseCode}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpiryInfo(LicenseValidationResult result) {
    if (result.expiryDate == null) return const SizedBox.shrink();

    final expiryDate = result.expiryDate!;
    final daysUntilExpiry = result.daysUntilExpiry ?? 0;
    final isExpired = result.isExpired;
    final isExpiringSoon = result.isExpiringSoon;

    Color expiryColor;
    IconData expiryIcon;
    String expiryText;

    if (isExpired) {
      expiryColor = Colors.red;
      expiryIcon = Icons.error_outline;
      expiryText = 'Expired on ${_formatDate(expiryDate)}';
    } else if (isExpiringSoon) {
      expiryColor = Colors.orange;
      expiryIcon = Icons.warning_amber_outlined;
      expiryText = 'Expires in $daysUntilExpiry day${daysUntilExpiry > 1 ? 's' : ''} (${_formatDate(expiryDate)})';
    } else {
      expiryColor = Colors.green;
      expiryIcon = Icons.check_circle_outline;
      expiryText = 'Valid until ${_formatDate(expiryDate)}';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: expiryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: expiryColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(expiryIcon, color: expiryColor, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              expiryText,
              style: TextStyle(
                color: expiryColor,
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
