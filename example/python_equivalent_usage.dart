import 'package:laser_cutting_calculator/services/python_equivalent_auth.dart';

/// Example usage of the Python equivalent authentication
/// 
/// This demonstrates how to use the Flutter/Dart implementation
/// that exactly mirrors the Python authentication function.
void main() async {
  print('=== Flutter/Dart Python Equivalent Authentication ===\n');

  // Example 1: Using the main function (equivalent to running Python script)
  print('1. Running main function (equivalent to Python script execution):');
  await PythonEquivalentAuth.main();
  print('');

  // Example 2: Manual authentication with custom values
  print('2. Manual authentication with custom values:');
  
  // Parse the public key
  final publicKey = PythonEquivalentAuth.parsePublicKeyFromPem(
    PythonEquivalentAuth.publicKeyPem,
  );
  
  // Custom values
  const customApiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
  const customSerial = 'CUSTOM-SERIAL-12345';
  const customHwid = 'CUSTOM_HWID';
  
  print('Using custom values:');
  print('- API Key: $customApiKey');
  print('- Serial: $customSerial');
  print('- HWID: $customHwid');
  
  final customResult = await PythonEquivalentAuth.authentication(
    publicKey,
    customApiKey,
    customSerial,
    customHwid,
  );
  
  print('Custom authentication result: $customResult');
  print('');

  // Example 3: Multiple authentication attempts
  print('3. Multiple authentication attempts with same credentials:');
  
  for (int i = 1; i <= 3; i++) {
    print('Attempt $i:');
    final result = await PythonEquivalentAuth.authentication(
      publicKey,
      PythonEquivalentAuth.apiKey,
      'O4T1K-RW2I9-HDAZY-K98BQ',
      'MEHDI',
    );
    print('  Result: $result');
  }
  
  print('\n=== Complete ===');
}

/// Widget example for Flutter app integration
/// 
/// This shows how you might integrate the authentication into a Flutter widget
class AuthenticationWidget {
  static Future<bool> authenticateUser({
    required String serialNumber,
    required String hardwareId,
  }) async {
    try {
      // Parse the public key
      final publicKey = PythonEquivalentAuth.parsePublicKeyFromPem(
        PythonEquivalentAuth.publicKeyPem,
      );
      
      // Perform authentication
      final isAuthenticated = await PythonEquivalentAuth.authentication(
        publicKey,
        PythonEquivalentAuth.apiKey,
        serialNumber,
        hardwareId,
      );
      
      return isAuthenticated;
    } catch (e) {
      print('Authentication error: $e');
      return false;
    }
  }
  
  /// Example usage in a Flutter app
  static Future<void> exampleUsageInApp() async {
    // Get user input (in real app, this would come from UI)
    const userSerial = 'O4T1K-RW2I9-HDAZY-K98BQ';
    const userHwid = 'MEHDI';
    
    // Authenticate
    final isValid = await authenticateUser(
      serialNumber: userSerial,
      hardwareId: userHwid,
    );
    
    if (isValid) {
      print('✅ User authenticated successfully!');
      // Navigate to main app or enable features
    } else {
      print('❌ Authentication failed!');
      // Show error message or restrict access
    }
  }
}
