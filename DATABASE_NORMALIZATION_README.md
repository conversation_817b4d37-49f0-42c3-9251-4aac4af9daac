# Database Normalization Implementation

This document explains the complete implementation of normalizing the configuration table in the Flutter Drift database for the Laser Cutting Calculator app.

## Overview

The original denormalized `Configurations` table stored all settings as key-value pairs. This implementation normalizes the data into proper relational tables with foreign key relationships, type safety, and better query performance.

## New Normalized Schema

### 1. Materials Table
Stores material types and their rates:
```dart
class Materials extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()();
  TextColumn get displayName => text().withLength(min: 1, max: 100)();
  RealColumn get rate => real()(); // Rate per hour in local currency
  BoolColumn get isDefault => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}
```

### 2. Gas Types Table
Stores different gas types used for cutting:
```dart
class GasTypes extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()();
  TextColumn get displayName => text().withLength(min: 1, max: 100)();
  BoolColumn get isDefault => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}
```

### 3. Thickness Ranges Table
Defines thickness ranges for gas cost calculation:
```dart
class ThicknessRanges extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()();
  TextColumn get displayName => text().withLength(min: 1, max: 100)();
  RealColumn get minThickness => real()();
  RealColumn get maxThickness => real().nullable()();
  IntColumn get sortOrder => integer()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}
```

### 4. Gas Costs Table (Junction Table)
Stores costs for gas type + thickness range combinations:
```dart
class GasCosts extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get gasTypeId => integer().references(GasTypes, #id, onDelete: KeyAction.cascade)();
  IntColumn get thicknessRangeId => integer().references(ThicknessRanges, #id, onDelete: KeyAction.cascade)();
  RealColumn get costPerHour => real()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  List<Set<Column>> get uniqueKeys => [
    {gasTypeId, thicknessRangeId}, // Ensure unique combination
  ];
}
```

### 5. App Settings Table
Stores general application configuration:
```dart
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get category => text().withLength(min: 1, max: 50)();
  TextColumn get key => text().withLength(min: 1, max: 100)();
  TextColumn get value => text()();
  TextColumn get valueType => text().withLength(min: 1, max: 20)();
  TextColumn get description => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  List<Set<Column>> get uniqueKeys => [
    {category, key}, // Ensure unique combination of category and key
  ];
}
```

## Migration Implementation

### Database Class Updates
```dart
@DriftDatabase(tables: [
  Clients, 
  Calculations, 
  Configurations, // Legacy table
  Materials, 
  GasTypes, 
  ThicknessRanges, 
  GasCosts, 
  AppSettings
])
class AppDatabase extends _$AppDatabase {
  @override
  int get schemaVersion => 2; // Incremented for migration

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        await _seedInitialNormalizedData();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 2) {
          await _migrateToNormalizedSchema(m);
        }
      },
    );
  }
}
```

### Migration Logic
The migration process:

1. **Creates new normalized tables**
2. **Reads existing configuration data** from the legacy `Configurations` table
3. **Parses and categorizes data**:
   - Material rates: `material_rate_*` → Materials table
   - Gas costs: `gas_cost_{gas_type}_{thickness_range}` → GasTypes, ThicknessRanges, GasCosts tables
   - App settings: Other keys → AppSettings table
4. **Preserves all existing data** without loss
5. **Handles type conversions** (text to bool, text to DateTime, etc.)

### Data Mapping Examples
```
Original: material_rate_acier = "25.0"
→ Materials: {name: "acier", displayName: "Acier (Steel)", rate: 25.0}

Original: gas_cost_oxygene_o2_1_5_mm = "8.0"
→ GasTypes: {name: "oxygene_o2", displayName: "Oxygène (O₂)"}
→ ThicknessRanges: {name: "1_5_mm", displayName: "1-5 mm", min: 1.0, max: 5.0}
→ GasCosts: {gasTypeId: 1, thicknessRangeId: 1, costPerHour: 8.0}

Original: design_service_price = "50.0"
→ AppSettings: {category: "pricing", key: "design_service_price", value: "50.0", valueType: "double"}
```

## Data Access Objects (DAOs)

### Materials DAO
```dart
@DriftAccessor(tables: [Materials])
class MaterialsDao extends DatabaseAccessor<AppDatabase> with _$MaterialsDaoMixin {
  // Methods: getAllMaterials, getMaterialByName, getDefaultMaterial, 
  // insertMaterial, updateMaterial, deleteMaterial, setDefaultMaterial, getMaterialRates
}
```

### Gas Types DAO
```dart
@DriftAccessor(tables: [GasTypes])
class GasTypesDao extends DatabaseAccessor<AppDatabase> with _$GasTypesDaoMixin {
  // Methods: getAllGasTypes, getGasTypeByName, getDefaultGasType,
  // insertGasType, updateGasType, deleteGasType, setDefaultGasType
}
```

### Gas Costs DAO
```dart
@DriftAccessor(tables: [GasCosts, GasTypes, ThicknessRanges])
class GasCostsDao extends DatabaseAccessor<AppDatabase> with _$GasCostsDaoMixin {
  // Methods: getAllGasCostsWithDetails, getGasCostsForGasType,
  // getGasCostForThickness, upsertGasCost, etc.
}
```

### App Settings DAO
```dart
@DriftAccessor(tables: [AppSettings])
class AppSettingsDao extends DatabaseAccessor<AppDatabase> with _$AppSettingsDaoMixin {
  // Methods: getSettingValue, getSettingValueAsDouble, getSettingValueAsBool,
  // upsertSetting, getDesignServicePrice, getPricePerMeter, etc.
}
```

## Usage Examples

### Getting Gas Cost for Cutting
```dart
final gasCostsDao = GasCostsDao(database);
final cost = await gasCostsDao.getGasCostForThickness('oxygene_o2', 7.5);
// Returns cost for Oxygen gas at 7.5mm thickness (uses 5-10mm range)
```

### Getting All Material Rates
```dart
final materialsDao = MaterialsDao(database);
final rates = await materialsDao.getMaterialRates();
// Returns: {'acier': 25.0, 'inox': 35.0, 'cuivre': 45.0, ...}
```

### Updating Settings
```dart
final appSettingsDao = AppSettingsDao(database);
await appSettingsDao.upsertSetting(
  'pricing', 
  'design_service_price', 
  '60.0', 
  'double'
);
```

### Complex Calculation
```dart
final examples = DatabaseUsageExamples(database);
final costs = await examples.calculateCuttingCost(
  materialName: 'acier',
  gasTypeName: 'oxygene_o2',
  thickness: 5.0,
  linearMeters: 10.0,
  cuttingSpeed: 100.0,
  designProvided: true,
);
// Returns detailed cost breakdown
```

## Benefits of Normalization

1. **Type Safety**: Proper data types instead of string storage
2. **Data Integrity**: Foreign key constraints prevent orphaned data
3. **Query Performance**: Indexed relationships for faster queries
4. **Maintainability**: Clear data structure and relationships
5. **Extensibility**: Easy to add new materials, gas types, etc.
6. **Data Validation**: Column constraints ensure data quality

## Files Modified/Created

- `lib/database/tables.dart` - Added normalized table definitions
- `lib/database/database.dart` - Updated with migration logic
- `lib/database/daos.dart` - Complete DAO implementations
- `lib/database/usage_examples.dart` - Usage examples and patterns

## Running the Migration

The migration runs automatically when the app starts and detects schema version < 2. The process:

1. Creates new normalized tables
2. Migrates existing data from `Configurations` table
3. Preserves all existing functionality
4. No data loss occurs during migration

## Testing

After migration, verify:
- All materials are properly migrated with correct rates
- Gas costs are correctly associated with gas types and thickness ranges
- App settings are preserved and accessible
- Default values are properly set
- All existing functionality continues to work
