import '../services/database_service.dart';
import '../repositories/configuration_repository.dart';


class MigrationService {
  static const String _migrationVersionKey = 'migration_version';
  static const int _currentMigrationVersion = 1;

  final DatabaseService _databaseService = DatabaseService.instance;
  final ConfigurationRepository _configRepository = ConfigurationRepository();

  Future<void> runMigrations() async {
    final currentVersion = await _getCurrentMigrationVersion();
    
    if (currentVersion < _currentMigrationVersion) {
      await _runMigration(currentVersion);
      await _setMigrationVersion(_currentMigrationVersion);
    }
  }

  Future<int> _getCurrentMigrationVersion() async {
    final versionString = await _configRepository.getConfigValue(_migrationVersionKey);
    return versionString != null ? int.tryParse(versionString) ?? 0 : 0;
  }

  Future<void> _setMigrationVersion(int version) async {
    await _configRepository.setConfigValue(_migrationVersionKey, version.toString());
  }

  Future<void> _runMigration(int fromVersion) async {
    switch (fromVersion) {
      case 0:
        await _migration1_InitialData();
        break;
      default:
        break;
    }
  }

  Future<void> _migration1_InitialData() async {
    // Check if we already have data
    final existingClients = await _databaseService.getAllClients();
    final existingCalculations = await _databaseService.getAllCalculations();
    
    // Only seed data if database is empty
    if (existingClients.isEmpty && existingCalculations.isEmpty) {
      await _seedInitialData();
    }
  }

  Future<void> _seedInitialData() async {
    // No initial data seeding - database starts empty
    print('Database initialized with empty state');
  }

  Future<void> clearAllData() async {
    await _databaseService.clearAllData();
    await _setMigrationVersion(0);
  }

  Future<bool> hasInitialData() async {
    final clients = await _databaseService.getAllClients();
    return clients.isNotEmpty;
  }
}
