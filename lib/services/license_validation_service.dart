import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';
import '../database/database.dart';
import '../database/daos.dart';

/// License validation service for authenticating with remote license server
class LicenseValidationService {
  final AppDatabase _database;
  late final LicensesDao _licensesDao;

  LicenseValidationService(this._database) {
    _licensesDao = LicensesDao(_database);
  }
  /// RSA Public Key for license validation 
  /// TODO: Replace with your actual RSA public key from the working Python client
  static const String _publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArpazceA9fb4RRjtAF41V
dlMlaKXOA77OjF4GAnOIWlZgIiL/INeVigAK2aiU7vNbSZZrIH26DghamvBkRpCv
/Yj4gsx87EAdQNdDMqWE0mGuEaZITlGESKTeUoQYefwyctuHpWiQdmziY0Dw5IjR
ZySLpGHePHWEp4+Z4BTtTbGT4O+DbfMkxRuD1TNoHnaEOKxZki3UWFTYbOiyO5HT
C0OUYHy5AcoGACv9LMhBfYmAgeWPDYS019wLiyyz9jZIUnq0IfBGgt5VpvD+TO/R
rN81gYXMw9K4FlgY9upK9aTtN6hc176RqkoEahRWDqkcE5ZFH11Wcbor5i2dNozB
zQIDAQAB 
-----END PUBLIC KEY-----
''';

  /// API Key for license validation
  /// TODO: Replace with your actual API key from the working Python client
  static const String _apiKey = '25cf537a-47b3-4699-9190-64e33ec05dfb';
  static const String _defaultLicenseServerUrl = 'https://lkm-app.fly.dev/api/v1/validate';
  static const String _licenseValidationKey = 'license_validation_status';
  static const String _lastValidationKey = 'last_license_validation';
  static const String _serialNumberKey = 'license_serial_number';
  
  // Cache validation result for 24 hours
  static const Duration _validationCacheDuration = Duration(minutes: 1);

  static LicenseValidationService? _instance;

  LicenseValidationService._internal(this._database) {
    _licensesDao = LicensesDao(_database);
  }

  static LicenseValidationService getInstance(AppDatabase database) {
    _instance ??= LicenseValidationService._internal(database);
    return _instance!;
  }

  /// Validate license with the remote server
  Future<LicenseValidationResult> validateLicense({
    required String serialNumber,
    String? customServerUrl,
    Duration timeout = const Duration(seconds: 30),
    bool enableDebug = false,
    String? customHwid, // Add option to use custom HWID for testing
  }) async {
    try {
      // Check cached validation first
      final cachedResult = await _getCachedValidation();
      if (cachedResult != null) {
        debugPrint('License validation: Using cached result');
        return cachedResult;
      }

      // Generate hardware ID (or use custom one for testing)
      final hwid = customHwid ?? await _generateHardwareId();
      
      if (enableDebug) {
        debugPrint('License validation debug:');
        debugPrint('- Serial Number: $serialNumber');
        debugPrint('- Hardware ID: $hwid');
      }
      
      // Prepare payload for encryption (serial:hwid)
      final payload = '$serialNumber:$hwid';
      
      // Encrypt payload using RSA with OAEP padding (same as Python example)
      final encryptedPayload = await _encryptPayload(payload);
      final encodedPayload = base64Encode(encryptedPayload);
      
      if (enableDebug) {
        debugPrint('- Payload: $payload');
        debugPrint('- Encrypted payload length: ${encryptedPayload.length}');
        debugPrint('- Base64 encoded length: ${encodedPayload.length}');
        debugPrint('- First 50 chars of encoded payload: ${encodedPayload.substring(0, math.min(50, encodedPayload.length))}...');
      }
      
      // Make request to license server (exact format as Python example)
      final serverUrl = customServerUrl ?? _defaultLicenseServerUrl;
      final requestBody = jsonEncode({
        'apiKey': _apiKey,
        'payload': encodedPayload,
      });
      
      if (enableDebug) {
        debugPrint('- Server URL: $serverUrl');
        debugPrint('- Request body length: ${requestBody.length}');
      }
      
      final response = await http.post(
        Uri.parse(serverUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: requestBody,
      ).timeout(timeout);

      debugPrint('License validation response: ${response.body}');

      // Parse response regardless of status code
      try {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        final responseCode = responseData['Code'] as String?;
        final message = responseData['Message'] as String?;
        final httpCode = responseData['HttpCode'] as String?;
        
        // Check if this is a successful validation
        if (response.statusCode == 200 && responseCode != null) {
          final result = _parseValidationResponse(responseCode, responseData);

          // Cache successful validation and save to database
          if (result.isValid) {
            await _cacheValidation(result);
            await _storeLicenseCredentials(serialNumber);
            await _saveLicenseToDatabase(serialNumber, hwid, result, customServerUrl);
          } else {
            // Also save failed validations to database for tracking
            await _saveLicenseToDatabase(serialNumber, hwid, result, customServerUrl);
          }

          return result;
        } else {
          // Handle error responses (including 401)
          return LicenseValidationResult(
            isValid: false,
            responseCode: responseCode ?? 'HTTP_ERROR_${response.statusCode}',
            message: message ?? 'Server returned status ${response.statusCode}',
            timestamp: DateTime.now(),
            httpCode: httpCode,
            serverResponse: responseData,
          );
        }
      } catch (e) {
        return LicenseValidationResult(
          isValid: false,
          responseCode: 'HTTP_ERROR_${response.statusCode}',
          message: 'Server returned status ${response.statusCode}: ${response.body}',
          timestamp: DateTime.now(),
        );
      }
    } on SocketException {
      return LicenseValidationResult(
        isValid: false,
        responseCode: 'NO_CONNECTION',
        message: 'Unable to connect to license server',
        timestamp: DateTime.now(),
      );
    } catch (e) {
      // Handle timeout and other HTTP exceptions
      if (e.toString().contains('TimeoutException') || e.toString().contains('timeout')) {
        return LicenseValidationResult(
          isValid: false,
          responseCode: 'TIMEOUT',
          message: 'License validation request timed out',
          timestamp: DateTime.now(),
        );
      }
      
      debugPrint('License validation error: $e');
      return LicenseValidationResult(
        isValid: false,
        responseCode: 'UNKNOWN_ERROR',
        message: 'Unknown error occurred during validation: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  /// Check if there's a valid cached license
  Future<bool> isLicenseValid() async {
    final cachedResult = await _getCachedValidation();
    return cachedResult?.isValid ?? false;
  }

  /// Force re-validation by clearing cache
  Future<void> clearValidationCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_licenseValidationKey);
    await prefs.remove(_lastValidationKey);
    await prefs.remove(_serialNumberKey);

    // Also deactivate licenses in database
    try {
      await _licensesDao.deactivateAllLicenses();
    } catch (e) {
      debugPrint('Error deactivating licenses in database: $e');
    }
  }

  /// Get stored license credentials
  Future<StoredLicenseCredentials?> getStoredCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final serialNumber = prefs.getString(_serialNumberKey);
    
    if (serialNumber != null) {
      return StoredLicenseCredentials(serialNumber: serialNumber);
    }
    
    return null;
  }

  /// Validate using stored credentials
  Future<LicenseValidationResult> validateWithStoredCredentials() async {
    final credentials = await getStoredCredentials();
    
    if (credentials == null) {
      return LicenseValidationResult(
        isValid: false,
        responseCode: 'NO_STORED_CREDENTIALS',
        message: 'No license credentials found',
        timestamp: DateTime.now(),
      );
    }
    
    return validateLicense(
      serialNumber: credentials.serialNumber,
    );
  }

  /// Generate a hardware-specific identifier (public for testing)
  Future<String> generateHardwareId() async {
    return _generateHardwareId();
  }

  /// Test RSA encryption (public for testing)
  Future<String> testEncryption(String payload) async {
    try {
      final encrypted = await _encryptPayload(payload);
      return base64Encode(encrypted);
    } catch (e) {
      return 'ERROR: $e';
    }
  }

  /// Generate a hardware-specific identifier
  Future<String> _generateHardwareId() async {
    final deviceInfo = DeviceInfoPlugin();
    String hwid = 'UNKNOWN_DEVICE';
    
    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        hwid = 'ANDROID_${androidInfo.id}_${androidInfo.model}_${androidInfo.manufacturer}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        hwid = 'IOS_${iosInfo.identifierForVendor}_${iosInfo.model}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        hwid = 'LINUX_${linuxInfo.machineId}_${linuxInfo.name}';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        hwid = 'WINDOWS_${windowsInfo.computerName}_${windowsInfo.numberOfCores}';
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        hwid = 'MACOS_${macInfo.systemGUID}_${macInfo.computerName}';
      }
    } catch (e) {
      debugPrint('Error generating hardware ID: $e');
      // Fallback to a basic identifier
      hwid = 'FALLBACK_${Platform.operatingSystem}_${DateTime.now().millisecondsSinceEpoch}';
    }
    
    // Hash the HWID for privacy and consistent length
    final bytes = utf8.encode(hwid);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16).toUpperCase();
  }

  /// Encrypt payload using RSA with OAEP padding using SHA256 (matching Python cryptography library)
  Future<Uint8List> _encryptPayload(String payload) async {
    try {
      // Parse the PEM public key
      final publicKey = _parsePublicKeyFromPem(_publicKeyPem);

      // Convert payload to bytes
      final payloadBytes = Uint8List.fromList(utf8.encode(payload));

      // Create RSA engine with OAEP padding using SHA256 (same as Python example)
      // Python uses: padding.OAEP(mgf=MGF1(algorithm=SHA256), algorithm=SHA256, label=None)
      // Use SHA256 digest to match Python implementation exactly
      final cipher = OAEPEncoding.withSHA256(RSAEngine());

      // Initialize with public key
      cipher.init(
        true, // forEncryption
        PublicKeyParameter<RSAPublicKey>(publicKey),
      );

      // Encrypt the payload
      final encrypted = cipher.process(payloadBytes);
      return encrypted;
    } catch (e) {
      debugPrint('RSA encryption error: $e');
      rethrow;
    }
  }

  /// Parse RSA public key from PEM format using basic_utils
  RSAPublicKey _parsePublicKeyFromPem(String pemString) {
    try {
      // Use basic_utils to parse the PEM public key
      final rsaPublicKey = CryptoUtils.rsaPublicKeyFromPem(pemString);
      return rsaPublicKey;
    } catch (e) {
      debugPrint('Error parsing RSA public key: $e');
      rethrow;
    }
  }

  /// Parse the validation response from server
  LicenseValidationResult _parseValidationResponse(String? responseCode, Map<String, dynamic> responseData) {
    const successCodes = ['OKAY', 'SUCCESS'];
    final isValid = responseCode != null && successCodes.contains(responseCode.toUpperCase());

    // Parse expiry date from server response
    DateTime? expiryDate;
    try {
      final expiryString = responseData['ExpiryDate'] as String?;
      if (expiryString != null && expiryString.isNotEmpty) {
        // Try different date formats that the server might use
        try {
          // ISO 8601 format: 2024-12-31T23:59:59Z
          expiryDate = DateTime.parse(expiryString);
        } catch (e) {
          try {
            // Date only format: 2024-12-31
            expiryDate = DateTime.parse('${expiryString}T23:59:59Z');
          } catch (e) {
            debugPrint('Failed to parse expiry date: $expiryString');
          }
        }
      }
    } catch (e) {
      debugPrint('Error parsing expiry date from response: $e');
    }

    String message;
    switch (responseCode?.toUpperCase()) {
      case 'OKAY':
      case 'SUCCESS':
        message = 'License validated successfully';
        if (expiryDate != null) {
          final daysUntilExpiry = expiryDate.difference(DateTime.now()).inDays;
          if (daysUntilExpiry <= 0) {
            message = 'License has expired';
          } else if (daysUntilExpiry <= 30) {
            message = 'License validated successfully (expires in $daysUntilExpiry days)';
          }
        }
        break;
      case 'INVALID_LICENSE':
        message = 'Invalid license key';
        break;
      case 'EXPIRED_LICENSE':
        message = 'License has expired';
        break;
      case 'INVALID_HWID':
        message = 'Hardware ID mismatch';
        break;
      case 'QUOTA_EXCEEDED':
        message = 'License usage quota exceeded';
        break;
      default:
        message = 'License validation failed: ${responseCode ?? 'UNKNOWN'}';
    }

    return LicenseValidationResult(
      isValid: isValid,
      responseCode: responseCode ?? 'UNKNOWN',
      message: message,
      timestamp: DateTime.now(),
      serverResponse: responseData,
      expiryDate: expiryDate,
    );
  }

  /// Cache validation result
  Future<void> _cacheValidation(LicenseValidationResult result) async {
    final prefs = await SharedPreferences.getInstance();
    final resultJson = jsonEncode({
      'isValid': result.isValid,
      'responseCode': result.responseCode,
      'message': result.message,
      'timestamp': result.timestamp.toIso8601String(),
      'expiryDate': result.expiryDate?.toIso8601String(),
    });

    await prefs.setString(_licenseValidationKey, resultJson);
    await prefs.setInt(_lastValidationKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// Save license validation result to database
  Future<void> _saveLicenseToDatabase(
    String serialNumber,
    String hardwareId,
    LicenseValidationResult result,
    String? serverUrl,
  ) async {
    try {
      await _licensesDao.saveLicenseValidation(
        serialNumber: serialNumber,
        hardwareId: hardwareId,
        isValid: result.isValid,
        responseCode: result.responseCode,
        message: result.message,
        expiryDate: result.expiryDate,
        serverUrl: serverUrl,
        serverResponse: result.serverResponse != null
            ? jsonEncode(result.serverResponse)
            : null,
      );

      // Clean up old license records to keep database size manageable
      await _licensesDao.cleanupOldLicenses();
    } catch (e) {
      debugPrint('Error saving license to database: $e');
      // Don't throw - database errors shouldn't break license validation
    }
  }

  /// Get cached validation result if still valid
  Future<LicenseValidationResult?> _getCachedValidation() async {
    // First check SharedPreferences cache (faster)
    final prefs = await SharedPreferences.getInstance();
    final lastValidation = prefs.getInt(_lastValidationKey);

    if (lastValidation != null) {
      final lastValidationTime = DateTime.fromMillisecondsSinceEpoch(lastValidation);
      final now = DateTime.now();

      if (now.difference(lastValidationTime) < _validationCacheDuration) {
        final cachedResult = prefs.getString(_licenseValidationKey);
        if (cachedResult != null) {
          try {
            final resultMap = jsonDecode(cachedResult) as Map<String, dynamic>;

            // Parse expiry date from cached result
            DateTime? expiryDate;
            final expiryString = resultMap['expiryDate'] as String?;
            if (expiryString != null && expiryString.isNotEmpty) {
              try {
                expiryDate = DateTime.parse(expiryString);
              } catch (e) {
                debugPrint('Error parsing cached expiry date: $e');
              }
            }

            return LicenseValidationResult(
              isValid: resultMap['isValid'] as bool,
              responseCode: resultMap['responseCode'] as String,
              message: resultMap['message'] as String,
              timestamp: DateTime.parse(resultMap['timestamp'] as String),
              expiryDate: expiryDate,
            );
          } catch (e) {
            debugPrint('Error parsing cached validation result: $e');
          }
        }
      }
    }

    // If no valid SharedPreferences cache, check database
    try {
      final currentLicense = await _licensesDao.getCurrentLicense();
      if (currentLicense != null && currentLicense.isValid) {
        final lastValidationTime = currentLicense.lastValidated;
        final now = DateTime.now();

        // Check if database cache is still valid
        if (now.difference(lastValidationTime) < _validationCacheDuration) {
          return LicenseValidationResult(
            isValid: currentLicense.isValid,
            responseCode: currentLicense.responseCode,
            message: currentLicense.message,
            timestamp: currentLicense.lastValidated,
            expiryDate: currentLicense.expiryDate,
            serverResponse: currentLicense.serverResponse != null
                ? jsonDecode(currentLicense.serverResponse!)
                : null,
          );
        }
      }
    } catch (e) {
      debugPrint('Error reading license from database: $e');
    }

    return null;
  }

  /// Store license credentials securely
  Future<void> _storeLicenseCredentials(String serialNumber) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_serialNumberKey, serialNumber);
  }

  /// Get current license from database
  Future<LicenseData?> getCurrentLicenseFromDatabase() async {
    try {
      return await _licensesDao.getCurrentLicense();
    } catch (e) {
      debugPrint('Error getting current license from database: $e');
      return null;
    }
  }

  /// Get license history from database
  Future<List<LicenseData>> getLicenseHistory() async {
    try {
      return await _licensesDao.getLicenseHistory();
    } catch (e) {
      debugPrint('Error getting license history from database: $e');
      return [];
    }
  }

  /// Check if current license is expired based on database
  Future<bool> isCurrentLicenseExpired() async {
    try {
      return await _licensesDao.isCurrentLicenseExpired();
    } catch (e) {
      debugPrint('Error checking license expiry from database: $e');
      return false;
    }
  }

  /// Get days until expiry from database
  Future<int?> getDaysUntilExpiry() async {
    try {
      return await _licensesDao.getDaysUntilExpiry();
    } catch (e) {
      debugPrint('Error getting days until expiry from database: $e');
      return null;
    }
  }
}

/// Result of license validation
class LicenseValidationResult {
  final bool isValid;
  final String responseCode;
  final String message;
  final DateTime timestamp;
  final String? httpCode;
  final Map<String, dynamic>? serverResponse;
  final DateTime? expiryDate;

  const LicenseValidationResult({
    required this.isValid,
    required this.responseCode,
    required this.message,
    required this.timestamp,
    this.httpCode,
    this.serverResponse,
    this.expiryDate,
  });

  /// Check if license is expired based on expiry date
  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  /// Get days until expiry (negative if expired)
  int? get daysUntilExpiry {
    if (expiryDate == null) return null;
    final now = DateTime.now();
    final difference = expiryDate!.difference(now);
    return difference.inDays;
  }

  /// Check if license is expiring soon (within 30 days)
  bool get isExpiringSoon {
    final days = daysUntilExpiry;
    return days != null && days <= 30 && days > 0;
  }

  @override
  String toString() {
    return 'LicenseValidationResult(isValid: $isValid, responseCode: $responseCode, message: $message, timestamp: $timestamp, httpCode: $httpCode, expiryDate: $expiryDate)';
  }
}

/// Stored license credentials
class StoredLicenseCredentials {
  final String serialNumber;

  const StoredLicenseCredentials({
    required this.serialNumber,
  });
}
