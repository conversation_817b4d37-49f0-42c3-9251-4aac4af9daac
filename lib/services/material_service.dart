import '../providers/configuration_provider.dart';

class MaterialService {
  static final MaterialService _instance = MaterialService._internal();
  factory MaterialService() => _instance;
  MaterialService._internal();

  static MaterialService get instance => _instance;

  final ConfigurationProvider _configProvider = ConfigurationProvider();

  /// Get all material rates from database only (no hard-coded values)
  Future<Map<String, double>> getAllMaterialRates() async {
    final Map<String, double> allMaterials = {};

    // Load all materials from database only
    final materialConfigs = await _configProvider.getConfigValuesByPrefix('material_rate_');

    for (final entry in materialConfigs.entries) {
      // Extract material key from 'material_rate_key' format
      final materialKey = entry.key.replaceFirst('material_rate_', '');

      final rate = double.tryParse(entry.value);
      if (rate != null) {
        allMaterials[materialKey] = rate;
      }
    }

    return allMaterials;
  }

  /// Get all material labels from database (generate labels for all materials)
  Future<Map<String, String>> getAllMaterialLabels() async {
    final Map<String, String> allLabels = {};

    // Load all materials from database
    final materialConfigs = await _configProvider.getConfigValuesByPrefix('material_rate_');

    for (final entry in materialConfigs.entries) {
      // Extract material key from 'material_rate_key' format
      final materialKey = entry.key.replaceFirst('material_rate_', '');

      // Generate a display label from the key
      final label = _generateMaterialLabel(materialKey);
      allLabels[materialKey] = label;
    }

    return allLabels;
  }

  /// Generate a display label for a material key
  String _generateMaterialLabel(String materialKey) {
    // Handle special cases for better display names
    switch (materialKey) {
      case 'acier':
        return 'Acier (Steel)';
      case 'inox':
        return 'Inox (Stainless Steel)';
      case 'cuivre':
        return 'Cuivre (Copper)';
      case 'aluminum':
        return 'Aluminum';
      case 'tole_galvanisee':
        return 'Tôle Galvanisée';
      default:
        // For custom materials, capitalize and replace dashes/underscores with spaces
        return materialKey
            .split(RegExp(r'[-_]'))
            .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
            .join(' ');
    }
  }



  /// Add a new custom material
  Future<bool> addCustomMaterial(String name, double rate) async {
    try {
      final key = name.toLowerCase().replaceAll(' ', '-');
      await _configProvider.setConfigValue('material_rate_$key', rate.toString());
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove a material (both custom and default)
  Future<bool> removeMaterial(String key) async {
    try {
      // Actually delete the configuration value from the database
      await _configProvider.deleteConfigValue('material_rate_$key');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove a custom material (legacy method for backward compatibility)
  Future<bool> removeCustomMaterial(String key) async {
    return await removeMaterial(key);
  }

  /// Update material rate
  Future<bool> updateMaterialRate(String key, double rate) async {
    try {
      await _configProvider.setConfigValue('material_rate_$key', rate.toString());
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if a material is custom (not a default material)
  bool isCustomMaterial(String key) {
    // Define default materials (these were originally in the app)
    const defaultMaterials = {'acier', 'inox', 'cuivre', 'aluminum', 'tole_galvanisee'};
    return !defaultMaterials.contains(key);
  }

  /// Get material rate by key
  Future<double?> getMaterialRate(String key) async {
    // Check database first (for both default and custom materials)
    final savedRate = await _configProvider.getConfigValue('material_rate_$key');
    if (savedRate != null && savedRate.isNotEmpty) {
      return double.tryParse(savedRate);
    }

    return null;
  }

  /// Get material display label by key
  Future<String> getMaterialLabel(String key) async {
    // Generate label using the same logic as getAllMaterialLabels
    return _generateMaterialLabel(key);
  }
}
