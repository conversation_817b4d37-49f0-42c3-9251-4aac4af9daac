import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

/// Direct Flutter/Dart equivalent of the Python authentication function
class PythonEquivalentAuth {
  /// RSA Public Key (same as Python example)
  static const String publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArpazceA9fb4RRjtAF41V
dlMlaKXOA77OjF4GAnOIWlZgIiL/INeVigAK2aiU7vNbSZZrIH26DghamvBkRpCv
/Yj4gsx87EAdQNdDMqWE0mGuEaZITlGESKTeUoQYefwyctuHpWiQdmziY0Dw5IjR
ZySLpGHePHWEp4+Z4BTtTbGT4O+DbfMkxRuD1TNoHnaEOKxZki3UWFTYbOiyO5HT
C0OUYHy5AcoGACv9LMhBfYmAgeWPDYS019wLiyyz9jZIUnq0IfBGgt5VpvD+TO/R
rN81gYXMw9K4FlgY9upK9aTtN6hc176RqkoEahRWDqkcE5ZFH11Wcbor5i2dNozB
zQIDAQAB 
-----END PUBLIC KEY-----''';

  /// API Key (same as Python example)
  static const String apiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';

  /// Request path (same as Python example)
  static const String requestPath = 'http://localhost:8000/api/v1/validate';

  /// Direct equivalent of Python authentication function
  /// 
  /// ```python
  /// def authentication(public_key, api_key, serial, hwid):
  ///     plaintexts = bytes(serial + ':' + hwid, 'utf-8')
  ///     if isinstance(public_key, rsa.RSAPublicKey):
  ///         payload = public_key.encrypt(
  ///             plaintexts,
  ///             padding.OAEP(
  ///                 mgf=padding.MGF1(algorithm=hashes.SHA256()),
  ///                 algorithm=hashes.SHA256(),
  ///                 label=None
  ///             )
  ///         )
  ///         final_payload = base64.b64encode(payload).decode('utf-8')
  ///         server_request = requests.post(request_path, json={
  ///             "apiKey": api_key, "payload": final_payload
  ///         }, timeout=10)
  ///         response_code = server_request.json()['Code']
  ///         success_codes=['OKAY', 'SUCCESS']
  ///         if any(response_code in i for i in success_codes):
  ///             print(f"Authentication: {response_code}")
  ///             return True
  ///         print(f"Error: {response_code}")
  ///         return False
  /// ```
  static Future<bool> authentication(
    RSAPublicKey publicKey,
    String apiKey,
    String serial,
    String hwid,
  ) async {
    try {
      // Python: plaintexts = bytes(serial + ':' + hwid, 'utf-8')
      final plaintexts = '$serial:$hwid';
      final plaintextBytes = Uint8List.fromList(utf8.encode(plaintexts));

      // Python: payload = public_key.encrypt(plaintexts, padding.OAEP(...))
      // Create RSA engine with OAEP padding using SHA256 (same as Python)
      final cipher = OAEPEncoding.withSHA256(RSAEngine());
      cipher.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
      final payload = cipher.process(plaintextBytes);

      // Python: final_payload = base64.b64encode(payload).decode('utf-8')
      final finalPayload = base64Encode(payload);

      // Python: server_request = requests.post(request_path, json={...}, timeout=10)
      final serverRequest = await http.post(
        Uri.parse(requestPath),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'apiKey': apiKey,
          'payload': finalPayload,
        }),
      ).timeout(const Duration(seconds: 10));

      // Python: response_code = server_request.json()['Code']
      final responseData = jsonDecode(serverRequest.body) as Map<String, dynamic>;
      final responseCode = responseData['Code'] as String?;

      // Python: success_codes=['OKAY', 'SUCCESS']
      const successCodes = ['OKAY', 'SUCCESS'];

      // Python: if any(response_code in i for i in success_codes):
      if (responseCode != null && successCodes.contains(responseCode)) {
        // Python: print(f"Authentication: {response_code}")
        print('Authentication: $responseCode');
        return true;
      }

      // Python: print(f"Error: {response_code}")
      print('Error: $responseCode');
      return false;
    } catch (e) {
      print('Error: $e');
      return false;
    }
  }

  /// Parse RSA public key from PEM format (helper function)
  static RSAPublicKey parsePublicKeyFromPem(String pemString) {
    return CryptoUtils.rsaPublicKeyFromPem(pemString);
  }

  /// Main function equivalent to Python's if __name__ == "__main__":
  static Future<void> main() async {
    // Python: PUBLIC_KEY = serialization.load_pem_public_key(str.encode(PUB_KEY))
    final publicKey = parsePublicKeyFromPem(publicKeyPem);

    // Python: API_KEY = 'a080015b-b827-48f8-a96d-dc3ccc650bc8'
    const apiKeyValue = apiKey;

    // Python: SERIAL = 'O4T1K-RW2I9-HDAZY-K98BQ'
    const serial = 'IJTK3-W2LZM-XHNYU-O1TK3';

    // Python: HWID = 'MEHDI'  # Deterministic UID
    const hwid = 'MEHDI'; // Deterministic UID

    // Python: authentication(PUBLIC_KEY, API_KEY, SERIAL, HWID)
    await authentication(publicKey, apiKeyValue, serial, hwid);
  }
}
