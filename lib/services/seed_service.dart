import 'dart:math';
import 'package:drift/drift.dart';
import '../models/client.dart';
import '../models/calculation.dart';
import '../services/database_service.dart';
import '../database/daos.dart';
import '../database/database.dart';

class SeedService {
  static final SeedService _instance = SeedService._internal();
  factory SeedService() => _instance;

  static SeedService get instance => _instance;

  final DatabaseService _databaseService = DatabaseService.instance;
  late final MaterialsDao _materialsDao;
  late final GasTypesDao _gasTypesDao;
  late final ThicknessRangesDao _thicknessRangesDao;
  late final GasCostsDao _gasCostsDao;
  late final AppSettingsDao _appSettingsDao;

  SeedService._internal() {
    final database = DatabaseService.instance.database;
    _materialsDao = MaterialsDao(database);
    _gasTypesDao = GasTypesDao(database);
    _thicknessRangesDao = ThicknessRangesDao(database);
    _gasCostsDao = GasCostsDao(database);
    _appSettingsDao = AppSettingsDao(database);
  }

  /// Seed the database with initial data
  Future<void> seedDatabase({bool force = false}) async {
    // Check if database has already been seeded
    if (!force) {
      final seeded = await _appSettingsDao.getSettingValue('system', 'database_seeded');
      if (seeded == 'true') {
        print('Database already seeded. Use force: true to re-seed.');
        return;
      }
    }

    print('Seeding database with initial data...');

    try {
      // Clear existing data if force seeding
      if (force) {
        await _clearAllData();
      }

      // Seed thickness ranges first (needed for gas costs)
      await _seedThicknessRanges();
      print('✓ Thickness ranges seeded');

      // Seed materials
      await _seedMaterials();
      print('✓ Materials seeded');

      // Seed gas types
      await _seedGasTypes();
      print('✓ Gas types seeded');

      // Seed gas costs (depends on gas types and thickness ranges)
      await _seedGasCosts();
      print('✓ Gas costs seeded');

      // Seed configuration
      await _seedConfiguration();
      print('✓ Configuration seeded');

      // Seed sample clients
      await _seedClients();
      print('✓ Sample clients seeded');

      // Seed sample calculations
      await _seedCalculations();
      print('✓ Sample calculations seeded');

      // Mark database as seeded
      await _appSettingsDao.upsertSetting('system', 'database_seeded', 'true', 'string');
      await _appSettingsDao.upsertSetting('system', 'database_seed_date', DateTime.now().toIso8601String(), 'string');

      print('Database seeding completed successfully!');
    } catch (e) {
      print('Error seeding database: $e');
      rethrow;
    }
  }

  /// Clear all existing data
  Future<void> _clearAllData() async {
    await _databaseService.clearAllData();

    // Clear all app settings
    final allSettings = await _appSettingsDao.getAllSettings();
    for (final setting in allSettings) {
      await _appSettingsDao.deleteSetting(setting.id);
    }
  }

  /// Seed thickness ranges
  Future<void> _seedThicknessRanges() async {
    // Check if thickness ranges already exist
    final existingRanges = await _thicknessRangesDao.getAllThicknessRanges();
    if (existingRanges.isNotEmpty) {
      return; // Already seeded
    }

    final thicknessRanges = [
      {'name': '1_5_mm', 'displayName': '1-5 mm', 'minThickness': 1.0, 'maxThickness': 5.0, 'sortOrder': 1},
      {'name': '5_10_mm', 'displayName': '5-10 mm', 'minThickness': 5.0, 'maxThickness': 10.0, 'sortOrder': 2},
      {'name': '10_15_mm', 'displayName': '10-15 mm', 'minThickness': 10.0, 'maxThickness': 15.0, 'sortOrder': 3},
      {'name': 'gt_15_mm', 'displayName': '> 15 mm', 'minThickness': 15.0, 'maxThickness': 999.0, 'sortOrder': 4},
    ];

    for (final range in thicknessRanges) {
      await _thicknessRangesDao.insertThicknessRange(ThicknessRangesCompanion(
        name: Value(range['name'] as String),
        displayName: Value(range['displayName'] as String),
        minThickness: Value(range['minThickness'] as double),
        maxThickness: Value(range['maxThickness'] as double),
        sortOrder: Value(range['sortOrder'] as int),
        updatedAt: Value(DateTime.now()),
      ));
    }
  }

  /// Seed materials with default rates
  Future<void> _seedMaterials() async {
    // Check if materials already exist
    final existingMaterials = await _materialsDao.getAllMaterials();
    if (existingMaterials.isNotEmpty) {
      return; // Already seeded
    }

    final materials = [
      {'name': 'acier', 'displayName': 'Acier', 'rate': 25.0, 'isDefault': true},
      {'name': 'inox', 'displayName': 'Inox', 'rate': 35.0, 'isDefault': false},
      {'name': 'cuivre', 'displayName': 'Cuivre', 'rate': 45.0, 'isDefault': false},
      {'name': 'aluminum', 'displayName': 'Aluminum', 'rate': 30.0, 'isDefault': false},
      {'name': 'tole_galvanisee', 'displayName': 'Tôle Galvanisée', 'rate': 22.0, 'isDefault': false},
    ];

    for (final material in materials) {
      await _materialsDao.insertMaterial(MaterialsCompanion(
        name: Value(material['name'] as String),
        displayName: Value(material['displayName'] as String),
        rate: Value(material['rate'] as double),
        isDefault: Value(material['isDefault'] as bool),
        updatedAt: Value(DateTime.now()),
      ));
    }
  }

  /// Seed gas types
  Future<void> _seedGasTypes() async {
    // Check if gas types already exist
    final existingGasTypes = await _gasTypesDao.getAllGasTypes();
    if (existingGasTypes.isNotEmpty) {
      return; // Already seeded
    }

    final gasTypes = [
      {'name': 'oxygene_o2', 'displayName': 'Oxygène (O₂)', 'isDefault': true},
      {'name': 'azote_n2', 'displayName': 'Azote (N₂)', 'isDefault': false},
      {'name': 'air_comprime', 'displayName': 'Air Comprimé', 'isDefault': false},
    ];

    for (final gasType in gasTypes) {
      await _gasTypesDao.insertGasType(GasTypesCompanion(
        name: Value(gasType['name'] as String),
        displayName: Value(gasType['displayName'] as String),
        isDefault: Value(gasType['isDefault'] as bool),
        updatedAt: Value(DateTime.now()),
      ));
    }
  }

  /// Seed gas costs (depends on gas types and thickness ranges being seeded first)
  Future<void> _seedGasCosts() async {
    // Check if gas costs already exist
    final existingGasCosts = await _gasCostsDao.getAllGasCostsWithDetails();
    if (existingGasCosts.isNotEmpty) {
      return; // Already seeded
    }

    final gasCostData = {
      'oxygene_o2': {
        '1_5_mm': 8.0,
        '5_10_mm': 12.0,
        '10_15_mm': 18.0,
        'gt_15_mm': 25.0,
      },
      'azote_n2': {
        '1_5_mm': 6.0,
        '5_10_mm': 9.0,
        '10_15_mm': 14.0,
        'gt_15_mm': 20.0,
      },
      'air_comprime': {
        '1_5_mm': 4.0,
        '5_10_mm': 6.0,
        '10_15_mm': 9.0,
        'gt_15_mm': 12.0,
      },
    };

    for (final gasEntry in gasCostData.entries) {
      final gasType = await _gasTypesDao.getGasTypeByName(gasEntry.key);
      if (gasType == null) continue;

      for (final thicknessEntry in gasEntry.value.entries) {
        final thicknessRange = await _thicknessRangesDao.getThicknessRangeByName(thicknessEntry.key);
        if (thicknessRange == null) continue;

        await _gasCostsDao.insertGasCost(GasCostsCompanion(
          gasTypeId: Value(gasType.id),
          thicknessRangeId: Value(thicknessRange.id),
          costPerHour: Value(thicknessEntry.value),
          updatedAt: Value(DateTime.now()),
        ));
      }
    }
  }

  /// Seed application configuration
  Future<void> _seedConfiguration() async {
    // Pricing settings
    await _appSettingsDao.upsertSetting('pricing', 'design_service_price', '50.0', 'double', description: 'Design service price');
    await _appSettingsDao.upsertSetting('pricing', 'price_per_meter', '15.0', 'double', description: 'Price per meter');
    await _appSettingsDao.upsertSetting('pricing', 'tax_rate', '20.0', 'double', description: 'Tax rate percentage');
    await _appSettingsDao.upsertSetting('pricing', 'min_order_amount', '25.0', 'double', description: 'Minimum order amount');
    await _appSettingsDao.upsertSetting('pricing', 'default_setup_fees', '0.0', 'double', description: 'Default setup fees');

    // Default values
    await _appSettingsDao.upsertSetting('defaults', 'gas_type', 'Oxygène (O₂)', 'string', description: 'Default gas type');
    await _appSettingsDao.upsertSetting('defaults', 'material', 'acier', 'string', description: 'Default material');
    await _appSettingsDao.upsertSetting('defaults', 'thickness', '5.0', 'double', description: 'Default thickness');
    await _appSettingsDao.upsertSetting('defaults', 'cutting_speed', '100.0', 'double', description: 'Default cutting speed');

    // Company information
    await _appSettingsDao.upsertSetting('company', 'name', 'Laser Cutting Co.', 'string', description: 'Company name');
    await _appSettingsDao.upsertSetting('company', 'address', '123 Industrial Street', 'string', description: 'Company address');
    await _appSettingsDao.upsertSetting('company', 'phone', '******-0123', 'string', description: 'Company phone');
    await _appSettingsDao.upsertSetting('company', 'email', '<EMAIL>', 'string', description: 'Company email');

    // System settings
    await _appSettingsDao.upsertSetting('system', 'app_version', '1.0.0', 'string', description: 'Application version');
    await _appSettingsDao.upsertSetting('system', 'currency', 'EUR', 'string', description: 'Currency code');
  }

  /// Seed sample clients
  Future<void> _seedClients() async {
    final clients = [
      Client(
        id: 'client_001',
        name: 'Jean Dupont',
        reference: 'REF-001',
        company: 'Métallurgie Dupont',
        email: '<EMAIL>',
        phone: '+33 1 23 45 67 89',
        projects: 12,
        totalSpent: 3450.75,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
      ),
      Client(
        id: 'client_002',
        name: 'Marie Martin',
        reference: 'REF-002',
        company: 'Atelier Martin & Fils',
        email: '<EMAIL>',
        phone: '+33 2 34 56 78 90',
        projects: 8,
        totalSpent: 2180.50,
        createdAt: DateTime.now().subtract(const Duration(days: 150)),
      ),
      Client(
        id: 'client_003',
        name: 'Pierre Dubois',
        reference: 'REF-003',
        company: 'Industries Dubois',
        email: '<EMAIL>',
        phone: '+33 3 45 67 89 01',
        projects: 15,
        totalSpent: 5670.25,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
      ),
      Client(
        id: 'client_004',
        name: 'Sophie Bernard',
        reference: 'REF-004',
        company: 'Précision Bernard',
        email: '<EMAIL>',
        phone: '+33 4 56 78 90 12',
        projects: 6,
        totalSpent: 1890.00,
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
      Client(
        id: 'client_005',
        name: 'Antoine Rousseau',
        reference: 'REF-005',
        company: null,
        email: '<EMAIL>',
        phone: '+33 5 67 89 01 23',
        projects: 3,
        totalSpent: 675.30,
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
      ),
    ];

    for (final client in clients) {
      await _databaseService.insertClient(client);
    }
  }

  /// Seed sample calculations
  Future<void> _seedCalculations() async {
    final random = Random();
    final materials = ['acier', 'inox', 'cuivre', 'aluminum', 'tole_galvanisee'];
    final gasTypes = ['Oxygène (O₂)', 'Azote (N₂)', 'Air Comprimé'];
    final clients = [
      {'name': 'Jean Dupont', 'ref': 'REF-001'},
      {'name': 'Marie Martin', 'ref': 'REF-002'},
      {'name': 'Pierre Dubois', 'ref': 'REF-003'},
      {'name': 'Sophie Bernard', 'ref': 'REF-004'},
      {'name': 'Antoine Rousseau', 'ref': 'REF-005'},
    ];

    final calculations = <Calculation>[];

    // Generate 25 sample calculations
    for (int i = 1; i <= 25; i++) {
      final client = clients[random.nextInt(clients.length)];
      final material = materials[random.nextInt(materials.length)];
      final gasType = gasTypes[random.nextInt(gasTypes.length)];
      final thickness = [1.0, 2.0, 3.0, 5.0, 8.0, 10.0, 12.0, 15.0, 20.0][random.nextInt(9)];
      final linearMeters = 5.0 + random.nextDouble() * 50.0; // 5-55 meters
      final cuttingSpeed = 80.0 + random.nextDouble() * 40.0; // 80-120 speed
      final designProvided = random.nextBool();
      final totalPrice = _calculatePrice(material, thickness, gasType, linearMeters, designProvided);
      
      final status = CalculationStatus.values[random.nextInt(CalculationStatus.values.length)];
      final daysAgo = random.nextInt(365);

      calculations.add(Calculation(
        id: 'calc_${i.toString().padLeft(3, '0')}',
        clientName: client['name']!,
        clientReference: client['ref']!,
        material: material,
        thickness: thickness,
        gasType: gasType,
        linearMeters: double.parse(linearMeters.toStringAsFixed(2)),
        cuttingSpeed: double.parse(cuttingSpeed.toStringAsFixed(1)),
        designProvided: designProvided,
        designReference: designProvided ? 'DWG-${i.toString().padLeft(3, '0')}' : null,
        totalPrice: double.parse(totalPrice.toStringAsFixed(2)),
        createdAt: DateTime.now().subtract(Duration(days: daysAgo)),
        status: status,
        // Add required new fields with default values
        cuttingDurationHours: linearMeters / cuttingSpeed * 60, // Estimate based on linear meters and speed
        setupFees: random.nextDouble() * 100, // Random setup fees 0-100 DH
        machineCost: totalPrice * 0.6, // Estimate 60% of total as machine cost
        gasCost: totalPrice * 0.2, // Estimate 20% of total as gas cost
        designCost: designProvided ? 0.0 : totalPrice * 0.2, // Estimate 20% if design not provided
      ));
    }

    for (final calculation in calculations) {
      await _databaseService.insertCalculation(calculation);
    }
  }

  /// Calculate price for a calculation (simplified pricing logic)
  double _calculatePrice(String material, double thickness, String gasType, double linearMeters, bool designProvided) {
    // Base material rates
    final materialRates = {
      'acier': 25.0,
      'inox': 35.0,
      'cuivre': 45.0,
      'aluminum': 30.0,
      'tole_galvanisee': 22.0,
    };

    // Gas costs based on thickness
    final gasCosts = {
      'Oxygène (O₂)': thickness <= 5 ? 8.0 : thickness <= 10 ? 12.0 : thickness <= 15 ? 18.0 : 25.0,
      'Azote (N₂)': thickness <= 5 ? 6.0 : thickness <= 10 ? 9.0 : thickness <= 15 ? 14.0 : 20.0,
      'Air Comprimé': thickness <= 5 ? 4.0 : thickness <= 10 ? 6.0 : thickness <= 15 ? 9.0 : 12.0,
    };

    final materialRate = materialRates[material] ?? 25.0;
    final gasCost = gasCosts[gasType] ?? 8.0;
    final designCost = designProvided ? 50.0 : 0.0;
    final pricePerMeter = 15.0;

    // Calculate total price
    final materialCost = materialRate * (thickness / 10.0); // Adjust for thickness
    final totalCostPerMeter = materialCost + gasCost + pricePerMeter;
    final totalPrice = (totalCostPerMeter * linearMeters) + designCost;

    return totalPrice;
  }

  /// Reset database to initial state
  Future<void> resetToInitialState() async {
    await seedDatabase(force: true);
  }

  /// Check if database has been seeded
  Future<bool> isDatabaseSeeded() async {
    final seeded = await _appSettingsDao.getSettingValue('system', 'database_seeded');
    return seeded == 'true';
  }

  /// Get seed information
  Future<Map<String, String?>> getSeedInfo() async {
    return {
      'seeded': await _appSettingsDao.getSettingValue('system', 'database_seeded'),
      'seed_date': await _appSettingsDao.getSettingValue('system', 'database_seed_date'),
      'app_version': await _appSettingsDao.getSettingValue('system', 'app_version'),
    };
  }
}
