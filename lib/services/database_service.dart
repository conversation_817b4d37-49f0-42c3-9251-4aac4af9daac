import 'dart:convert';
import '../database/database.dart';
import '../models/client.dart';
import '../models/calculation.dart';

class DatabaseService {
  static DatabaseService? _instance;
  static AppDatabase? _database;
  static bool _isTestMode = false;

  DatabaseService._internal();

  static DatabaseService get instance {
    _instance ??= DatabaseService._internal();
    return _instance!;
  }

  static void setTestMode(bool testMode) {
    _isTestMode = testMode;
    _database?.close();
    _database = null;
  }

  AppDatabase get database {
    _database ??= _isTestMode ? AppDatabase.forTesting() : AppDatabase();
    return _database!;
  }

  // Client operations
  Future<List<Client>> getAllClients() async {
    return await database.getAllClients();
  }

  Future<Client?> getClientById(String id) async {
    return await database.getClientById(id);
  }

  Future<void> insertClient(Client client) async {
    await database.insertClient(client);
  }

  Future<void> updateClient(Client client) async {
    await database.updateClient(client);
  }

  Future<void> deleteClient(String id) async {
    await database.deleteClient(id);
  }

  Future<List<Client>> searchClients(String query) async {
    final allClients = await getAllClients();
    if (query.isEmpty) return allClients;

    final lowerQuery = query.toLowerCase();
    return allClients.where((client) {
      return client.name.toLowerCase().contains(lowerQuery) ||
          client.reference.toLowerCase().contains(lowerQuery) ||
          (client.company?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  // Calculation operations
  Future<List<Calculation>> getAllCalculations() async {
    return await database.getAllCalculations();
  }

  Future<Calculation?> getCalculationById(String id) async {
    return await database.getCalculationById(id);
  }

  Future<void> insertCalculation(Calculation calculation) async {
    await database.insertCalculation(calculation);
  }

  Future<void> updateCalculation(Calculation calculation) async {
    await database.updateCalculation(calculation);
  }

  Future<void> deleteCalculation(String id) async {
    await database.deleteCalculation(id);
  }

  Future<List<Calculation>> searchCalculations(String query) async {
    final allCalculations = await getAllCalculations();
    if (query.isEmpty) return allCalculations;

    final lowerQuery = query.toLowerCase();
    return allCalculations.where((calc) {
      return calc.clientName.toLowerCase().contains(lowerQuery) ||
          calc.clientReference.toLowerCase().contains(lowerQuery) ||
          calc.id.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  Future<List<Calculation>> getCalculationsByStatus(CalculationStatus status) async {
    final allCalculations = await getAllCalculations();
    return allCalculations.where((calc) => calc.status == status).toList();
  }

  // Configuration operations
  Future<String?> getConfigValue(String key) async {
    return await database.getConfigValue(key);
  }

  Future<void> setConfigValue(String key, String value) async {
    await database.setConfigValue(key, value);
  }

  Future<Map<String, String>> getConfigValuesByPrefix(String prefix) async {
    return await database.getConfigValuesByPrefix(prefix);
  }

  // Generic CRUD operations for any data type

  /// Create or update any data as JSON in the configuration table
  Future<void> createData(String key, Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    await setConfigValue(key, jsonString);
  }

  /// Read data by key and return as Map
  Future<Map<String, dynamic>?> readData(String key) async {
    final jsonString = await getConfigValue(key);
    if (jsonString == null || jsonString.isEmpty) return null;

    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      // If it's not JSON, return as simple string value
      return {'value': jsonString};
    }
  }

  /// Update existing data
  Future<void> updateData(String key, Map<String, dynamic> data) async {
    await createData(key, data); // Same as create for key-value store
  }

  /// Delete data by key
  Future<void> deleteData(String key) async {
    await database.deleteConfigValue(key);
  }

  /// Delete configuration value by key
  Future<void> deleteConfigValue(String key) async {
    await database.deleteConfigValue(key);
  }

  /// List all data with a specific prefix
  Future<Map<String, Map<String, dynamic>>> listDataByPrefix(String prefix) async {
    final configValues = await getConfigValuesByPrefix(prefix);
    final Map<String, Map<String, dynamic>> result = {};

    for (final entry in configValues.entries) {
      try {
        final data = jsonDecode(entry.value) as Map<String, dynamic>;
        result[entry.key] = data;
      } catch (e) {
        // If it's not JSON, treat as simple string value
        result[entry.key] = {'value': entry.value};
      }
    }

    return result;
  }

  /// Search data by prefix and filter function
  Future<Map<String, Map<String, dynamic>>> searchData(
    String prefix,
    bool Function(String key, Map<String, dynamic> data) filter
  ) async {
    final allData = await listDataByPrefix(prefix);
    final Map<String, Map<String, dynamic>> result = {};

    for (final entry in allData.entries) {
      if (filter(entry.key, entry.value)) {
        result[entry.key] = entry.value;
      }
    }

    return result;
  }

  /// Batch operations
  Future<void> batchCreate(Map<String, Map<String, dynamic>> dataMap) async {
    for (final entry in dataMap.entries) {
      await createData(entry.key, entry.value);
    }
  }

  Future<void> batchDelete(List<String> keys) async {
    for (final key in keys) {
      await deleteData(key);
    }
  }

  // Statistics and analytics
  Future<Map<String, dynamic>> getStatistics() async {
    final calculations = await getAllCalculations();
    final clients = await getAllClients();

    final completedCalculations = calculations.where((c) => c.status == CalculationStatus.completed).toList();
    final totalRevenue = completedCalculations.fold<double>(0.0, (sum, calc) => sum + calc.totalPrice);

    return {
      'totalClients': clients.length,
      'totalCalculations': calculations.length,
      'completedCalculations': completedCalculations.length,
      'pendingCalculations': calculations.where((c) => c.status == CalculationStatus.pending).length,
      'cancelledCalculations': calculations.where((c) => c.status == CalculationStatus.cancelled).length,
      'totalRevenue': totalRevenue,
      'averageOrderValue': completedCalculations.isNotEmpty ? totalRevenue / completedCalculations.length : 0.0,
    };
  }

  // Database maintenance
  Future<void> close() async {
    await _database?.close();
    _database = null;
  }

  Future<void> clearAllData() async {
    // Delete all calculations
    final calculations = await getAllCalculations();
    for (final calc in calculations) {
      await deleteCalculation(calc.id);
    }

    // Delete all clients
    final clients = await getAllClients();
    for (final client in clients) {
      await deleteClient(client.id);
    }
  }
}