import '../providers/configuration_provider.dart';

class GasService {
  static final GasService _instance = GasService._internal();
  factory GasService() => _instance;
  GasService._internal();

  static GasService get instance => _instance;

  final ConfigurationProvider _configProvider = ConfigurationProvider();

  /// Get all gas types (default + custom) with their thickness-based costs
  Future<Map<String, Map<String, double>>> getAllGasTypes() async {
    final Map<String, Map<String, double>> allGasTypes = {};

    // Load from database only - no hard-coded defaults

    // Load all gas configurations from database
    final gasConfigs = await _configProvider.getConfigValuesByPrefix('gas_cost_');

    // Group by gas type
    final Map<String, Map<String, double>> gasTypeData = {};

    for (final entry in gasConfigs.entries) {
      // Parse keys like 'gas_cost_oxygene_o2_1_5_mm' or 'gas_cost_argon_5_10_mm'
      final keyWithoutPrefix = entry.key.replaceFirst('gas_cost_', '');

      // Try to match known range patterns
      String? gasKey;
      String? rangeKey;

      if (keyWithoutPrefix.endsWith('_1_5_mm')) {
        gasKey = keyWithoutPrefix.replaceAll('_1_5_mm', '');
        rangeKey = '1_5_mm';
      } else if (keyWithoutPrefix.endsWith('_5_10_mm')) {
        gasKey = keyWithoutPrefix.replaceAll('_5_10_mm', '');
        rangeKey = '5_10_mm';
      } else if (keyWithoutPrefix.endsWith('_10_15_mm')) {
        gasKey = keyWithoutPrefix.replaceAll('_10_15_mm', '');
        rangeKey = '10_15_mm';
      } else if (keyWithoutPrefix.endsWith('_gt_15_mm')) {
        gasKey = keyWithoutPrefix.replaceAll('_gt_15_mm', '');
        rangeKey = 'gt_15_mm';
      }

      if (gasKey != null && rangeKey != null) {
        // Convert back to display format
        final gasDisplayName = _convertKeyToDisplayName(gasKey);
        final rangeDisplayName = _convertRangeKeyToDisplayName(rangeKey);

        // Parse the cost value
        final cost = double.tryParse(entry.value);
        if (cost != null) {
          gasTypeData[gasDisplayName] ??= {};
          gasTypeData[gasDisplayName]![rangeDisplayName] = cost;
        }
      }
    }

    // Only include gas types that have complete data (all 4 thickness ranges)
    for (final entry in gasTypeData.entries) {
      if (entry.value.isNotEmpty && entry.value.length == 4) {
        allGasTypes[entry.key] = entry.value;
      }
    }

    return allGasTypes;
  }

  /// Get all gas type labels from database
  Future<Map<String, String>> getAllGasLabels() async {
    final Map<String, String> allLabels = {};

    // Load all gas types from database
    final allGasTypes = await getAllGasTypes();

    for (final gasName in allGasTypes.keys) {
      // Use the gas name as the label (they're already in display format)
      allLabels[gasName] = gasName;
    }

    return allLabels;
  }

  /// Add a new custom gas type with default thickness ranges
  Future<bool> addCustomGasType(String gasName) async {
    try {
      final gasKey = _convertDisplayNameToKey(gasName);
      
      // Add all thickness ranges with default 0.0 values
      final ranges = ['1_5_mm', '5_10_mm', '10_15_mm', 'gt_15_mm'];
      
      for (final range in ranges) {
        await _configProvider.setConfigValue('gas_cost_${gasKey}_$range', '0.0');
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove a gas type (both custom and default)
  Future<bool> removeGasType(String gasName) async {
    try {
      final gasKey = _convertDisplayNameToKey(gasName);

      // Actually delete all thickness range configurations from the database
      final ranges = ['1_5_mm', '5_10_mm', '10_15_mm', 'gt_15_mm'];

      for (final range in ranges) {
        await _configProvider.deleteConfigValue('gas_cost_${gasKey}_$range');
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Remove a custom gas type (legacy method for backward compatibility)
  Future<bool> removeCustomGasType(String gasName) async {
    return await removeGasType(gasName);
  }

  /// Update gas cost for specific thickness range
  Future<bool> updateGasCost(String gasName, String thicknessRange, double cost) async {
    try {
      final gasKey = _convertDisplayNameToKey(gasName);
      final rangeKey = _convertDisplayNameToRangeKey(thicknessRange);
      
      await _configProvider.setConfigValue('gas_cost_${gasKey}_$rangeKey', cost.toString());
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if a gas type is custom (not a default gas type)
  bool isCustomGasType(String gasName) {
    // Define default gas types (these were originally in the app)
    const defaultGasTypes = {'Oxygène (O₂)', 'Azote (N₂)', 'Air Comprimé'};
    return !defaultGasTypes.contains(gasName);
  }

  /// Get gas cost for specific gas type and thickness range
  Future<double?> getGasCost(String gasName, String thicknessRange) async {
    final gasKey = _convertDisplayNameToKey(gasName);
    final rangeKey = _convertDisplayNameToRangeKey(thicknessRange);
    
    // Check database first
    final savedCost = await _configProvider.getConfigValue('gas_cost_${gasKey}_$rangeKey');
    if (savedCost != null && savedCost.isNotEmpty) {
      return double.tryParse(savedCost);
    }
    
    return null;
  }

  /// Convert display name to database key format
  String _convertDisplayNameToKey(String displayName) {
    return displayName.toLowerCase()
        .replaceAll(' ', '_')
        .replaceAll('(', '')
        .replaceAll(')', '')
        .replaceAll('₂', '2')
        .replaceAll('é', 'e')
        .replaceAll('è', 'e')
        .replaceAll('à', 'a')
        .replaceAll('ç', 'c');
  }

  /// Convert database key back to display name format
  String _convertKeyToDisplayName(String key) {
    // This is a simplified conversion - for complex gas names,
    // we might need a more sophisticated mapping
    if (key == 'oxygene_o2') return 'Oxygène (O₂)';
    if (key == 'azote_n2') return 'Azote (N₂)';
    
    // For custom gas types, capitalize and replace underscores
    return key.split('_').map((word) => 
      word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }

  /// Convert thickness range display name to database key format
  String _convertDisplayNameToRangeKey(String displayRange) {
    if (displayRange == '1-5 mm') return '1_5_mm';
    if (displayRange == '5-10 mm') return '5_10_mm';
    if (displayRange == '10-15 mm') return '10_15_mm';
    if (displayRange == '> 15 mm') return 'gt_15_mm';

    // Fallback for custom ranges
    return displayRange.toLowerCase()
        .replaceAll(' ', '_')
        .replaceAll('-', '_')
        .replaceAll('>', 'gt');
  }

  /// Convert range database key back to display format
  String _convertRangeKeyToDisplayName(String rangeKey) {
    if (rangeKey == '1_5_mm') return '1-5 mm';
    if (rangeKey == '5_10_mm') return '5-10 mm';
    if (rangeKey == '10_15_mm') return '10-15 mm';
    if (rangeKey == 'gt_15_mm') return '> 15 mm';
    
    // For custom ranges, convert back
    return rangeKey.replaceAll('_', ' ').replaceAll('gt', '>');
  }
}
