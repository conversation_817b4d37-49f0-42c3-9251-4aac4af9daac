import '../database/daos.dart';
import '../services/database_service.dart';

/// Comprehensive pricing calculation service implementing the formula:
/// total_price = machine_cost + gas_cost + setup_fees + design_cost
class PricingFormulaService {
  static final PricingFormulaService _instance = PricingFormulaService._internal();
  factory PricingFormulaService() => _instance;

  static PricingFormulaService get instance => _instance;

  late final MaterialsDao _materialsDao;
  late final GasCostsDao _gasCostsDao;
  late final AppSettingsDao _appSettingsDao;

  PricingFormulaService._internal() {
    final database = DatabaseService.instance.database;
    _materialsDao = MaterialsDao(database);
    _gasCostsDao = GasCostsDao(database);
    _appSettingsDao = AppSettingsDao(database);
  }

  /// Calculate total cutting price with detailed breakdown
  Future<PricingBreakdown> calculateTotalPrice({
    required String material,
    required double cuttingDurationHours,
    required String gasType,
    required double thickness,
    required bool isDesignFeesIncluded,
    double? customDesignCost,
    double? customSetupFees,
  }) async {
    // 1. Calculate Machine Cost
    final machineCost = await _calculateMachineCost(
      material: material,
      cuttingDurationHours: cuttingDurationHours,
    );

    // 2. Calculate Gas Cost
    final gasCost = await _calculateGasCost(
      gasType: gasType,
      thickness: thickness,
      cuttingDurationHours: cuttingDurationHours,
    );

    // 3. Calculate Setup Fees
    final setupFees = await _calculateSetupFees(customSetupFees);

    // 4. Calculate Design Cost
    final designCost = await _calculateDesignCost(
      isDesignFeesIncluded: isDesignFeesIncluded,
      customDesignCost: customDesignCost,
    );

    // 5. Calculate Total
    final totalPrice = machineCost + gasCost + setupFees + designCost;

    return PricingBreakdown(
      machineCost: machineCost,
      gasCost: gasCost,
      setupFees: setupFees,
      designCost: designCost,
      totalPrice: totalPrice,
      material: material,
      gasType: gasType,
      thickness: thickness,
      cuttingDurationHours: cuttingDurationHours,
      isDesignFeesIncluded: isDesignFeesIncluded,
    );
  }

  /// Calculate machine cost: cutting_duration_hours × rate_per_hour
  Future<double> _calculateMachineCost({
    required String material,
    required double cuttingDurationHours,
  }) async {
    // Get material rate per hour
    final materialData = await _materialsDao.getMaterialByName(material);

    if (materialData == null || materialData.rate <= 0) {
      return 0.0; // No cost if material rate not configured
    }

    return cuttingDurationHours * materialData.rate;
  }

  /// Calculate gas cost based on gas type and thickness interval
  Future<double> _calculateGasCost({
    required String gasType,
    required double thickness,
    required double cuttingDurationHours,
  }) async {
    // Get gas cost for the specific thickness
    final gasCostPerHour = await _gasCostsDao.getGasCostForThickness(gasType, thickness);

    if (gasCostPerHour == null || gasCostPerHour <= 0) {
      return 0.0; // No cost if gas cost not configured
    }

    return cuttingDurationHours * gasCostPerHour;
  }

  /// Calculate setup fees (configurable value)
  Future<double> _calculateSetupFees(double? customSetupFees) async {
    if (customSetupFees != null) {
      return customSetupFees;
    }

    // Get default setup fees from configuration
    final defaultSetupFees = await _appSettingsDao.getSettingValueAsDouble('pricing', 'default_setup_fees');
    return defaultSetupFees ?? 0.0;
  }

  /// Calculate design cost based on whether design fees are included
  Future<double> _calculateDesignCost({
    required bool isDesignFeesIncluded,
    double? customDesignCost,
  }) async {
    if (!isDesignFeesIncluded) {
      return 0.0; // No design cost if design fees are not included
    }

    if (customDesignCost != null) {
      return customDesignCost;
    }

    // Get default design cost from configuration
    final defaultDesignCost = await _appSettingsDao.getDesignServicePrice();
    return defaultDesignCost ?? 0.0;
  }

  /// Determine thickness range for gas cost calculation
  String getThicknessRange(double thickness) {
    if (thickness >= 1 && thickness <= 5) {
      return '1-5 mm';
    } else if (thickness > 5 && thickness <= 10) {
      return '5-10 mm';
    } else if (thickness > 10 && thickness <= 15) {
      return '10-15 mm';
    } else {
      return '> 15 mm';
    }
  }

  /// Get default material rates (for reference)
  Map<String, double> getDefaultMaterialRates() {
    return {
      'acier': 600.0,        // Steel (Acier): 600 dh/h
      'inox': 900.0,         // Stainless Steel (Inox): 900 dh/h
      'cuivre': 0.0,         // Copper: [input rate]
      'aluminum': 0.0,       // Aluminium: [input rate]
    };
  }

  /// Set default configuration values
  /// Note: This method is deprecated as defaults are now handled by the database migration
  Future<void> setDefaultConfiguration() async {
    // Defaults are now seeded during database migration
    // This method is kept for backward compatibility but does nothing
  }
}

/// Data class to hold detailed pricing breakdown
class PricingBreakdown {
  final double machineCost;
  final double gasCost;
  final double setupFees;
  final double designCost;
  final double totalPrice;
  
  // Additional context information
  final String material;
  final String gasType;
  final double thickness;
  final double cuttingDurationHours;
  final bool isDesignFeesIncluded;

  const PricingBreakdown({
    required this.machineCost,
    required this.gasCost,
    required this.setupFees,
    required this.designCost,
    required this.totalPrice,
    required this.material,
    required this.gasType,
    required this.thickness,
    required this.cuttingDurationHours,
    required this.isDesignFeesIncluded,
  });

  /// Get step-by-step calculation breakdown as formatted strings
  List<String> getCalculationSteps() {
    final steps = <String>[];
    
    steps.add('=== CALCUL DU PRIX TOTAL ===');
    steps.add('');
    
    // Machine Cost
    steps.add('1. COÛT MACHINE:');
    steps.add('   Matériau: $material');
    steps.add('   Durée de découpe: ${cuttingDurationHours.toStringAsFixed(2)} heures');
    steps.add('   Coût machine = ${cuttingDurationHours.toStringAsFixed(2)} h × taux/h');
    steps.add('   Coût machine = ${machineCost.toStringAsFixed(2)} DH');
    steps.add('');
    
    // Gas Cost
    steps.add('2. COÛT GAZ:');
    steps.add('   Type de gaz: $gasType');
    steps.add('   Épaisseur: ${thickness.toStringAsFixed(1)} mm');
    steps.add('   Durée: ${cuttingDurationHours.toStringAsFixed(2)} heures');
    steps.add('   Coût gaz = ${gasCost.toStringAsFixed(2)} DH');
    steps.add('');
    
    // Setup Fees
    steps.add('3. FRAIS DE CONFIGURATION:');
    steps.add('   Frais de configuration = ${setupFees.toStringAsFixed(2)} DH');
    steps.add('');
    
    // Design Cost
    steps.add('4. COÛT DE CONCEPTION:');
    if (!isDesignFeesIncluded) {
      steps.add('   Frais de conception non inclus');
      steps.add('   Coût de conception = 0.00 DH');
    } else {
      steps.add('   Frais de conception inclus');
      steps.add('   Coût de conception = ${designCost.toStringAsFixed(2)} DH');
    }
    steps.add('');
    
    // Total
    steps.add('5. PRIX TOTAL:');
    steps.add('   Prix total = Coût machine + Coût gaz + Frais config + Coût conception');
    steps.add('   Prix total = ${machineCost.toStringAsFixed(2)} + ${gasCost.toStringAsFixed(2)} + ${setupFees.toStringAsFixed(2)} + ${designCost.toStringAsFixed(2)}');
    steps.add('   Prix total = ${totalPrice.toStringAsFixed(2)} DH');
    
    return steps;
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'machineCost': machineCost,
      'gasCost': gasCost,
      'setupFees': setupFees,
      'designCost': designCost,
      'totalPrice': totalPrice,
      'material': material,
      'gasType': gasType,
      'thickness': thickness,
      'cuttingDurationHours': cuttingDurationHours,
      'isDesignFeesIncluded': isDesignFeesIncluded,
    };
  }

  /// Create from JSON
  factory PricingBreakdown.fromJson(Map<String, dynamic> json) {
    return PricingBreakdown(
      machineCost: json['machineCost']?.toDouble() ?? 0.0,
      gasCost: json['gasCost']?.toDouble() ?? 0.0,
      setupFees: json['setupFees']?.toDouble() ?? 0.0,
      designCost: json['designCost']?.toDouble() ?? 0.0,
      totalPrice: json['totalPrice']?.toDouble() ?? 0.0,
      material: json['material'] ?? '',
      gasType: json['gasType'] ?? '',
      thickness: json['thickness']?.toDouble() ?? 0.0,
      cuttingDurationHours: json['cuttingDurationHours']?.toDouble() ?? 0.0,
      isDesignFeesIncluded: json['isDesignFeesIncluded'] ?? false,
    );
  }
}
