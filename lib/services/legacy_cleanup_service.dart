import 'package:flutter/foundation.dart';
import '../database/database.dart';
import '../database/daos.dart';
import 'database_service.dart';

/// Service to clean up legacy configuration entries
class LegacyCleanupService {
  static LegacyCleanupService? _instance;
  static LegacyCleanupService get instance => _instance ??= LegacyCleanupService._();
  
  LegacyCleanupService._();

  late AppSettingsDao _appSettingsDao;

  /// Initialize the cleanup service
  void initialize() {
    final database = DatabaseService.instance.database;
    _appSettingsDao = AppSettingsDao(database);
  }

  /// Clean up legacy configuration entries
  Future<void> cleanupLegacyEntries() async {
    try {
      debugPrint('Starting legacy configuration cleanup...');
      
      // Get all legacy entries
      final legacySettings = await _appSettingsDao.getSettingsByCategory('legacy');
      
      if (legacySettings.isEmpty) {
        debugPrint('No legacy entries found to clean up');
        return;
      }

      debugPrint('Found ${legacySettings.length} legacy entries to clean up');

      // Map of keys that should be migrated to proper categories
      final migrationMap = {
        'default_setup_fees': {'category': 'pricing', 'type': 'double'},
        'design_service_price': {'category': 'pricing', 'type': 'double'},
        'default_thickness': {'category': 'defaults', 'type': 'double'},
        'default_cutting_speed': {'category': 'defaults', 'type': 'double'},
        'price_per_meter': {'category': 'pricing', 'type': 'double'},
        'tax_rate': {'category': 'pricing', 'type': 'double'},
        'min_order_amount': {'category': 'pricing', 'type': 'double'},
        'app_version': {'category': 'app', 'type': 'string'},
        'currency': {'category': 'app', 'type': 'string'},
      };

      int migratedCount = 0;
      int deletedCount = 0;

      for (final legacySetting in legacySettings) {
        final key = legacySetting.key;
        final value = legacySetting.value;
        
        if (migrationMap.containsKey(key)) {
          // Migrate to proper category
          final mapping = migrationMap[key]!;
          
          // Check if it already exists in the proper category
          final existingProper = await _appSettingsDao.getSettingByCategoryAndKey(
            mapping['category']!,
            key,
          );
          
          if (existingProper == null) {
            // Migrate to proper category
            await _appSettingsDao.upsertSetting(
              mapping['category']!,
              key,
              value,
              mapping['type']!,
              description: 'Migrated from legacy category',
            );
            debugPrint('Migrated $key from legacy to ${mapping['category']} category');
            migratedCount++;
          } else {
            debugPrint('$key already exists in ${mapping['category']} category, skipping migration');
          }
        }
        
        // Delete the legacy entry
        await _appSettingsDao.deleteSetting(legacySetting.id);
        debugPrint('Deleted legacy entry: $key');
        deletedCount++;
      }

      debugPrint('Legacy cleanup completed:');
      debugPrint('  - Migrated: $migratedCount entries');
      debugPrint('  - Deleted: $deletedCount legacy entries');
      
    } catch (e) {
      debugPrint('Error during legacy cleanup: $e');
      rethrow;
    }
  }

  /// Check if legacy cleanup is needed
  Future<bool> isCleanupNeeded() async {
    try {
      final legacySettings = await _appSettingsDao.getSettingsByCategory('legacy');
      return legacySettings.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking if cleanup is needed: $e');
      return false;
    }
  }

  /// Get count of legacy entries
  Future<int> getLegacyEntriesCount() async {
    try {
      final legacySettings = await _appSettingsDao.getSettingsByCategory('legacy');
      return legacySettings.length;
    } catch (e) {
      debugPrint('Error getting legacy entries count: $e');
      return 0;
    }
  }

  /// Show legacy entries for debugging
  Future<void> showLegacyEntries() async {
    try {
      final legacySettings = await _appSettingsDao.getSettingsByCategory('legacy');
      
      if (legacySettings.isEmpty) {
        debugPrint('No legacy entries found');
        return;
      }

      debugPrint('Legacy entries found:');
      for (final setting in legacySettings) {
        debugPrint('  ${setting.key}: ${setting.value} (${setting.valueType})');
      }
    } catch (e) {
      debugPrint('Error showing legacy entries: $e');
    }
  }

  /// Perform automatic cleanup if needed
  Future<void> autoCleanupIfNeeded() async {
    try {
      if (await isCleanupNeeded()) {
        debugPrint('Legacy entries detected, performing automatic cleanup...');
        await cleanupLegacyEntries();
      }
    } catch (e) {
      debugPrint('Error during automatic cleanup: $e');
    }
  }
}
