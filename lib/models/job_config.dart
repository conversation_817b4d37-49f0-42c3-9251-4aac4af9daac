import '../services/database_service.dart';

class JobConfig {
  final String clientName;
  final String clientReference;
  final String gasType;
  final String material;
  final double thickness;
  final double cuttingSpeed;
  final double linearMeters;
  final bool includeDesignFees;
  final bool isNewClient;
  final String designReference;
  final double designServicePrice;
  // New fields for comprehensive pricing formula
  final double cuttingDurationHours;
  final double setupFees;
  // New field to track if design service price is customized
  final bool isDesignServicePriceCustomized;

  JobConfig({
    required this.clientName,
    required this.clientReference,
    required this.gasType,
    required this.material,
    required this.thickness,
    required this.cuttingSpeed,
    required this.linearMeters,
    required this.includeDesignFees,
    required this.isNewClient,
    required this.designReference,
    required this.designServicePrice,
    required this.cuttingDurationHours,
    required this.setupFees,
    required this.isDesignServicePriceCustomized,
  });

  JobConfig copyWith({
    String? clientName,
    String? clientReference,
    String? gasType,
    String? material,
    double? thickness,
    double? cuttingSpeed,
    double? linearMeters,
    bool? includeDesignFees,
    bool? isNewClient,
    String? designReference,
    double? designServicePrice,
    double? cuttingDurationHours,
    double? setupFees,
    bool? isDesignServicePriceCustomized,
  }) {
    return JobConfig(
      clientName: clientName ?? this.clientName,
      clientReference: clientReference ?? this.clientReference,
      gasType: gasType ?? this.gasType,
      material: material ?? this.material,
      thickness: thickness ?? this.thickness,
      cuttingSpeed: cuttingSpeed ?? this.cuttingSpeed,
      linearMeters: linearMeters ?? this.linearMeters,
      includeDesignFees: includeDesignFees ?? this.includeDesignFees,
      isNewClient: isNewClient ?? this.isNewClient,
      designReference: designReference ?? this.designReference,
      designServicePrice: designServicePrice ?? this.designServicePrice,
      cuttingDurationHours: cuttingDurationHours ?? this.cuttingDurationHours,
      setupFees: setupFees ?? this.setupFees,
      isDesignServicePriceCustomized: isDesignServicePriceCustomized ?? this.isDesignServicePriceCustomized,
    );
  }

  static Future<JobConfig> get initial async {
    // Check if there are any existing clients in the database
    final hasClients = await _hasExistingClients();
    
    return JobConfig(
        clientName: '',
        clientReference: '',
        gasType: '',
        material: '',
        thickness: 1.0,
        cuttingSpeed: 100.0,
        linearMeters: 11.0,
        includeDesignFees: false,
        isNewClient: !hasClients, // true if no clients exist, false if clients exist
        designReference: '',
        designServicePrice: 50.0, // Will be updated with database value
        cuttingDurationHours: 1.0,
        setupFees: 0.0,
        isDesignServicePriceCustomized: false,
      );
  }

  // Helper method to check if there are existing clients
  static Future<bool> _hasExistingClients() async {
    try {
      final DatabaseService databaseService = DatabaseService.instance;
      final clients = await databaseService.getAllClients();
      return clients.isNotEmpty;
    } catch (e) {
      // If there's an error accessing the database, assume there are no clients
      return false;
    }
  }
}
