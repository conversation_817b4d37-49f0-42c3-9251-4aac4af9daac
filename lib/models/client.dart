class Client {
  final String id;
  final String name;
  final String reference;
  final String? company;
  final String? email;
  final String? phone;
  final int projects;
  final double totalSpent;
  final DateTime createdAt;

  Client({
    required this.id,
    required this.name,
    required this.reference,
    this.company,
    this.email,
    this.phone,
    required this.projects,
    required this.totalSpent,
    required this.createdAt,
  });

  Client copyWith({
    String? id,
    String? name,
    String? reference,
    String? company,
    String? email,
    String? phone,
    int? projects,
    double? totalSpent,
    DateTime? createdAt,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      reference: reference ?? this.reference,
      company: company ?? this.company,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      projects: projects ?? this.projects,
      totalSpent: totalSpent ?? this.totalSpent,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id'],
      name: json['name'],
      reference: json['reference'],
      company: json['company'],
      email: json['email'],
      phone: json['phone'],
      projects: json['projects'],
      totalSpent: json['totalSpent'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'reference': reference,
      'company': company,
      'email': email,
      'phone': phone,
      'projects': projects,
      'totalSpent': totalSpent,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
