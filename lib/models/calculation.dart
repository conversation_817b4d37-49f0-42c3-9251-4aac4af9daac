enum CalculationStatus { completed, pending, cancelled }

class Calculation {
  final String id;
  final String clientName;
  final String clientReference;
  final String material;
  final double thickness;
  final String gasType;
  final double linearMeters;
  final double cuttingSpeed;
  final bool designProvided;
  final String? designReference;
  final double totalPrice;
  final DateTime createdAt;
  final CalculationStatus status;
  // New fields for detailed pricing breakdown
  final double cuttingDurationHours;
  final double setupFees;
  final double machineCost;
  final double gasCost;
  final double designCost;

  Calculation({
    required this.id,
    required this.clientName,
    required this.clientReference,
    required this.material,
    required this.thickness,
    required this.gasType,
    required this.linearMeters,
    required this.cuttingSpeed,
    required this.designProvided,
    this.designReference,
    required this.totalPrice,
    required this.createdAt,
    required this.status,
    required this.cuttingDurationHours,
    required this.setupFees,
    required this.machineCost,
    required this.gasCost,
    required this.designCost,
  });

  Calculation copyWith({
    String? id,
    String? clientName,
    String? clientReference,
    String? material,
    double? thickness,
    String? gasType,
    double? linearMeters,
    double? cuttingSpeed,
    bool? designProvided,
    String? designReference,
    double? totalPrice,
    DateTime? createdAt,
    CalculationStatus? status,
    double? cuttingDurationHours,
    double? setupFees,
    double? machineCost,
    double? gasCost,
    double? designCost,
  }) {
    return Calculation(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      clientReference: clientReference ?? this.clientReference,
      material: material ?? this.material,
      thickness: thickness ?? this.thickness,
      gasType: gasType ?? this.gasType,
      linearMeters: linearMeters ?? this.linearMeters,
      cuttingSpeed: cuttingSpeed ?? this.cuttingSpeed,
      designProvided: designProvided ?? this.designProvided,
      designReference: designReference ?? this.designReference,
      totalPrice: totalPrice ?? this.totalPrice,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      cuttingDurationHours: cuttingDurationHours ?? this.cuttingDurationHours,
      setupFees: setupFees ?? this.setupFees,
      machineCost: machineCost ?? this.machineCost,
      gasCost: gasCost ?? this.gasCost,
      designCost: designCost ?? this.designCost,
    );
  }

  factory Calculation.fromJson(Map<String, dynamic> json) {
    return Calculation(
      id: json['id'],
      clientName: json['clientName'],
      clientReference: json['clientReference'],
      material: json['material'],
      thickness: json['thickness'].toDouble(),
      gasType: json['gasType'],
      linearMeters: json['linearMeters'].toDouble(),
      cuttingSpeed: json['cuttingSpeed'].toDouble(),
      designProvided: json['designProvided'],
      designReference: json['designReference'],
      totalPrice: json['totalPrice'].toDouble(),
      createdAt: DateTime.parse(json['createdAt']),
      status: CalculationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      cuttingDurationHours: json['cuttingDurationHours']?.toDouble() ?? 1.0,
      setupFees: json['setupFees']?.toDouble() ?? 0.0,
      machineCost: json['machineCost']?.toDouble() ?? 0.0,
      gasCost: json['gasCost']?.toDouble() ?? 0.0,
      designCost: json['designCost']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientName': clientName,
      'clientReference': clientReference,
      'material': material,
      'thickness': thickness,
      'gasType': gasType,
      'linearMeters': linearMeters,
      'cuttingSpeed': cuttingSpeed,
      'designProvided': designProvided,
      'designReference': designReference,
      'totalPrice': totalPrice,
      'createdAt': createdAt.toIso8601String(),
      'status': status.toString().split('.').last,
      'cuttingDurationHours': cuttingDurationHours,
      'setupFees': setupFees,
      'machineCost': machineCost,
      'gasCost': gasCost,
      'designCost': designCost,
    };
  }
}
