/// Application constants used throughout the laser cutting calculator
class AppConstants {
  // Default material rates (DH per hour)
  static const Map<String, double> defaultMaterialRates = {
    'acier': 600.0,        // Steel
    'inox': 900.0,         // Stainless Steel
    'cuivre': 0.0,         // Copper (configurable)
    'aluminum': 0.0,       // Aluminum (configurable)
    'tole_galvanisee': 0.0, // Galvanized sheet (configurable)
  };

  // Default gas types with cost structure
  static const Map<String, Map<String, double>> defaultGasTypes = {
    'Oxygène (O₂)': {
      '1-5 mm': 0.0,
      '5-10 mm': 0.0,
      '10-15 mm': 0.0,
      '> 15 mm': 0.0,
    },
    'Azote (N₂)': {
      '1-5 mm': 0.0,
      '5-10 mm': 0.0,
      '10-15 mm': 0.0,
      '> 15 mm': 0.0,
    },
    'Air Comprimé': {
      '1-5 mm': 0.0,
      '5-10 mm': 0.0,
      '10-15 mm': 0.0,
      '> 15 mm': 0.0,
    },
  };

  // Getters for backward compatibility with tests
  static Map<String, double> get materialRates => defaultMaterialRates;
  static Map<String, Map<String, double>> get gasTypes => defaultGasTypes;

  // Gas labels for backward compatibility
  static Map<String, String> get gasLabels => {
    'Oxygène (O₂)': 'Oxygène (O₂)',
    'Azote (N₂)': 'Azote (N₂)',
    'Air Comprimé': 'Air Comprimé',
    'oxygen': 'Oxygène (O₂)',
    'nitrogen': 'Azote (N₂)',
    'air': 'Air Comprimé',
  };

  // Material labels for backward compatibility
  static Map<String, String> get materialLabels => {
    'acier': 'Acier (Steel)',
    'inox': 'Inox (Stainless Steel)',
    'cuivre': 'Cuivre (Copper)',
    'aluminum': 'Aluminum',
    'tole_galvanisee': 'Tôle Galvanisée',
  };

  // Thickness ranges for gas cost calculation
  static const List<String> thicknessRanges = [
    '1-5 mm',
    '5-10 mm',
    '10-15 mm',
    '> 15 mm',
  ];

  // Default configuration values
  static const double defaultSetupFees = 0.0;
  static const double defaultDesignCost = 50.0;
  static const double defaultThickness = 1.0;
  static const double defaultCuttingSpeed = 100.0;
  static const double defaultLinearMeters = 11.0;
  static const double defaultCuttingDurationHours = 1.0;

  // Database configuration keys
  static const String keyDefaultGasType = 'default_gas_type';
  static const String keyDefaultMaterial = 'default_material';
  static const String keyDefaultThickness = 'default_thickness';
  static const String keyDefaultCuttingSpeed = 'default_cutting_speed';
  static const String keyDefaultLinearMeters = 'default_linear_meters';
  static const String keyDesignServicePrice = 'design_service_price';
  static const String keyDefaultSetupFees = 'default_setup_fees';

  // Material rate keys
  static const String materialRatePrefix = 'material_rate_';
  
  // Gas cost keys
  static const String gasCostPrefix = 'gas_cost_';

  // UI Constants
  static const double cardElevation = 2.0;
  static const double borderRadius = 8.0;
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // Currency
  static const String currency = 'DH';
  static const String currencySymbol = 'DH';

  // Validation constants
  static const double minThickness = 0.1;
  static const double maxThickness = 100.0;
  static const double minCuttingSpeed = 1.0;
  static const double maxCuttingSpeed = 10000.0;
  static const double minLinearMeters = 0.1;
  static const double maxLinearMeters = 10000.0;
  static const double minCuttingDuration = 0.1;
  static const double maxCuttingDuration = 1000.0;

  // Error messages
  static const String errorInvalidThickness = 'Épaisseur invalide';
  static const String errorInvalidCuttingSpeed = 'Vitesse de découpe invalide';
  static const String errorInvalidLinearMeters = 'Distance linéaire invalide';
  static const String errorInvalidCuttingDuration = 'Durée de découpe invalide';
  static const String errorMissingClientInfo = 'Informations client manquantes';
  static const String errorMissingMaterial = 'Matériau non sélectionné';
  static const String errorMissingGasType = 'Type de gaz non sélectionné';

  // Success messages
  static const String successCalculationSaved = 'Calcul sauvegardé avec succès';
  static const String successConfigurationSaved = 'Configuration sauvegardée avec succès';
  static const String successMaterialAdded = 'Matériau ajouté avec succès';
  static const String successGasTypeAdded = 'Type de gaz ajouté avec succès';

  // Application info
  static const String appName = 'Calculateur de Coût Découpe Laser';
  static const String appDescription = 'Système professionnel d\'estimation de projet et de devis';
  static const String appVersion = '1.0.0';

  // License validation
  // static const String licenseServerUrl = 'http://localhost:8000/api/v1/validate';
  static const String licenseServerUrl = 'https://lkm-app.fly.dev/api/v1/validate';
  static const Duration licenseValidationTimeout = Duration(seconds: 30);
  static const Duration licenseCacheDuration = Duration(hours: 24);

  // License error messages
  static const String errorLicenseRequired = 'Licence valide requise';
  static const String errorLicenseValidation = 'Erreur lors de la validation de la licence';
  static const String errorLicenseExpired = 'Licence expirée';
  static const String errorLicenseInvalid = 'Licence invalide';
  static const String errorNoConnection = 'Connexion au serveur de licence impossible';

  // Private constructor to prevent instantiation
  AppConstants._();
}
