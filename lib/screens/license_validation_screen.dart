import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/license_provider.dart';
import '../constants/app_constants.dart';
import '../services/license_validation_service.dart';

/// Screen for license validation and management
class LicenseValidationScreen extends StatefulWidget {
  const LicenseValidationScreen({super.key});

  @override
  State<LicenseValidationScreen> createState() => _LicenseValidationScreenState();
}

class _LicenseValidationScreenState extends State<LicenseValidationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serialNumberController = TextEditingController();
  final _serverUrlController = TextEditingController();
  
  bool _showAdvanced = false;

  @override
  void initState() {
    super.initState();
    _loadStoredCredentials();
  }

  @override
  void dispose() {
    _serialNumberController.dispose();
    _serverUrlController.dispose();
    super.dispose();
  }

  void _loadStoredCredentials() {
    final licenseProvider = context.read<LicenseProvider>();
    final credentials = licenseProvider.storedCredentials;
    
    if (credentials != null) {
      _serialNumberController.text = credentials.serialNumber;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Validation de Licence'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<LicenseProvider>(
        builder: (context, licenseProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // License Status Card
                  _buildStatusCard(licenseProvider),

                  const SizedBox(height: AppConstants.defaultPadding),

                  // License Details Card (only show if we have license data)
                  if (licenseProvider.lastValidationResult != null || licenseProvider.storedCredentials != null)
                    _buildLicenseDetailsCard(licenseProvider),

                  if (licenseProvider.lastValidationResult != null || licenseProvider.storedCredentials != null)
                    const SizedBox(height: AppConstants.defaultPadding),

                  // License Input Form
                  _buildLicenseForm(licenseProvider),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Action Buttons
                  _buildActionButtons(licenseProvider),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Advanced Options
                  // _buildAdvancedOptions(),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Help Information
                  // _buildHelpCard(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(LicenseProvider licenseProvider) {
    final statusColor = _getStatusColor(licenseProvider.getStatusColor());
    
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(licenseProvider),
                  color: statusColor,
                  size: 24,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Statut de la Licence',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              licenseProvider.getStatusMessage(),
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (licenseProvider.lastValidationResult != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                'Dernière validation: ${_formatTimestamp(licenseProvider.lastValidationResult!.timestamp)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              // Show expiry date if available
              if (licenseProvider.lastValidationResult!.expiryDate != null) ...[
                const SizedBox(height: AppConstants.smallPadding / 2),
                _buildExpiryInfo(licenseProvider.lastValidationResult!),
              ],
            ],
            // Quick summary for valid licenses
            if (licenseProvider.isLicenseValid && licenseProvider.lastValidationResult?.expiryDate != null) ...[
              const SizedBox(height: AppConstants.smallPadding),
              _buildQuickSummary(licenseProvider),
            ],

            if (licenseProvider.shouldPromptForRenewal()) ...[
              const SizedBox(height: AppConstants.smallPadding),
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning_amber, color: Colors.orange, size: 16),
                    const SizedBox(width: AppConstants.smallPadding),
                    Expanded(
                      child: Text(
                        'Il est recommandé de revalider votre licence',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Color.fromRGBO(
                            Colors.orange.red,
                            Colors.orange.green,
                            Colors.orange.blue,
                            0.8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLicenseForm(LicenseProvider licenseProvider) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de Licence',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Serial Number Field
            TextFormField(
              controller: _serialNumberController,
              decoration: const InputDecoration(
                labelText: 'Numéro de Série',
                hintText: 'XXXXX-XXXXX-XXXXX-XXXXX',
                prefixIcon: Icon(Icons.confirmation_number),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le numéro de série est requis';
                }
                if (!RegExp(r'^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$').hasMatch(value.trim())) {
                  return 'Format de numéro de série invalide';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(LicenseProvider licenseProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: licenseProvider.isValidating ? null : _validateLicense,
          icon: licenseProvider.isValidating 
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.verified),
          label: Text(licenseProvider.isValidating ? 'Validation...' : 'Valider la Licence'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        
        if (licenseProvider.storedCredentials != null) ...[
          const SizedBox(height: AppConstants.smallPadding),
          OutlinedButton.icon(
            onPressed: licenseProvider.isValidating ? null : _refreshLicense,
            icon: const Icon(Icons.refresh),
            label: const Text('Actualiser'),
          ),
        ],
        
        const SizedBox(height: AppConstants.smallPadding),
        
        TextButton.icon(
          onPressed: _clearLicenseData,
          icon: const Icon(Icons.clear),
          label: const Text('Effacer les Données'),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedOptions() {
    return Card(
      elevation: AppConstants.cardElevation,
      child: ExpansionTile(
        title: const Text('Options Avancées'),
        leading: const Icon(Icons.settings),
        initiallyExpanded: _showAdvanced,
        onExpansionChanged: (expanded) => setState(() => _showAdvanced = expanded),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: TextFormField(
              controller: _serverUrlController,
              decoration: const InputDecoration(
                labelText: 'URL du Serveur de Licence (Optionnel)',
                // hintText: 'http://localhost:8000/api/v1/validate',
                prefixIcon: Icon(Icons.link),
                border: OutlineInputBorder(),
                helperText: 'Laissez vide pour utiliser le serveur par défaut',
              ),
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  try {
                    Uri.parse(value.trim());
                  } catch (e) {
                    return 'URL invalide';
                  }
                }
                return null;
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpCard() {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.help_outline),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Aide',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            const Text(
              '• Le numéro de série est fourni lors de l\'achat de la licence\n'
              '• Une connexion Internet est requise pour la validation initiale\n'
              '• La licence est mise en cache pour 24 heures après validation\n'
              '• Contactez le support si vous rencontrez des problèmes',
            ),
          ],
        ),
      ),
    );
  }

  void _validateLicense() async {
    if (!_formKey.currentState!.validate()) return;

    final licenseProvider = context.read<LicenseProvider>();
    
    final success = await licenseProvider.validateLicense(
      serialNumber: _serialNumberController.text.trim(),
      customServerUrl: _serverUrlController.text.trim().isEmpty 
          ? null 
          : _serverUrlController.text.trim(),
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Licence validée avec succès' : 'Échec de la validation'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
      
      if (success) {
        Navigator.of(context).pop();
      }
    }
  }

  void _refreshLicense() async {
    final licenseProvider = context.read<LicenseProvider>();
    final success = await licenseProvider.refreshLicense();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'Licence actualisée' : 'Échec de l\'actualisation'),
          backgroundColor: success ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  void _clearLicenseData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: const Text('Voulez-vous vraiment effacer toutes les données de licence ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Effacer'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final licenseProvider = context.read<LicenseProvider>();
      await licenseProvider.clearLicenseData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Données de licence effacées')),
        );
      }
    }
  }

  IconData _getStatusIcon(LicenseProvider licenseProvider) {
    if (licenseProvider.isValidating) return Icons.hourglass_empty;
    if (licenseProvider.isLicenseValid) return Icons.check_circle;
    return Icons.error;
  }

  Color _getStatusColor(LicenseStatusColor statusColor) {
    switch (statusColor) {
      case LicenseStatusColor.success:
        return Colors.green;
      case LicenseStatusColor.warning:
        return Colors.orange;
      case LicenseStatusColor.error:
        return Colors.red;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year} à ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Build expiry date information widget
  Widget _buildExpiryInfo(LicenseValidationResult result) {
    if (result.expiryDate == null) return const SizedBox.shrink();

    final expiryDate = result.expiryDate!;
    final daysUntilExpiry = result.daysUntilExpiry ?? 0;
    final isExpired = result.isExpired;
    final isExpiringSoon = result.isExpiringSoon;

    Color expiryColor;
    IconData expiryIcon;
    String expiryText;

    if (isExpired) {
      expiryColor = Colors.red;
      expiryIcon = Icons.error_outline;
      expiryText = 'Licence expirée le ${_formatDate(expiryDate)}';
    } else if (isExpiringSoon) {
      expiryColor = Colors.orange;
      expiryIcon = Icons.warning_amber_outlined;
      expiryText = 'Expire dans $daysUntilExpiry jour${daysUntilExpiry > 1 ? 's' : ''} (${_formatDate(expiryDate)})';
    } else {
      expiryColor = Colors.green;
      expiryIcon = Icons.check_circle_outline;
      expiryText = 'Valide jusqu\'au ${_formatDate(expiryDate)}';
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallPadding,
        vertical: AppConstants.smallPadding / 2,
      ),
      decoration: BoxDecoration(
        color: expiryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
        border: Border.all(color: expiryColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            expiryIcon,
            color: expiryColor,
            size: 16,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(
              expiryText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color.fromRGBO(
                  expiryColor.red,
                  expiryColor.green,
                  expiryColor.blue,
                  0.8,
                ),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Build quick summary for valid licenses
  Widget _buildQuickSummary(LicenseProvider licenseProvider) {
    final result = licenseProvider.lastValidationResult!;
    final expiryDate = result.expiryDate!;
    final daysUntilExpiry = result.daysUntilExpiry ?? 0;

    String summaryText;
    Color summaryColor;
    IconData summaryIcon;

    if (result.isExpired) {
      summaryText = 'Licence expirée depuis ${daysUntilExpiry * -1} jours';
      summaryColor = Colors.red;
      summaryIcon = Icons.error;
    } else if (result.isExpiringSoon) {
      summaryText = 'Expire dans $daysUntilExpiry jours (${_formatDate(expiryDate)})';
      summaryColor = Colors.orange;
      summaryIcon = Icons.warning;
    } else {
      summaryText = 'Valide jusqu\'au ${_formatDate(expiryDate)} ($daysUntilExpiry jours restants)';
      summaryColor = Colors.green;
      summaryIcon = Icons.check_circle;
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: summaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
        border: Border.all(color: summaryColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(summaryIcon, color: summaryColor, size: 16),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: Text(
              summaryText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Color.fromRGBO(
                  summaryColor.red,
                  summaryColor.green,
                  summaryColor.blue,
                  0.8,
                ),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build comprehensive license details card
  Widget _buildLicenseDetailsCard(LicenseProvider licenseProvider) {
    final result = licenseProvider.lastValidationResult;
    final credentials = licenseProvider.storedCredentials;

    if (result == null && credentials == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'Détails de la Licence',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // License details grid
            _buildDetailsGrid(licenseProvider),
          ],
        ),
      ),
    );
  }

  /// Build details grid with license information
  Widget _buildDetailsGrid(LicenseProvider licenseProvider) {
    final result = licenseProvider.lastValidationResult;
    final credentials = licenseProvider.storedCredentials;

    return Column(
      children: [
        // Serial Number
        if (credentials?.serialNumber != null)
          _buildDetailRow(
            'Numéro de Série',
            credentials!.serialNumber,
            Icons.confirmation_number,
            Colors.blue,
          ),

        // License Status
        if (result != null) ...[
          _buildDetailRow(
            'Statut',
            licenseProvider.getStatusMessage(),
            _getStatusIcon(licenseProvider),
            _getStatusColor(licenseProvider.getStatusColor()),
          ),

          // Expiry Date
          if (result.expiryDate != null)
            _buildExpiryDetailRow(result),

          // Days Until Expiry
          if (result.daysUntilExpiry != null)
            _buildDetailRow(
              'Jours Restants',
              result.isExpired
                ? 'Expiré (${result.daysUntilExpiry! * -1} jours)'
                : '${result.daysUntilExpiry} jours',
              result.isExpired ? Icons.error : Icons.schedule,
              result.isExpired ? Colors.red :
              result.isExpiringSoon ? Colors.orange : Colors.green,
            ),

          // Last Validation
          _buildDetailRow(
            'Dernière Validation',
            _formatTimestamp(result.timestamp),
            Icons.access_time,
            Colors.grey[600]!,
          ),

          // Hardware ID (if available from debug info)
          if (result.serverResponse != null && result.serverResponse!.containsKey('HWID'))
            _buildDetailRow(
              'ID Matériel',
              result.serverResponse!['HWID'].toString(),
              Icons.computer,
              Colors.grey[600]!,
            ),
        ],

        // Additional server response details
        if (result?.serverResponse != null)
          _buildServerResponseSection(result!.serverResponse!),
      ],
    );
  }

  /// Build a single detail row
  Widget _buildDetailRow(String label, String value, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build expiry date detail row with enhanced formatting
  Widget _buildExpiryDetailRow(LicenseValidationResult result) {
    if (result.expiryDate == null) return const SizedBox.shrink();

    final expiryDate = result.expiryDate!;
    final isExpired = result.isExpired;
    final isExpiringSoon = result.isExpiringSoon;

    Color color;
    IconData icon;
    String formattedDate;

    if (isExpired) {
      color = Colors.red;
      icon = Icons.error;
      formattedDate = '${_formatDate(expiryDate)} (Expiré)';
    } else if (isExpiringSoon) {
      color = Colors.orange;
      icon = Icons.warning;
      formattedDate = '${_formatDate(expiryDate)} (Expire bientôt)';
    } else {
      color = Colors.green;
      icon = Icons.check_circle;
      formattedDate = _formatDate(expiryDate);
    }

    return _buildDetailRow(
      'Date d\'Expiration',
      formattedDate,
      icon,
      color,
    );
  }

  /// Build server response section for additional details
  Widget _buildServerResponseSection(Map<String, dynamic> serverResponse) {
    // Filter out common fields we already display
    final filteredResponse = Map<String, dynamic>.from(serverResponse);
    filteredResponse.removeWhere((key, value) =>
      ['Code', 'Message', 'ExpiryDate', 'HttpCode'].contains(key));

    if (filteredResponse.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Divider(),
        const SizedBox(height: 8),
        Text(
          'Informations Supplémentaires',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        ...filteredResponse.entries.map((entry) =>
          _buildDetailRow(
            _formatServerKey(entry.key),
            entry.value.toString(),
            Icons.info,
            Colors.grey[600]!,
          ),
        ),
      ],
    );
  }

  /// Format server response keys for display
  String _formatServerKey(String key) {
    // Convert camelCase or snake_case to readable format
    return key
        .replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}')
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ')
        .trim();
  }
}
