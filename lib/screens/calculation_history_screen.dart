import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/history_provider.dart';
import '../models/calculation.dart';

import '../widgets/custom_card.dart';

class CalculationHistoryScreen extends StatelessWidget {
  const CalculationHistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<HistoryProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Stats Cards
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Total Calculs',
                      provider.filteredCalculations.length.toString(),
                      Icons.history,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'Valeur Totale',
                      '${provider.totalValue.toStringAsFixed(0)} DH',
                      Icons.attach_money,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'Complétés',
                      provider.completedCount.toString(),
                      Icons.check_circle,
                      Colors.purple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Filters Card
              CustomCard(
                title: 'Historique des Calculs',
                icon: Icons.history,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            decoration: InputDecoration(
                              hintText: 'Rechercher par client, référence ou ID...',
                              prefixIcon: const Icon(Icons.search),
                              border: const OutlineInputBorder(),
                              filled: true,
                              fillColor: Colors.grey[50],
                            ),
                            onChanged: provider.updateSearchTerm,
                          ),
                        ),
                        const SizedBox(width: 16),
                        SizedBox(
                          width: 160,
                          child: DropdownButtonFormField<CalculationStatus?>(
                            decoration: const InputDecoration(
                              labelText: 'Statut',
                              border: OutlineInputBorder(),
                            ),
                            value: provider.statusFilter,
                            items: [
                              const DropdownMenuItem(
                                value: null,
                                child: Text('Tous les statuts'),
                              ),
                              ...CalculationStatus.values.map((status) {
                                return DropdownMenuItem(
                                  value: status,
                                  child: Text(_getStatusLabel(status)),
                                );
                              }),
                            ],
                            onChanged: provider.updateStatusFilter,
                          ),
                        ),
                        const SizedBox(width: 16),
                        SizedBox(
                          width: 160,
                          child: DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'Trier par',
                              border: OutlineInputBorder(),
                            ),
                            value: provider.sortBy,
                            items: const [
                              DropdownMenuItem(
                                value: 'date-desc',
                                child: Text('Plus récent'),
                              ),
                              DropdownMenuItem(
                                value: 'date-asc',
                                child: Text('Plus ancien'),
                              ),
                              DropdownMenuItem(
                                value: 'price-desc',
                                child: Text('Prix décroissant'),
                              ),
                              DropdownMenuItem(
                                value: 'price-asc',
                                child: Text('Prix croissant'),
                              ),
                              DropdownMenuItem(
                                value: 'client',
                                child: Text('Nom client'),
                              ),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                provider.updateSortBy(value);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Calculations List
              ...provider.filteredCalculations.map((calculation) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _buildCalculationCard(context, calculation, provider),
              )),

              // Empty State
              if (provider.filteredCalculations.isEmpty) ...[
                CustomCard(
                  title: 'Aucun calcul trouvé',
                  icon: Icons.history,
                  child: Column(
                    children: [
                      const Icon(Icons.history, size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        provider.searchTerm.isNotEmpty || provider.statusFilter != null
                            ? 'Essayez d\'ajuster vos filtres de recherche'
                            : 'Commencez par créer des calculs pour les voir ici',
                        style: const TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationCard(BuildContext context, Calculation calculation, HistoryProvider provider) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.calendar_today, size: 20, color: Colors.grey),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          calculation.id,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          dateFormat.format(calculation.createdAt),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(calculation.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getStatusLabel(calculation.status),
                        style: TextStyle(
                          fontSize: 12,
                          color: _getStatusColor(calculation.status),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '${calculation.totalPrice.toStringAsFixed(2)} DH',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    Icons.person,
                    calculation.clientName,
                    calculation.clientReference,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.inventory,
                    calculation.material,
                    '${calculation.thickness}mm épais',
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.air,
                    calculation.gasType,
                    '${calculation.linearMeters}m linéaire',
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    Icons.speed,
                    '${calculation.cuttingSpeed.toStringAsFixed(0)} mm/min',
                    'Vitesse de découpe',
                    Colors.purple,
                  ),
                ),
              ],
            ),

            // Design Reference
            if (calculation.designReference != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.purple[50],
                  border: Border.all(color: Colors.purple[200]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Référence de Conception:',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.purple,
                      ),
                    ),
                    Text(
                      calculation.designReference!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.purple,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const Divider(),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: calculation.designProvided ? Colors.green : Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Conception: ${calculation.designProvided ? "Fournie" : "Configuration requise"}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
                Row(
                  children: [
                    OutlinedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Affichage du calcul...')),
                        );
                      },
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('Voir'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                        side: BorderSide(color: Colors.blue[200]!),
                      ),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Export en cours...')),
                        );
                      },
                      icon: const Icon(Icons.download, size: 16),
                      label: const Text('Exporter'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.green,
                        side: BorderSide(color: Colors.green[200]!),
                      ),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: () {
                        _showDeleteDialog(context, calculation, provider);
                      },
                      icon: const Icon(Icons.delete, size: 16),
                      label: const Text('Supprimer'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red[200]!),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String title, String subtitle, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getStatusLabel(CalculationStatus status) {
    switch (status) {
      case CalculationStatus.completed:
        return 'Complété';
      case CalculationStatus.pending:
        return 'En attente';
      case CalculationStatus.cancelled:
        return 'Annulé';
    }
  }

  Color _getStatusColor(CalculationStatus status) {
    switch (status) {
      case CalculationStatus.completed:
        return Colors.green;
      case CalculationStatus.pending:
        return Colors.orange;
      case CalculationStatus.cancelled:
        return Colors.red;
    }
  }

  void _showDeleteDialog(BuildContext context, Calculation calculation, HistoryProvider provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text('Voulez-vous vraiment supprimer le calcul ${calculation.id}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                provider.removeCalculation(calculation.id);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Calcul supprimé')),
                );
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );
  }
}
