import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/calculator_provider.dart';
import '../providers/client_provider.dart';
import '../providers/history_provider.dart';
import '../providers/material_provider.dart';
import '../providers/gas_provider.dart';

import '../widgets/custom_card.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/customizable_text_field.dart';
import '../widgets/gas_type_selector.dart';
import '../widgets/pricing_breakdown_widget.dart';

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeProviders();
  }

  Future<void> _initializeProviders() async {
    final calculatorProvider = Provider.of<CalculatorProvider>(context, listen: false);
    await calculatorProvider.initialize();
    setState(() {
      _isInitialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Consumer5<CalculatorProvider, ClientProvider, HistoryProvider, MaterialProvider, GasProvider>(
      builder: (context, calculatorProvider, clientProvider, historyProvider, materialProvider, gasProvider, child) {
        final config = calculatorProvider.config;
        final totalPrice = calculatorProvider.totalPrice;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Main Content
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    // Client Information Card
                    CustomCard(
                      title: 'Informations Client',
                      icon: Icons.person,
                      child: Column(
                        children: [
                          // New Client Toggle
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Enregistrement Nouveau Client',
                                      style: TextStyle(fontWeight: FontWeight.w500),
                                    ),
                                    Text(
                                      'Activer pour enregistrer un nouveau client',
                                      style: TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                  ],
                                ),
                                Switch(
                                  value: config.isNewClient,
                                  onChanged: calculatorProvider.updateIsNewClient,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          if (config.isNewClient) ...[
                            Row(
                              children: [
                                Expanded(
                                  child: CustomTextField(
                                    label: 'Nom du Client *',
                                    value: config.clientName,
                                    onChanged: calculatorProvider.updateClientName,
                                    hint: 'Saisir le nom de l\'entreprise cliente',
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: CustomTextField(
                                    label: 'Numéro de Référence *',
                                    value: config.clientReference,
                                    onChanged: calculatorProvider.updateClientReference,
                                    hint: 'Saisir l\'ID de référence du projet',
                                  ),
                                ),
                              ],
                            ),
                          ] else ...[
                            Row(
                              children: [
                                Expanded(
                                  child: DropdownButtonFormField<String>(
                                    decoration: const InputDecoration(
                                      labelText: 'Sélectionner Client',
                                      border: OutlineInputBorder(),
                                    ),
                                    items: clientProvider.clients.map((client) {
                                      return DropdownMenuItem(
                                        value: client.id,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(client.name, style: const TextStyle(fontWeight: FontWeight.w500)),
                                            Text(client.reference, style: const TextStyle(fontSize: 12, color: Colors.grey)),
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (clientId) {
                                      if (clientId != null) {
                                        final client = clientProvider.getClientById(clientId);
                                        if (client != null) {
                                          calculatorProvider.updateClientName(client.name);
                                          calculatorProvider.updateClientReference(client.reference);
                                        }
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () {
                                    calculatorProvider.updateClientName('');
                                    calculatorProvider.updateClientReference('');
                                  },
                                  icon: const Icon(Icons.clear),
                                  tooltip: 'Réinitialiser la sélection',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.grey[100],
                                    foregroundColor: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            if (config.clientName.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('Client Sélectionné :', style: TextStyle(fontWeight: FontWeight.w500)),
                                    Text('${config.clientName} - ${config.clientReference}'),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Design Services Card
                    CustomCard(
                      title: 'Services de Conception',
                      icon: Icons.description,
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Inclure les frais de conception',
                                      style: TextStyle(fontWeight: FontWeight.w500),
                                    ),
                                    Text(
                                      'Indiquer si le client a fourni des fichiers de conception',
                                      style: TextStyle(fontSize: 12, color: Colors.grey),
                                    ),
                                  ],
                                ),
                                Switch(
                                  value: config.includeDesignFees,
                                  onChanged: calculatorProvider.updateIsDesignProvided,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          CustomTextField(
                            label: 'Référence de Conception',
                            value: config.designReference,
                            onChanged: calculatorProvider.updateDesignReference,
                            hint: 'Saisir la référence du fichier',
                          ),
                          if (config.includeDesignFees) ...[
                            const SizedBox(height: 16),
                            CustomizableTextField(
                              label: 'Frais de Service de Conception (DH)',
                              value: config.designServicePrice.toString(),
                              defaultValue: calculatorProvider.defaultDesignServicePrice.toString(),
                              isCustomized: config.isDesignServicePriceCustomized,
                              onChanged: (value) {
                                final price = double.tryParse(value) ?? 0.0;
                                // Auto-validate the customization as user types
                                calculatorProvider.validateDesignServicePriceCustomization(price);
                              },
                              onCustomize: () {
                                calculatorProvider.startDesignServicePriceCustomization();
                              },
                              onCancel: () {
                                calculatorProvider.cancelDesignServicePriceCustomization();
                              },
                              keyboardType: TextInputType.number,
                              suffix: 'DH',
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Material Specifications Card
                    CustomCard(
                      title: 'Spécifications Matériau',
                      icon: Icons.inventory,
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Type de Matériau *',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    SizedBox(
                                      height: 56, // Fixed height to match CustomTextField
                                      child: DropdownButtonFormField<String>(
                                        decoration: InputDecoration(
                                          border: const OutlineInputBorder(),
                                          contentPadding: const EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 16,
                                          ),
                                          filled: true,
                                          fillColor: Colors.grey[50],
                                        ),
                                        value: config.material.isEmpty ? null : config.material,
                                        hint: const Text('Sélectionner le type de matériau'),
                                        items: materialProvider.materialEntries.map((entry) {
                                          return DropdownMenuItem(
                                            value: entry.key,
                                            child: Text(entry.value),
                                          );
                                        }).toList(),
                                        onChanged: (value) {
                                          if (value != null) {
                                            calculatorProvider.updateMaterial(value);
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: CustomTextField(
                                  label: 'Épaisseur (mm) *',
                                  value: config.thickness.toString(),
                                  onChanged: (value) {
                                    final thickness = double.tryParse(value) ?? 1.0;
                                    calculatorProvider.updateThickness(thickness);
                                  },
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Cutting Parameters Card
                    CustomCard(
                      title: 'Paramètres de Découpe',
                      icon: Icons.settings,
                      child: Column(
                        children: [
                          GasTypeSelector(
                            selectedGas: config.gasType,
                            onGasSelected: calculatorProvider.updateGasType,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: CustomTextField(
                                  label: 'Vitesse de Découpe (mm/min)',
                                  value: config.cuttingSpeed.toString(),
                                  onChanged: (value) {
                                    final speed = double.tryParse(value) ?? 100.0;
                                    calculatorProvider.updateCuttingSpeed(speed);
                                  },
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: CustomTextField(
                                  label: 'Distance Linéaire (m) *',
                                  value: config.linearMeters.toString(),
                                  onChanged: (value) {
                                    final meters = double.tryParse(value) ?? 1.0;
                                    calculatorProvider.updateLinearMeters(meters);
                                  },
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // New fields for comprehensive pricing
                          Row(
                            children: [
                              Expanded(
                                child: CustomTextField(
                                  label: 'Durée de Découpe (heures) *',
                                  value: config.cuttingDurationHours.toString(),
                                  onChanged: (value) {
                                    final hours = double.tryParse(value) ?? 1.0;
                                    calculatorProvider.updateCuttingDurationHours(hours);
                                  },
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: CustomTextField(
                                  label: 'Frais de Configuration (DH)',
                                  value: config.setupFees.toString(),
                                  onChanged: (value) {
                                    final fees = double.tryParse(value) ?? 0.0;
                                    calculatorProvider.updateSetupFees(fees);
                                  },
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // Cost Estimation Sidebar
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    // Pricing Breakdown
                    PricingBreakdownCard(
                      pricingBreakdown: calculatorProvider.pricingBreakdown,
                    ),
                    const SizedBox(height: 16),

                    // Project Specifications
                    if (config.gasType.isNotEmpty && config.material.isNotEmpty) ...[
                      CustomCard(
                        title: 'Spécifications du Projet',
                        icon: Icons.info_outline,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSpecItem('${config.linearMeters}m distance linéaire'),
                            _buildSpecItem('${config.thickness}mm ${materialProvider.materialLabels[config.material] ?? config.material}'),
                            _buildSpecItem('Gaz ${gasProvider.gasLabels[config.gasType] ?? config.gasType}'),
                            _buildSpecItem('${config.cuttingSpeed}mm/min vitesse'),
                            _buildSpecItem('${config.cuttingDurationHours}h durée de découpe'),
                            if (config.setupFees > 0)
                              _buildSpecItem('${config.setupFees.toStringAsFixed(2)} DH frais de config'),
                            if (config.includeDesignFees)
                              _buildSpecItem('${config.designServicePrice.toStringAsFixed(2)} DH conception'),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Save Button
                    if (config.gasType.isNotEmpty && config.material.isNotEmpty && totalPrice > 0)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () async {
                            final messenger = ScaffoldMessenger.of(context);

                            // Validate required fields
                            if (config.clientName.isEmpty || config.clientReference.isEmpty) {
                              messenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Veuillez remplir le nom et la référence du client'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            if (config.material.isEmpty || config.gasType.isEmpty) {
                              messenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Veuillez sélectionner le matériau et le type de gaz'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            if (totalPrice <= 0) {
                              messenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Le prix total doit être supérieur à 0'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            // Save the calculation
                            final success = await calculatorProvider.saveCalculation();

                            if (success) {
                              // Refresh the calculations list
                              await historyProvider.loadCalculations();

                              // Show success message
                              messenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Calcul sauvegardé avec succès!'),
                                  backgroundColor: Colors.green,
                                ),
                              );

                              // Reset the calculator for next calculation
                              await calculatorProvider.resetCalculator();
                            } else {
                              messenger.showSnackBar(
                                const SnackBar(
                                  content: Text('Erreur lors de la sauvegarde du calcul'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[800],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Sauvegarder le Calcul'),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSpecItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(text, style: const TextStyle(fontSize: 12))),
        ],
      ),
    );
  }
}
