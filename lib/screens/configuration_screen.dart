import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/custom_card.dart';
import '../providers/configuration_provider.dart';
import '../providers/material_provider.dart';
import '../providers/gas_provider.dart';
import '../providers/calculator_provider.dart';
import '../utils/dev_database_utils.dart';


class ConfigurationScreen extends StatefulWidget {
  const ConfigurationScreen({super.key});

  @override
  State<ConfigurationScreen> createState() => _ConfigurationScreenState();
}

class _ConfigurationScreenState extends State<ConfigurationScreen> {
  // All data loaded from database/services - no hard-coded values
  final Map<String, double> _materialRates = {};
  final Map<String, String> _materialLabels = {};
  final Map<String, bool> _materialEditable = {};
  final Map<String, Map<String, double>> _gasTypes = {};
  final Map<String, String> _gasLabels = {};

  double _setupFee = 0.0; // Will be loaded from database
  double _designCost = 0.0; // Will be loaded from database
  String _newMaterialName = '';
  String _newMaterialRate = '';
  String _newGasName = '';
  bool _isLoading = true;

  // Controllers for text fields
  final Map<String, TextEditingController> _materialControllers = {};
  final Map<String, Map<String, TextEditingController>> _gasControllers = {};
  late TextEditingController _setupFeeController;
  late TextEditingController _designCostController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadConfiguration();
  }



  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _initializeControllers() {
    // Setup fee and design cost controllers will be initialized after loading from database
    _setupFeeController = TextEditingController();
    _designCostController = TextEditingController();

    // Material and gas controllers will be initialized in _loadConfiguration()
    // after loading from MaterialService and GasProvider
  }

  void _disposeControllers() {
    _setupFeeController.dispose();
    _designCostController.dispose();

    // Dispose material controllers
    for (final controller in _materialControllers.values) {
      controller.dispose();
    }
    _materialControllers.clear();

    // Dispose gas controllers
    for (final gasMap in _gasControllers.values) {
      for (final controller in gasMap.values) {
        controller.dispose();
      }
    }
    _gasControllers.clear();
  }



  Future<void> _loadConfiguration() async {
    final configProvider = Provider.of<ConfigurationProvider>(context, listen: false);
    final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
    final gasProvider = Provider.of<GasProvider>(context, listen: false);

    try {
      // Load setup fee
      final setupFeeValue = await configProvider.getConfigValue('default_setup_fees');
      _setupFee = double.tryParse(setupFeeValue ?? '0.0') ?? 0.0;
      _setupFeeController.text = _setupFee.toString();

      // Load design cost
      final designCostValue = await configProvider.getConfigValue('design_service_price');
      _designCost = double.tryParse(designCostValue ?? '50.0') ?? 50.0;
      _designCostController.text = _designCost.toString();

      // Load all materials from MaterialProvider
      await materialProvider.loadMaterials();
      final allMaterialRates = materialProvider.materialRates;
      final materialLabels = materialProvider.materialLabels;

      // Update material rates, labels and controllers
      for (final entry in allMaterialRates.entries) {
        _materialRates[entry.key] = entry.value;
        _materialLabels[entry.key] = materialLabels[entry.key] ?? entry.key.capitalize();

        // Set editability for all materials (default materials are editable too)
        _materialEditable[entry.key] = true;

        // Create controller if it doesn't exist
        if (!_materialControllers.containsKey(entry.key)) {
          _materialControllers[entry.key] = TextEditingController(text: entry.value.toString());
        } else {
          _materialControllers[entry.key]?.text = entry.value.toString();
        }
      }

      // Load all gas types (default + custom) from GasProvider
      await gasProvider.loadGasTypes();
      final allGasTypes = gasProvider.gasTypes;
      final gasLabels = gasProvider.gasLabels;

      // Update gas types, labels and controllers
      _gasTypes.clear();
      _gasLabels.clear();
      _gasControllers.clear();

      for (final gasEntry in allGasTypes.entries) {
        _gasTypes[gasEntry.key] = Map.from(gasEntry.value);
        _gasLabels[gasEntry.key] = gasLabels[gasEntry.key] ?? gasEntry.key.capitalize();
        _gasControllers[gasEntry.key] = {};

        for (final rangeEntry in gasEntry.value.entries) {
          _gasControllers[gasEntry.key]![rangeEntry.key] =
              TextEditingController(text: rangeEntry.value.toString());
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addMaterial() async {
    if (_newMaterialName.isNotEmpty && _newMaterialRate.isNotEmpty) {
      final rate = double.tryParse(_newMaterialRate);
      if (rate != null) {
        final key = _newMaterialName.toLowerCase().replaceAll(' ', '-');
        final materialName = _newMaterialName; // Store for success message
        final messenger = ScaffoldMessenger.of(context);

        try {
          // Add material using MaterialProvider
          final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
          await materialProvider.addMaterial(key, rate);

          // Reload configuration to update local state
          await _loadConfiguration();

          // Clear form
          setState(() {
            _newMaterialName = '';
            _newMaterialRate = '';
          });

          // Show success message
          messenger.showSnackBar(
            SnackBar(
              content: Text('Matériau "$materialName" ajouté avec succès!'),
              backgroundColor: Colors.green,
            ),
          );
        } catch (e) {
          // Show error message
          messenger.showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de l\'ajout du matériau'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }



  Future<void> _removeMaterial(String key) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      // Remove using MaterialProvider for consistency
      final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
      final success = await materialProvider.removeMaterial(key);

      if (success) {
        // Update local state
        if (!mounted) return;
        setState(() {
          _materialRates.remove(key);
          _materialLabels.remove(key);
          _materialEditable.remove(key);
          _materialControllers[key]?.dispose();
          _materialControllers.remove(key);
        });

        // Show success message
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Matériau supprimé avec succès!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show error message
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la suppression du matériau'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Show error message
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Erreur lors de la suppression du matériau'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _updateMaterialRate(String key, double rate) async {
    try {
      // Update material rate using MaterialProvider
      final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
      await materialProvider.updateMaterialRate(key, rate);

      // Update local state
      if (!mounted) return;
      setState(() {
        _materialRates[key] = rate;
        // Don't update controller here to avoid cursor jumping
      });
    } catch (e) {
      // Revert to previous value if save fails
      // The UI will show the old value since setState wasn't called
    }
  }

  Future<void> _addGasType() async {
    if (_newGasName.isNotEmpty) {
      final gasName = _newGasName;
      final messenger = ScaffoldMessenger.of(context);

      try {
        // Add gas type using GasProvider
        final gasProvider = Provider.of<GasProvider>(context, listen: false);
        final success = await gasProvider.addGasType(gasName);

        if (success) {
          // Update local state
          if (!mounted) return;
          // Reload configuration to get the updated gas types with proper ranges
          await _loadConfiguration();

          // Clear form
          setState(() {
            _newGasName = '';
          });

          // Show success message
          messenger.showSnackBar(
            SnackBar(
              content: Text('Type de gaz "$gasName" ajouté avec succès!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          // Show error message
          messenger.showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de l\'ajout du type de gaz'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        // Show error message
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'ajout du type de gaz'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removeGasType(String gasName) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      // Remove gas type using GasProvider
      final gasProvider = Provider.of<GasProvider>(context, listen: false);
      final success = await gasProvider.removeGasType(gasName);

      if (success) {
        // Update local state
        if (!mounted) return;
        setState(() {
          _gasTypes.remove(gasName);
          _gasLabels.remove(gasName);
          // Dispose controllers
          _gasControllers[gasName]?.values.forEach((controller) => controller.dispose());
          _gasControllers.remove(gasName);
        });

        // Show success message
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Type de gaz supprimé avec succès!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show error message
        messenger.showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la suppression du type de gaz'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Show error message
      messenger.showSnackBar(
        const SnackBar(
          content: Text('Erreur lors de la suppression du type de gaz'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _updateGasCost(String gasName, String range, double cost) async {
    try {
      // Update gas cost using GasProvider
      final gasProvider = Provider.of<GasProvider>(context, listen: false);
      await gasProvider.updateGasCost(gasName, range, cost);

      // Update local state
      if (!mounted) return;
      setState(() {
        _gasTypes[gasName]![range] = cost;
        // Don't update controller here to avoid cursor jumping
      });
    } catch (e) {
      // Revert to previous value if save fails
      // The UI will show the old value since setState wasn't called
    }
  }

  Future<void> _saveConfiguration() async {
    final messenger = ScaffoldMessenger.of(context);
    final configProvider = Provider.of<ConfigurationProvider>(context, listen: false);
    final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
    final gasProvider = Provider.of<GasProvider>(context, listen: false);
    final calculatorProvider = Provider.of<CalculatorProvider>(context, listen: false);

    try {
      // Show loading indicator
      messenger.showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              ),
              SizedBox(width: 12),
              Text('Sauvegarde en cours...'),
            ],
          ),
          duration: Duration(seconds: 1),
        ),
      );

      int savedItems = 0;

      // Save setup fee and design cost to app settings
      await configProvider.setConfigValue('default_setup_fees', _setupFee.toString());
      await configProvider.setConfigValue('design_service_price', _designCost.toString());
      savedItems += 2;

      // Refresh calculator provider with updated design service price
      await calculatorProvider.refreshDesignServicePrice();

      // Save material rates using MaterialProvider
      // Ensure all current values are saved to database
      for (final entry in _materialRates.entries) {
        await materialProvider.updateMaterialRate(entry.key, entry.value);
        savedItems++;
      }

      // Save gas costs using GasProvider
      // Ensure all current values are saved to database
      for (final gasEntry in _gasTypes.entries) {
        final gasName = gasEntry.key;
        for (final rangeEntry in gasEntry.value.entries) {
          final rangeName = rangeEntry.key;
          final cost = rangeEntry.value;
          await gasProvider.updateGasCost(gasName, rangeName, cost);
          savedItems++;
        }
      }

      // Show success message with details
      messenger.showSnackBar(
        SnackBar(
          content: Text('Configuration sauvegardée avec succès! ($savedItems éléments)'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () => messenger.hideCurrentSnackBar(),
          ),
        ),
      );
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la sauvegarde: ${e.toString()}'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Fermer',
            textColor: Colors.white,
            onPressed: () => messenger.hideCurrentSnackBar(),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.grey[50]!, Colors.grey[100]!],
          ),
        ),
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Chargement de la configuration...'),
                  ],
                ),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue[600],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.settings,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Panneau de Configuration',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            'Définir la structure des coûts de découpe laser',
                            style: TextStyle(
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  ElevatedButton.icon(
                    onPressed: _saveConfiguration,
                    icon: const Icon(Icons.save),
                    label: const Text('Sauvegarder'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Material Rates Card
                  Expanded(
                    child: CustomCard(
                      title: 'Tarifs Matériaux (DH/heure)',
                      icon: Icons.timeline,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Définir les tarifs horaires par type de matériau',
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                          const SizedBox(height: 16),
                          ..._materialRates.entries.map((entry) {
                            final key = entry.key;
                            final isEditable = _materialEditable[key] ?? true;

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: Row(
                                children: [
                                  SizedBox(
                                    width: 80,
                                    child: Text(
                                      _materialLabels[key] ?? key.capitalize(),
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: TextFormField(
                                      controller: _materialControllers[key],
                                      enabled: isEditable,
                                      keyboardType: TextInputType.number,
                                      decoration: const InputDecoration(
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 8,
                                        ),
                                      ),
                                      onChanged: (value) {
                                        final newRate = double.tryParse(value);
                                        if (newRate != null) {
                                          _updateMaterialRate(key, newRate);
                                        }
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'DH/h',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  // Show delete button for all materials
                                  ...[
                                    const SizedBox(width: 8),
                                    IconButton(
                                      onPressed: () => _removeMaterial(key),
                                      icon: const Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                        size: 16,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }),
                          const Divider(),
                          const Text(
                            'Ajouter un nouveau matériau',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  decoration: const InputDecoration(
                                    hintText: 'Nom du matériau',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  onChanged: (value) => _newMaterialName = value,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextField(
                                  decoration: const InputDecoration(
                                    hintText: 'Tarif',
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) => _newMaterialRate = value,
                                ),
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: _addMaterial,
                                icon: const Icon(Icons.add),
                                style: IconButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 24),

                  // Setup Fee Card
                  Expanded(
                    child: CustomCard(
                      title: 'Frais de Configuration',
                      icon: Icons.monetization_on,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Frais fixes par opération de découpe',
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              const Text(
                                'Frais de configuration',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 12),
                              SizedBox(
                                width: 120,
                                child: TextFormField(
                                  controller: _setupFeeController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  onChanged: (value) {
                                    final fee = double.tryParse(value);
                                    if (fee != null) {
                                      setState(() {
                                        _setupFee = fee;
                                      });
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'DH',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Design Cost Configuration
                          Row(
                            children: [
                              const Text(
                                'Coût de conception',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 12),
                              SizedBox(
                                width: 120,
                                child: TextFormField(
                                  controller: _designCostController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    border: OutlineInputBorder(),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                  ),
                                  onChanged: (value) {
                                    final cost = double.tryParse(value);
                                    if (cost != null) {
                                      setState(() {
                                        _designCost = cost;
                                      });
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'DH',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Gas Costs Card
              CustomCard(
                title: 'Coûts des Gaz par Épaisseur',
                icon: Icons.air,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Définir les coûts des gaz selon le type et l\'épaisseur du matériau',
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 16),
                    ..._gasTypes.entries.map((gasEntry) {
                      final gasName = gasEntry.key;
                      final ranges = gasEntry.value;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _gasLabels[gasName] ?? gasName.capitalize(),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              // Show delete button for all gas types
                              IconButton(
                                onPressed: () => _removeGasType(gasName),
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                  size: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: ranges.entries.map((rangeEntry) {
                              final range = rangeEntry.key;

                              return Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 12),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        Provider.of<GasProvider>(context, listen: false).getThicknessRangeDisplayName(range),
                                        style: const TextStyle(
                                          fontSize: 10,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: TextFormField(
                                              controller: _gasControllers[gasName]?[range],
                                              keyboardType: TextInputType.number,
                                              decoration: const InputDecoration(
                                                border: OutlineInputBorder(),
                                                contentPadding: EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 4,
                                                ),
                                              ),
                                              style: const TextStyle(fontSize: 12),
                                              onChanged: (value) {
                                                final newCost = double.tryParse(value);
                                                if (newCost != null) {
                                                  _updateGasCost(gasName, range, newCost);
                                                }
                                              },
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          const Text(
                                            'DH',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                          const SizedBox(height: 16),
                        ],
                      );
                    }),
                    const Divider(),
                    const Text(
                      'Ajouter un nouveau type de gaz',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            decoration: const InputDecoration(
                              hintText: 'Nom du gaz (ex: Argon)',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                            ),
                            onChanged: (value) => _newGasName = value,
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _addGasType,
                          icon: const Icon(Icons.add),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Development Tools Section (Debug mode only)
              if (const bool.fromEnvironment('dart.vm.product') == false) ...[
                const SizedBox(height: 24),
                _buildDevelopmentSection(),
              ],
                  ],
                ),
              ),
      ),
    );
  }

  /// Build development tools section (debug mode only)
  Widget _buildDevelopmentSection() {
    return CustomCard(
      title: 'Outils de Développement',
      icon: Icons.developer_mode,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Base de données',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 12),
          
          // Database operation buttons
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ElevatedButton.icon(
                onPressed: () async {
                  await DevDatabaseUtils.checkStatus();
                  _showSnackBar('État de la base de données affiché dans la console');
                },
                icon: const Icon(Icons.info_outline, size: 18),
                label: const Text('Statut'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () async {
                  await DevDatabaseUtils.forceSeed();
                  _showSnackBar('Base de données re-semée avec succès');
                  _loadConfiguration(); // Reload configuration
                },
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Re-semer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () async {
                  await DevDatabaseUtils.resetDatabase();
                  _showSnackBar('Base de données réinitialisée');
                  _loadConfiguration(); // Reload configuration
                },
                icon: const Icon(Icons.restore, size: 18),
                label: const Text('Réinitialiser'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[600],
                  foregroundColor: Colors.white,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () async {
                  // Show confirmation dialog
                  final confirmed = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Confirmation'),
                      content: const Text(
                        'Êtes-vous sûr de vouloir supprimer toutes les données ? '
                        'Cette action est irréversible.',
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('Annuler'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text('Supprimer'),
                        ),
                      ],
                    ),
                  );
                  
                  if (confirmed == true) {
                    await DevDatabaseUtils.clearAllData();
                    _showSnackBar('Toutes les données supprimées');
                    _loadConfiguration(); // Reload configuration
                  }
                },
                icon: const Icon(Icons.delete_forever, size: 18),
                label: const Text('Tout supprimer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.warning_amber, color: Colors.orange[600], size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Ces outils sont uniquement disponibles en mode développement',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
