import 'package:drift/drift.dart';
import 'database.dart';
import 'daos.dart';
import 'tables.dart';

/// This file demonstrates how to use the normalized database structure
/// with the new Materials, GasTypes, ThicknessRanges, GasCosts, and AppSettings tables.
class DatabaseUsageExamples {
  final AppDatabase database;
  
  // DAOs for accessing normalized data
  late final MaterialsDao materialsDao;
  late final GasTypesDao gasTypesDao;
  late final ThicknessRangesDao thicknessRangesDao;
  late final GasCostsDao gasCostsDao;
  late final AppSettingsDao appSettingsDao;

  DatabaseUsageExamples(this.database) {
    materialsDao = MaterialsDao(database);
    gasTypesDao = GasTypesDao(database);
    thicknessRangesDao = ThicknessRangesDao(database);
    gasCostsDao = GasCostsDao(database);
    appSettingsDao = AppSettingsDao(database);
  }

  // ============================================================================
  // MATERIAL OPERATIONS
  // ============================================================================

  /// Example: Get all materials with their rates
  Future<Map<String, double>> getAllMaterialRates() async {
    return await materialsDao.getMaterialRates();
  }

  /// Example: Get the default material
  Future<MaterialData?> getDefaultMaterial() async {
    return await materialsDao.getDefaultMaterial();
  }

  /// Example: Add a new material
  Future<int> addNewMaterial(String name, String displayName, double rate, {bool isDefault = false}) async {
    return await materialsDao.insertMaterial(MaterialsCompanion(
      name: Value(name),
      displayName: Value(displayName),
      rate: Value(rate),
      isDefault: Value(isDefault),
    ));
  }

  /// Example: Update material rate
  Future<bool> updateMaterialRate(String materialName, double newRate) async {
    final material = await materialsDao.getMaterialByName(materialName);
    if (material == null) return false;

    return await materialsDao.updateMaterial(
      material.id,
      MaterialsCompanion(
        rate: Value(newRate),
        updatedAt: Value(DateTime.now()),
      ),
    );
  }

  // ============================================================================
  // GAS TYPE OPERATIONS
  // ============================================================================

  /// Example: Get all gas types
  Future<List<GasTypeData>> getAllGasTypes() async {
    return await gasTypesDao.getAllGasTypes();
  }

  /// Example: Get the default gas type
  Future<GasTypeData?> getDefaultGasType() async {
    return await gasTypesDao.getDefaultGasType();
  }

  /// Example: Add a new gas type
  Future<int> addNewGasType(String name, String displayName, {bool isDefault = false}) async {
    return await gasTypesDao.insertGasType(GasTypesCompanion(
      name: Value(name),
      displayName: Value(displayName),
      isDefault: Value(isDefault),
    ));
  }

  // ============================================================================
  // GAS COST OPERATIONS
  // ============================================================================

  /// Example: Get gas cost for specific gas type and thickness
  Future<double?> getGasCostForCutting(String gasTypeName, double thickness) async {
    return await gasCostsDao.getGasCostForThickness(gasTypeName, thickness);
  }

  /// Example: Get all gas costs with details (joined data)
  Future<List<GasCostWithDetails>> getAllGasCostsWithDetails() async {
    return await gasCostsDao.getAllGasCostsWithDetails();
  }

  /// Example: Update gas cost for specific gas type and thickness range
  Future<void> updateGasCost(String gasTypeName, String thicknessRangeName, double newCost) async {
    // Get gas type ID
    final gasType = await gasTypesDao.getGasTypeByName(gasTypeName);
    if (gasType == null) throw Exception('Gas type not found: $gasTypeName');

    // Get thickness range ID
    final thicknessRange = await thicknessRangesDao.getThicknessRangeByName(thicknessRangeName);
    if (thicknessRange == null) throw Exception('Thickness range not found: $thicknessRangeName');

    // Update or insert the gas cost
    await gasCostsDao.upsertGasCost(gasType.id, thicknessRange.id, newCost);
  }

  // ============================================================================
  // APP SETTINGS OPERATIONS
  // ============================================================================

  /// Example: Get pricing settings
  Future<Map<String, double?>> getPricingSettings() async {
    return {
      'design_service_price': await appSettingsDao.getDesignServicePrice(),
      'price_per_meter': await appSettingsDao.getPricePerMeter(),
      'tax_rate': await appSettingsDao.getTaxRate(),
      'min_order_amount': await appSettingsDao.getMinOrderAmount(),
    };
  }

  /// Example: Update design service price
  Future<void> updateDesignServicePrice(double newPrice) async {
    await appSettingsDao.upsertSetting(
      'pricing',
      'design_service_price',
      newPrice.toString(),
      'double',
      description: 'Price charged for design services',
    );
  }

  /// Example: Get app configuration
  Future<Map<String, String?>> getAppConfiguration() async {
    return {
      'currency': await appSettingsDao.getCurrency(),
      'app_version': await appSettingsDao.getAppVersion(),
    };
  }

  // ============================================================================
  // COMPLEX QUERIES AND CALCULATIONS
  // ============================================================================

  /// Example: Calculate total cost for a cutting job
  Future<Map<String, double>> calculateCuttingCost({
    required String materialName,
    required String gasTypeName,
    required double thickness,
    required double linearMeters,
    required double cuttingSpeed,
    required bool designProvided,
  }) async {
    // Get material rate
    final material = await materialsDao.getMaterialByName(materialName);
    if (material == null) throw Exception('Material not found: $materialName');

    // Get gas cost for thickness
    final gasCost = await gasCostsDao.getGasCostForThickness(gasTypeName, thickness);
    if (gasCost == null) throw Exception('Gas cost not found for $gasTypeName at ${thickness}mm');

    // Get pricing settings
    final pricePerMeter = await appSettingsDao.getPricePerMeter() ?? 15.0;
    final designServicePrice = await appSettingsDao.getDesignServicePrice() ?? 50.0;
    final taxRate = await appSettingsDao.getTaxRate() ?? 20.0;

    // Calculate costs
    final materialCost = material.rate * (thickness / 10.0) * linearMeters;
    final gasCostTotal = gasCost * linearMeters;
    final baseCost = pricePerMeter * linearMeters;
    final designCost = designProvided ? designServicePrice : 0.0;

    final subtotal = materialCost + gasCostTotal + baseCost + designCost;
    final taxAmount = subtotal * (taxRate / 100);
    final total = subtotal + taxAmount;

    return {
      'material_cost': materialCost,
      'gas_cost': gasCostTotal,
      'base_cost': baseCost,
      'design_cost': designCost,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'total': total,
    };
  }

  /// Example: Get all available thickness ranges
  Future<List<ThicknessRangeData>> getAvailableThicknessRanges() async {
    return await thicknessRangesDao.getAllThicknessRanges();
  }

  /// Example: Find appropriate thickness range for a given thickness
  Future<ThicknessRangeData?> findThicknessRange(double thickness) async {
    return await thicknessRangesDao.getThicknessRangeForValue(thickness);
  }

  // ============================================================================
  // BULK OPERATIONS
  // ============================================================================

  /// Example: Bulk update gas costs for a specific gas type
  Future<void> bulkUpdateGasCosts(String gasTypeName, Map<String, double> costs) async {
    final gasType = await gasTypesDao.getGasTypeByName(gasTypeName);
    if (gasType == null) throw Exception('Gas type not found: $gasTypeName');

    await database.transaction(() async {
      for (final entry in costs.entries) {
        final thicknessRangeName = entry.key;
        final cost = entry.value;

        final thicknessRange = await thicknessRangesDao.getThicknessRangeByName(thicknessRangeName);
        if (thicknessRange != null) {
          await gasCostsDao.upsertGasCost(gasType.id, thicknessRange.id, cost);
        }
      }
    });
  }

  /// Example: Get complete configuration summary
  Future<Map<String, dynamic>> getConfigurationSummary() async {
    final materials = await materialsDao.getAllMaterials();
    final gasTypes = await gasTypesDao.getAllGasTypes();
    final gasCosts = await gasCostsDao.getAllGasCostsWithDetails();
    final pricingSettings = await getPricingSettings();
    final appConfig = await getAppConfiguration();

    return {
      'materials': materials.map((m) => {
        'name': m.name,
        'display_name': m.displayName,
        'rate': m.rate,
        'is_default': m.isDefault,
      }).toList(),
      'gas_types': gasTypes.map((g) => {
        'name': g.name,
        'display_name': g.displayName,
        'is_default': g.isDefault,
      }).toList(),
      'gas_costs': gasCosts.map((gc) => {
        'gas_type': gc.gasType?.displayName,
        'thickness_range': gc.thicknessRange?.displayName,
        'cost_per_hour': gc.gasCost.costPerHour,
      }).toList(),
      'pricing_settings': pricingSettings,
      'app_configuration': appConfig,
    };
  }
}
