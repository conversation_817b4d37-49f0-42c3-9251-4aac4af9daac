import 'package:flutter/foundation.dart';
import '../services/seed_service.dart';
import '../services/database_service.dart';

class DatabaseSeeder {
  static Future<void> run({bool force = false}) async {
    if (kDebugMode) {
      print('Starting database seeding process...');
    }

    try {
      // Initialize database service
      final dbService = DatabaseService.instance;
      
      // Ensure database is ready
      final _ = dbService.database;

      // Run seeding
      await SeedService.instance.seedDatabase(force: force);

      if (kDebugMode) {
        print('Database seeding completed successfully!');
        
        // Print some statistics
        final stats = await dbService.getStatistics();
        print('Statistics after seeding:');
        print('- Total clients: ${stats['totalClients']}');
        print('- Total calculations: ${stats['totalCalculations']}');
        print('- Completed calculations: ${stats['completedCalculations']}');
        print('- Total revenue: €${stats['totalRevenue']?.toStringAsFixed(2)}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during database seeding: $e');
      }
      rethrow;
    }
  }

  /// Check if seeding is needed
  static Future<bool> needsSeeding() async {
    return !(await SeedService.instance.isDatabaseSeeded());
  }

  /// Get seed information
  static Future<Map<String, String?>> getSeedInfo() async {
    return await SeedService.instance.getSeedInfo();
  }

  /// Reset database to initial state
  static Future<void> reset() async {
    if (kDebugMode) {
      print('Resetting database to initial state...');
    }
    await SeedService.instance.resetToInitialState();
    if (kDebugMode) {
      print('Database reset completed!');
    }
  }
}
