import 'package:drift/drift.dart';

// ============================================================================
// EXISTING TABLES (for backward compatibility during migration)
// ============================================================================

// Clients table
@DataClassName('ClientData')
class Clients extends Table {
  TextColumn get id => text()();
  TextColumn get name => text()();
  TextColumn get reference => text()();
  TextColumn get company => text().nullable()();
  TextColumn get email => text().nullable()();
  TextColumn get phone => text().nullable()();
  IntColumn get projects => integer().withDefault(const Constant(0))();
  RealColumn get totalSpent => real().withDefault(const Constant(0.0))();
  DateTimeColumn get createdAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

// Calculations table
@DataClassName('CalculationData')
class Calculations extends Table {
  TextColumn get id => text()();
  TextColumn get clientName => text()();
  TextColumn get clientReference => text()();
  TextColumn get material => text()();
  RealColumn get thickness => real()();
  TextColumn get gasType => text()();
  RealColumn get linearMeters => real()();
  RealColumn get cuttingSpeed => real()();
  BoolColumn get designProvided => boolean()();
  TextColumn get designReference => text().nullable()();
  RealColumn get totalPrice => real()();
  DateTimeColumn get createdAt => dateTime()();
  TextColumn get status => text()(); // Will store enum as string

  @override
  Set<Column> get primaryKey => {id};
}

// Legacy configuration table (will be migrated to normalized tables)
@DataClassName('ConfigurationData')
class Configurations extends Table {
  TextColumn get key => text()();
  TextColumn get value => text()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {key};
}

// ============================================================================
// NORMALIZED TABLES
// ============================================================================

/// Materials table - stores material types and their rates
@DataClassName('MaterialData')
class Materials extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()();
  TextColumn get displayName => text().withLength(min: 1, max: 100)();
  RealColumn get rate => real()(); // Rate per hour in local currency
  BoolColumn get isDefault => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

/// Gas types table - stores different gas types used for cutting
@DataClassName('GasTypeData')
class GasTypes extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()(); // Internal name (e.g., 'oxygene_o2')
  TextColumn get displayName => text().withLength(min: 1, max: 100)(); // Display name (e.g., 'Oxygène (O₂)')
  BoolColumn get isDefault => boolean().withDefault(const Constant(false))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

/// Thickness ranges table - defines thickness ranges for gas cost calculation
@DataClassName('ThicknessRangeData')
class ThicknessRanges extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 50).unique()(); // Internal name (e.g., '1_5_mm')
  TextColumn get displayName => text().withLength(min: 1, max: 100)(); // Display name (e.g., '1-5 mm')
  RealColumn get minThickness => real()(); // Minimum thickness in mm (inclusive)
  RealColumn get maxThickness => real().nullable()(); // Maximum thickness in mm (exclusive), null for open-ended ranges
  IntColumn get sortOrder => integer()(); // For ordering ranges logically
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

/// Gas costs table - junction table storing costs for gas type + thickness range combinations
@DataClassName('GasCostData')
class GasCosts extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get gasTypeId => integer().references(GasTypes, #id, onDelete: KeyAction.cascade)();
  IntColumn get thicknessRangeId => integer().references(ThicknessRanges, #id, onDelete: KeyAction.cascade)();
  RealColumn get costPerHour => real()(); // Cost per hour in local currency
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  List<Set<Column>> get uniqueKeys => [
    {gasTypeId, thicknessRangeId}, // Ensure unique combination of gas type and thickness range
  ];
}

/// App settings table - stores general application configuration
@DataClassName('AppSettingData')
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get category => text().withLength(min: 1, max: 50)(); // Category for grouping settings
  TextColumn get key => text().withLength(min: 1, max: 100)(); // Setting key
  TextColumn get value => text()(); // Setting value (stored as string, parsed as needed)
  TextColumn get valueType => text().withLength(min: 1, max: 20)(); // Type hint: 'string', 'double', 'bool', 'datetime'
  TextColumn get description => text().nullable()(); // Optional description
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  List<Set<Column>> get uniqueKeys => [
    {category, key}, // Ensure unique combination of category and key
  ];
}

/// License information table - stores license validation data persistently
@DataClassName('LicenseData')
class Licenses extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get serialNumber => text().withLength(min: 1, max: 100)(); // License serial number
  TextColumn get hardwareId => text().withLength(min: 1, max: 100)(); // Hardware ID used for validation
  BoolColumn get isValid => boolean()(); // Current validation status
  TextColumn get responseCode => text().withLength(min: 1, max: 50)(); // Server response code (SUCCESS, ERROR, etc.)
  TextColumn get message => text()(); // Validation message from server
  DateTimeColumn get expiryDate => dateTime().nullable()(); // License expiry date from server
  DateTimeColumn get lastValidated => dateTime()(); // When this license was last validated
  DateTimeColumn get firstValidated => dateTime()(); // When this license was first validated
  TextColumn get serverUrl => text().nullable()(); // Custom server URL if used
  TextColumn get serverResponse => text().nullable()(); // Full JSON response from server for debugging
  BoolColumn get isActive => boolean().withDefault(const Constant(true))(); // Whether this license record is active
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  List<Set<Column>> get uniqueKeys => [
    {serialNumber}, // Each serial number should be unique
  ];
}