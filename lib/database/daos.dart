import 'package:drift/drift.dart';
import 'database.dart';
import 'tables.dart';

part 'daos.g.dart';

// ============================================================================
// MATERIALS DAO
// ============================================================================

@DriftAccessor(tables: [Materials])
class MaterialsDao extends DatabaseAccessor<AppDatabase> with _$MaterialsDaoMixin {
  MaterialsDao(AppDatabase db) : super(db);

  /// Get all materials ordered by name
  Future<List<MaterialData>> getAllMaterials() async {
    return await (select(materials)..orderBy([(m) => OrderingTerm.asc(m.name)])).get();
  }

  /// Get material by ID
  Future<MaterialData?> getMaterialById(int id) async {
    return await (select(materials)..where((m) => m.id.equals(id))).getSingleOrNull();
  }

  /// Get material by name
  Future<MaterialData?> getMaterialByName(String name) async {
    return await (select(materials)..where((m) => m.name.equals(name))).getSingleOrNull();
  }

  /// Get the default material
  Future<MaterialData?> getDefaultMaterial() async {
    return await (select(materials)..where((m) => m.isDefault.equals(true))).getSingleOrNull();
  }

  /// Insert a new material
  Future<int> insertMaterial(MaterialsCompanion material) async {
    return await into(materials).insert(material);
  }

  /// Update a material
  Future<bool> updateMaterial(int id, MaterialsCompanion material) async {
    final rowsAffected = await (update(materials)..where((m) => m.id.equals(id))).write(material);
    return rowsAffected > 0;
  }

  /// Delete a material
  Future<int> deleteMaterial(int id) async {
    return await (delete(materials)..where((m) => m.id.equals(id))).go();
  }

  /// Set a material as default (and unset others)
  Future<void> setDefaultMaterial(int id) async {
    await transaction(() async {
      // First, unset all defaults
      await (update(materials)).write(const MaterialsCompanion(isDefault: Value(false)));
      // Then set the specified material as default
      await (update(materials)..where((m) => m.id.equals(id)))
          .write(const MaterialsCompanion(isDefault: Value(true)));
    });
  }

  /// Get materials with their rates for display
  Future<Map<String, double>> getMaterialRates() async {
    final materialList = await getAllMaterials();
    return Map.fromEntries(
      materialList.map((m) => MapEntry(m.name, m.rate)),
    );
  }
}

// ============================================================================
// GAS TYPES DAO
// ============================================================================

@DriftAccessor(tables: [GasTypes])
class GasTypesDao extends DatabaseAccessor<AppDatabase> with _$GasTypesDaoMixin {
  GasTypesDao(AppDatabase db) : super(db);

  /// Get all gas types ordered by name
  Future<List<GasTypeData>> getAllGasTypes() async {
    return await (select(gasTypes)..orderBy([(g) => OrderingTerm.asc(g.name)])).get();
  }

  /// Get gas type by ID
  Future<GasTypeData?> getGasTypeById(int id) async {
    return await (select(gasTypes)..where((g) => g.id.equals(id))).getSingleOrNull();
  }

  /// Get gas type by name
  Future<GasTypeData?> getGasTypeByName(String name) async {
    return await (select(gasTypes)..where((g) => g.name.equals(name))).getSingleOrNull();
  }

  /// Get gas type by display name
  Future<GasTypeData?> getGasTypeByDisplayName(String displayName) async {
    return await (select(gasTypes)..where((g) => g.displayName.equals(displayName))).getSingleOrNull();
  }

  /// Get the default gas type
  Future<GasTypeData?> getDefaultGasType() async {
    return await (select(gasTypes)..where((g) => g.isDefault.equals(true))).getSingleOrNull();
  }

  /// Insert a new gas type
  Future<int> insertGasType(GasTypesCompanion gasType) async {
    return await into(gasTypes).insert(gasType);
  }

  /// Update a gas type
  Future<bool> updateGasType(int id, GasTypesCompanion gasType) async {
    final rowsAffected = await (update(gasTypes)..where((g) => g.id.equals(id))).write(gasType);
    return rowsAffected > 0;
  }

  /// Delete a gas type
  Future<int> deleteGasType(int id) async {
    return await (delete(gasTypes)..where((g) => g.id.equals(id))).go();
  }

  /// Set a gas type as default (and unset others)
  Future<void> setDefaultGasType(int id) async {
    await transaction(() async {
      // First, unset all defaults
      await (update(gasTypes)).write(const GasTypesCompanion(isDefault: Value(false)));
      // Then set the specified gas type as default
      await (update(gasTypes)..where((g) => g.id.equals(id)))
          .write(const GasTypesCompanion(isDefault: Value(true)));
    });
  }
}

// ============================================================================
// THICKNESS RANGES DAO
// ============================================================================

@DriftAccessor(tables: [ThicknessRanges])
class ThicknessRangesDao extends DatabaseAccessor<AppDatabase> with _$ThicknessRangesDaoMixin {
  ThicknessRangesDao(AppDatabase db) : super(db);

  /// Get all thickness ranges ordered by sort order
  Future<List<ThicknessRangeData>> getAllThicknessRanges() async {
    return await (select(thicknessRanges)..orderBy([(t) => OrderingTerm.asc(t.sortOrder)])).get();
  }

  /// Get thickness range by ID
  Future<ThicknessRangeData?> getThicknessRangeById(int id) async {
    return await (select(thicknessRanges)..where((t) => t.id.equals(id))).getSingleOrNull();
  }

  /// Get thickness range by name
  Future<ThicknessRangeData?> getThicknessRangeByName(String name) async {
    return await (select(thicknessRanges)..where((t) => t.name.equals(name))).getSingleOrNull();
  }

  /// Find the appropriate thickness range for a given thickness value
  Future<ThicknessRangeData?> getThicknessRangeForValue(double thickness) async {
    final ranges = await getAllThicknessRanges();

    for (final range in ranges) {
      final minThickness = range.minThickness;
      final maxThickness = range.maxThickness;

      if (thickness >= minThickness) {
        if (maxThickness == null || thickness < maxThickness) {
          return range;
        }
      }
    }

    return null; // No suitable range found
  }

  /// Insert a new thickness range
  Future<int> insertThicknessRange(ThicknessRangesCompanion thicknessRange) async {
    return await into(thicknessRanges).insert(thicknessRange);
  }

  /// Update a thickness range
  Future<bool> updateThicknessRange(int id, ThicknessRangesCompanion thicknessRange) async {
    final rowsAffected = await (update(thicknessRanges)..where((t) => t.id.equals(id))).write(thicknessRange);
    return rowsAffected > 0;
  }

  /// Delete a thickness range
  Future<int> deleteThicknessRange(int id) async {
    return await (delete(thicknessRanges)..where((t) => t.id.equals(id))).go();
  }
}

// ============================================================================
// GAS COSTS DAO
// ============================================================================

@DriftAccessor(tables: [GasCosts, GasTypes, ThicknessRanges])
class GasCostsDao extends DatabaseAccessor<AppDatabase> with _$GasCostsDaoMixin {
  GasCostsDao(AppDatabase db) : super(db);

  /// Get all gas costs with joined gas type and thickness range data
  Future<List<GasCostWithDetails>> getAllGasCostsWithDetails() async {
    final query = select(gasCosts).join([
      leftOuterJoin(gasTypes, gasTypes.id.equalsExp(gasCosts.gasTypeId)),
      leftOuterJoin(thicknessRanges, thicknessRanges.id.equalsExp(gasCosts.thicknessRangeId)),
    ]);

    final results = await query.get();
    return results.map((row) => GasCostWithDetails(
      gasCost: row.readTable(gasCosts),
      gasType: row.readTableOrNull(gasTypes),
      thicknessRange: row.readTableOrNull(thicknessRanges),
    )).toList();
  }

  /// Get gas costs for a specific gas type
  Future<List<GasCostWithDetails>> getGasCostsForGasType(int gasTypeId) async {
    final query = (select(gasCosts)..where((g) => g.gasTypeId.equals(gasTypeId))).join([
      leftOuterJoin(gasTypes, gasTypes.id.equalsExp(gasCosts.gasTypeId)),
      leftOuterJoin(thicknessRanges, thicknessRanges.id.equalsExp(gasCosts.thicknessRangeId)),
    ]);

    final results = await query.get();
    return results.map((row) => GasCostWithDetails(
      gasCost: row.readTable(gasCosts),
      gasType: row.readTableOrNull(gasTypes),
      thicknessRange: row.readTableOrNull(thicknessRanges),
    )).toList();
  }

  /// Get gas costs for a specific gas type by name
  Future<List<GasCostWithDetails>> getGasCostsForGasTypeName(String gasTypeName) async {
    final query = select(gasCosts).join([
      innerJoin(gasTypes, gasTypes.id.equalsExp(gasCosts.gasTypeId) & gasTypes.name.equals(gasTypeName)),
      leftOuterJoin(thicknessRanges, thicknessRanges.id.equalsExp(gasCosts.thicknessRangeId)),
    ]);

    final results = await query.get();
    return results.map((row) => GasCostWithDetails(
      gasCost: row.readTable(gasCosts),
      gasType: row.readTableOrNull(gasTypes),
      thicknessRange: row.readTableOrNull(thicknessRanges),
    )).toList();
  }

  /// Get gas cost for specific gas type and thickness
  Future<double?> getGasCostForThickness(String gasTypeName, double thickness) async {
    // First, find the appropriate thickness range
    final thicknessRangesDao = ThicknessRangesDao(db);
    final thicknessRange = await thicknessRangesDao.getThicknessRangeForValue(thickness);
    if (thicknessRange == null) return null;

    // Then find the gas cost for this gas type and thickness range
    final query = select(gasCosts).join([
      innerJoin(gasTypes, gasTypes.id.equalsExp(gasCosts.gasTypeId) & gasTypes.name.equals(gasTypeName)),
      innerJoin(thicknessRanges, thicknessRanges.id.equalsExp(gasCosts.thicknessRangeId) & thicknessRanges.id.equals(thicknessRange.id)),
    ]);

    final result = await query.getSingleOrNull();
    return result?.readTable(gasCosts).costPerHour;
  }

  /// Insert a new gas cost
  Future<int> insertGasCost(GasCostsCompanion gasCost) async {
    return await into(gasCosts).insert(gasCost);
  }

  /// Update a gas cost
  Future<bool> updateGasCost(int id, GasCostsCompanion gasCost) async {
    final rowsAffected = await (update(gasCosts)..where((g) => g.id.equals(id))).write(gasCost);
    return rowsAffected > 0;
  }

  /// Delete a gas cost
  Future<int> deleteGasCost(int id) async {
    return await (delete(gasCosts)..where((g) => g.id.equals(id))).go();
  }

  /// Update or insert gas cost for specific gas type and thickness range
  Future<void> upsertGasCost(int gasTypeId, int thicknessRangeId, double costPerHour) async {
    // First try to find existing record
    final existing = await (select(gasCosts)
      ..where((g) => g.gasTypeId.equals(gasTypeId) & g.thicknessRangeId.equals(thicknessRangeId))
    ).getSingleOrNull();

    if (existing != null) {
      // Update existing record
      await (update(gasCosts)..where((g) => g.id.equals(existing.id))).write(GasCostsCompanion(
        costPerHour: Value(costPerHour),
        updatedAt: Value(DateTime.now()),
      ));
    } else {
      // Insert new record
      await into(gasCosts).insert(GasCostsCompanion(
        gasTypeId: Value(gasTypeId),
        thicknessRangeId: Value(thicknessRangeId),
        costPerHour: Value(costPerHour),
        updatedAt: Value(DateTime.now()),
      ));
    }
  }
}

// ============================================================================
// LICENSE DAO
// ============================================================================

@DriftAccessor(tables: [Licenses])
class LicensesDao extends DatabaseAccessor<AppDatabase> with _$LicensesDaoMixin {
  LicensesDao(AppDatabase db) : super(db);

  /// Get the current active license
  Future<LicenseData?> getCurrentLicense() async {
    return await (select(licenses)
          ..where((l) => l.isActive.equals(true))
          ..orderBy([(l) => OrderingTerm.desc(l.lastValidated)])
          ..limit(1))
        .getSingleOrNull();
  }

  /// Get license by serial number
  Future<LicenseData?> getLicenseBySerial(String serialNumber) async {
    return await (select(licenses)
          ..where((l) => l.serialNumber.equals(serialNumber)))
        .getSingleOrNull();
  }

  /// Save or update license validation result
  Future<LicenseData> saveLicenseValidation({
    required String serialNumber,
    required String hardwareId,
    required bool isValid,
    required String responseCode,
    required String message,
    DateTime? expiryDate,
    String? serverUrl,
    String? serverResponse,
  }) async {
    final now = DateTime.now();

    // Check if license already exists
    final existingLicense = await getLicenseBySerial(serialNumber);

    if (existingLicense != null) {
      // Update existing license
      final updatedLicense = existingLicense.copyWith(
        hardwareId: hardwareId,
        isValid: isValid,
        responseCode: responseCode,
        message: message,
        expiryDate: Value(expiryDate),
        lastValidated: now,
        serverUrl: Value(serverUrl),
        serverResponse: Value(serverResponse),
        isActive: true,
        updatedAt: now,
      );

      await update(licenses).replace(updatedLicense);
      return updatedLicense;
    } else {
      // Create new license record
      final newLicense = LicensesCompanion(
        serialNumber: Value(serialNumber),
        hardwareId: Value(hardwareId),
        isValid: Value(isValid),
        responseCode: Value(responseCode),
        message: Value(message),
        expiryDate: Value(expiryDate),
        lastValidated: Value(now),
        firstValidated: Value(now),
        serverUrl: Value(serverUrl),
        serverResponse: Value(serverResponse),
        isActive: const Value(true),
      );

      final id = await into(licenses).insert(newLicense);
      return await (select(licenses)..where((l) => l.id.equals(id))).getSingle();
    }
  }

  /// Deactivate all licenses (useful when clearing license data)
  Future<void> deactivateAllLicenses() async {
    await (update(licenses)..where((l) => l.isActive.equals(true)))
        .write(const LicensesCompanion(isActive: Value(false)));
  }

  /// Get all license history
  Future<List<LicenseData>> getLicenseHistory() async {
    return await (select(licenses)
          ..orderBy([(l) => OrderingTerm.desc(l.lastValidated)]))
        .get();
  }

  /// Delete old license records (keep only the last 10)
  Future<void> cleanupOldLicenses() async {
    final allLicenses = await getLicenseHistory();
    if (allLicenses.length > 10) {
      final toDelete = allLicenses.skip(10).map((l) => l.id).toList();
      await (delete(licenses)..where((l) => l.id.isIn(toDelete))).go();
    }
  }

  /// Check if license is expired based on stored expiry date
  Future<bool> isCurrentLicenseExpired() async {
    final currentLicense = await getCurrentLicense();
    if (currentLicense?.expiryDate == null) return false;
    return DateTime.now().isAfter(currentLicense!.expiryDate!);
  }

  /// Get days until expiry for current license
  Future<int?> getDaysUntilExpiry() async {
    final currentLicense = await getCurrentLicense();
    if (currentLicense?.expiryDate == null) return null;
    return currentLicense!.expiryDate!.difference(DateTime.now()).inDays;
  }
}

// ============================================================================
// APP SETTINGS DAO
// ============================================================================

@DriftAccessor(tables: [AppSettings])
class AppSettingsDao extends DatabaseAccessor<AppDatabase> with _$AppSettingsDaoMixin {
  AppSettingsDao(AppDatabase db) : super(db);

  /// Get all settings
  Future<List<AppSettingData>> getAllSettings() async {
    return await (select(appSettings)..orderBy([(s) => OrderingTerm.asc(s.category), (s) => OrderingTerm.asc(s.key)])).get();
  }

  /// Get settings by category
  Future<List<AppSettingData>> getSettingsByCategory(String category) async {
    return await (select(appSettings)..where((s) => s.category.equals(category))).get();
  }

  /// Get setting by key
  Future<AppSettingData?> getSettingByKey(String key) async {
    return await (select(appSettings)..where((s) => s.key.equals(key))).getSingleOrNull();
  }

  /// Get setting by category and key
  Future<AppSettingData?> getSettingByCategoryAndKey(String category, String key) async {
    return await (select(appSettings)..where((s) => s.category.equals(category) & s.key.equals(key))).getSingleOrNull();
  }

  /// Get setting value as string
  Future<String?> getSettingValue(String category, String key) async {
    final setting = await getSettingByCategoryAndKey(category, key);
    return setting?.value;
  }

  /// Get setting value as double
  Future<double?> getSettingValueAsDouble(String category, String key) async {
    final value = await getSettingValue(category, key);
    return value != null ? double.tryParse(value) : null;
  }

  /// Get setting value as bool
  Future<bool?> getSettingValueAsBool(String category, String key) async {
    final value = await getSettingValue(category, key);
    if (value == null) return null;
    return value.toLowerCase() == 'true' || value == '1';
  }

  /// Get setting value as DateTime
  Future<DateTime?> getSettingValueAsDateTime(String category, String key) async {
    final value = await getSettingValue(category, key);
    return value != null ? DateTime.tryParse(value) : null;
  }

  /// Insert a new setting
  Future<int> insertSetting(AppSettingsCompanion setting) async {
    return await into(appSettings).insert(setting);
  }

  /// Update a setting
  Future<bool> updateSetting(int id, AppSettingsCompanion setting) async {
    final rowsAffected = await (update(appSettings)..where((s) => s.id.equals(id))).write(setting);
    return rowsAffected > 0;
  }

  /// Delete a setting
  Future<int> deleteSetting(int id) async {
    return await (delete(appSettings)..where((s) => s.id.equals(id))).go();
  }

  /// Update or insert setting value
  Future<void> upsertSetting(String category, String key, String value, String valueType, {String? description}) async {
    try {
      // Try to update first
      final rowsAffected = await (update(appSettings)..where((s) => s.category.equals(category) & s.key.equals(key))).write(
        AppSettingsCompanion(
          value: Value(value),
          valueType: Value(valueType),
          description: Value(description),
          updatedAt: Value(DateTime.now()),
        ),
      );

      // If no rows were affected, the setting doesn't exist, so insert it
      if (rowsAffected == 0) {
        try {
          await into(appSettings).insert(AppSettingsCompanion(
            category: Value(category),
            key: Value(key),
            value: Value(value),
            valueType: Value(valueType),
            description: Value(description),
            updatedAt: Value(DateTime.now()),
          ));
        } catch (e) {
          // If insert fails due to unique constraint (race condition),
          // try update again as another thread might have inserted it
          if (e.toString().contains('UNIQUE constraint failed')) {
            await (update(appSettings)..where((s) => s.category.equals(category) & s.key.equals(key))).write(
              AppSettingsCompanion(
                value: Value(value),
                valueType: Value(valueType),
                description: Value(description),
                updatedAt: Value(DateTime.now()),
              ),
            );
          } else {
            rethrow;
          }
        }
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Convenience methods for common settings
  Future<double?> getDesignServicePrice() async {
    return await getSettingValueAsDouble('pricing', 'design_service_price');
  }

  Future<double?> getPricePerMeter() async {
    return await getSettingValueAsDouble('pricing', 'price_per_meter');
  }

  Future<double?> getTaxRate() async {
    return await getSettingValueAsDouble('pricing', 'tax_rate');
  }

  Future<double?> getMinOrderAmount() async {
    return await getSettingValueAsDouble('pricing', 'min_order_amount');
  }

  Future<String?> getCurrency() async {
    return await getSettingValue('app', 'currency');
  }

  Future<String?> getAppVersion() async {
    return await getSettingValue('app', 'app_version');
  }
}

// ============================================================================
// HELPER CLASSES
// ============================================================================

/// Helper class to hold gas cost data with related gas type and thickness range information
class GasCostWithDetails {
  final GasCostData gasCost;
  final GasTypeData? gasType;
  final ThicknessRangeData? thicknessRange;

  GasCostWithDetails({
    required this.gasCost,
    this.gasType,
    this.thicknessRange,
  });

  @override
  String toString() {
    return 'GasCostWithDetails(gasCost: ${gasCost.id}, gasType: ${gasType?.displayName}, thicknessRange: ${thicknessRange?.displayName}, cost: ${gasCost.costPerHour})';
  }
}