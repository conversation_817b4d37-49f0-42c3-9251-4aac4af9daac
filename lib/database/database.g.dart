// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $ClientsTable extends Clients with TableInfo<$ClientsTable, ClientData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ClientsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _referenceMeta =
      const VerificationMeta('reference');
  @override
  late final GeneratedColumn<String> reference = GeneratedColumn<String>(
      'reference', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _companyMeta =
      const VerificationMeta('company');
  @override
  late final GeneratedColumn<String> company = GeneratedColumn<String>(
      'company', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _phoneMeta = const VerificationMeta('phone');
  @override
  late final GeneratedColumn<String> phone = GeneratedColumn<String>(
      'phone', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _projectsMeta =
      const VerificationMeta('projects');
  @override
  late final GeneratedColumn<int> projects = GeneratedColumn<int>(
      'projects', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _totalSpentMeta =
      const VerificationMeta('totalSpent');
  @override
  late final GeneratedColumn<double> totalSpent = GeneratedColumn<double>(
      'total_spent', aliasedName, false,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      defaultValue: const Constant(0.0));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        name,
        reference,
        company,
        email,
        phone,
        projects,
        totalSpent,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'clients';
  @override
  VerificationContext validateIntegrity(Insertable<ClientData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('reference')) {
      context.handle(_referenceMeta,
          reference.isAcceptableOrUnknown(data['reference']!, _referenceMeta));
    } else if (isInserting) {
      context.missing(_referenceMeta);
    }
    if (data.containsKey('company')) {
      context.handle(_companyMeta,
          company.isAcceptableOrUnknown(data['company']!, _companyMeta));
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    }
    if (data.containsKey('phone')) {
      context.handle(
          _phoneMeta, phone.isAcceptableOrUnknown(data['phone']!, _phoneMeta));
    }
    if (data.containsKey('projects')) {
      context.handle(_projectsMeta,
          projects.isAcceptableOrUnknown(data['projects']!, _projectsMeta));
    }
    if (data.containsKey('total_spent')) {
      context.handle(
          _totalSpentMeta,
          totalSpent.isAcceptableOrUnknown(
              data['total_spent']!, _totalSpentMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ClientData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ClientData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      reference: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}reference'])!,
      company: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}company']),
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email']),
      phone: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}phone']),
      projects: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}projects'])!,
      totalSpent: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}total_spent'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $ClientsTable createAlias(String alias) {
    return $ClientsTable(attachedDatabase, alias);
  }
}

class ClientData extends DataClass implements Insertable<ClientData> {
  final String id;
  final String name;
  final String reference;
  final String? company;
  final String? email;
  final String? phone;
  final int projects;
  final double totalSpent;
  final DateTime createdAt;
  const ClientData(
      {required this.id,
      required this.name,
      required this.reference,
      this.company,
      this.email,
      this.phone,
      required this.projects,
      required this.totalSpent,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['name'] = Variable<String>(name);
    map['reference'] = Variable<String>(reference);
    if (!nullToAbsent || company != null) {
      map['company'] = Variable<String>(company);
    }
    if (!nullToAbsent || email != null) {
      map['email'] = Variable<String>(email);
    }
    if (!nullToAbsent || phone != null) {
      map['phone'] = Variable<String>(phone);
    }
    map['projects'] = Variable<int>(projects);
    map['total_spent'] = Variable<double>(totalSpent);
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  ClientsCompanion toCompanion(bool nullToAbsent) {
    return ClientsCompanion(
      id: Value(id),
      name: Value(name),
      reference: Value(reference),
      company: company == null && nullToAbsent
          ? const Value.absent()
          : Value(company),
      email:
          email == null && nullToAbsent ? const Value.absent() : Value(email),
      phone:
          phone == null && nullToAbsent ? const Value.absent() : Value(phone),
      projects: Value(projects),
      totalSpent: Value(totalSpent),
      createdAt: Value(createdAt),
    );
  }

  factory ClientData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ClientData(
      id: serializer.fromJson<String>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      reference: serializer.fromJson<String>(json['reference']),
      company: serializer.fromJson<String?>(json['company']),
      email: serializer.fromJson<String?>(json['email']),
      phone: serializer.fromJson<String?>(json['phone']),
      projects: serializer.fromJson<int>(json['projects']),
      totalSpent: serializer.fromJson<double>(json['totalSpent']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'name': serializer.toJson<String>(name),
      'reference': serializer.toJson<String>(reference),
      'company': serializer.toJson<String?>(company),
      'email': serializer.toJson<String?>(email),
      'phone': serializer.toJson<String?>(phone),
      'projects': serializer.toJson<int>(projects),
      'totalSpent': serializer.toJson<double>(totalSpent),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  ClientData copyWith(
          {String? id,
          String? name,
          String? reference,
          Value<String?> company = const Value.absent(),
          Value<String?> email = const Value.absent(),
          Value<String?> phone = const Value.absent(),
          int? projects,
          double? totalSpent,
          DateTime? createdAt}) =>
      ClientData(
        id: id ?? this.id,
        name: name ?? this.name,
        reference: reference ?? this.reference,
        company: company.present ? company.value : this.company,
        email: email.present ? email.value : this.email,
        phone: phone.present ? phone.value : this.phone,
        projects: projects ?? this.projects,
        totalSpent: totalSpent ?? this.totalSpent,
        createdAt: createdAt ?? this.createdAt,
      );
  ClientData copyWithCompanion(ClientsCompanion data) {
    return ClientData(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      reference: data.reference.present ? data.reference.value : this.reference,
      company: data.company.present ? data.company.value : this.company,
      email: data.email.present ? data.email.value : this.email,
      phone: data.phone.present ? data.phone.value : this.phone,
      projects: data.projects.present ? data.projects.value : this.projects,
      totalSpent:
          data.totalSpent.present ? data.totalSpent.value : this.totalSpent,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ClientData(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('reference: $reference, ')
          ..write('company: $company, ')
          ..write('email: $email, ')
          ..write('phone: $phone, ')
          ..write('projects: $projects, ')
          ..write('totalSpent: $totalSpent, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name, reference, company, email, phone,
      projects, totalSpent, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ClientData &&
          other.id == this.id &&
          other.name == this.name &&
          other.reference == this.reference &&
          other.company == this.company &&
          other.email == this.email &&
          other.phone == this.phone &&
          other.projects == this.projects &&
          other.totalSpent == this.totalSpent &&
          other.createdAt == this.createdAt);
}

class ClientsCompanion extends UpdateCompanion<ClientData> {
  final Value<String> id;
  final Value<String> name;
  final Value<String> reference;
  final Value<String?> company;
  final Value<String?> email;
  final Value<String?> phone;
  final Value<int> projects;
  final Value<double> totalSpent;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const ClientsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.reference = const Value.absent(),
    this.company = const Value.absent(),
    this.email = const Value.absent(),
    this.phone = const Value.absent(),
    this.projects = const Value.absent(),
    this.totalSpent = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ClientsCompanion.insert({
    required String id,
    required String name,
    required String reference,
    this.company = const Value.absent(),
    this.email = const Value.absent(),
    this.phone = const Value.absent(),
    this.projects = const Value.absent(),
    this.totalSpent = const Value.absent(),
    required DateTime createdAt,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        name = Value(name),
        reference = Value(reference),
        createdAt = Value(createdAt);
  static Insertable<ClientData> custom({
    Expression<String>? id,
    Expression<String>? name,
    Expression<String>? reference,
    Expression<String>? company,
    Expression<String>? email,
    Expression<String>? phone,
    Expression<int>? projects,
    Expression<double>? totalSpent,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (reference != null) 'reference': reference,
      if (company != null) 'company': company,
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
      if (projects != null) 'projects': projects,
      if (totalSpent != null) 'total_spent': totalSpent,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ClientsCompanion copyWith(
      {Value<String>? id,
      Value<String>? name,
      Value<String>? reference,
      Value<String?>? company,
      Value<String?>? email,
      Value<String?>? phone,
      Value<int>? projects,
      Value<double>? totalSpent,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return ClientsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      reference: reference ?? this.reference,
      company: company ?? this.company,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      projects: projects ?? this.projects,
      totalSpent: totalSpent ?? this.totalSpent,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (reference.present) {
      map['reference'] = Variable<String>(reference.value);
    }
    if (company.present) {
      map['company'] = Variable<String>(company.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (phone.present) {
      map['phone'] = Variable<String>(phone.value);
    }
    if (projects.present) {
      map['projects'] = Variable<int>(projects.value);
    }
    if (totalSpent.present) {
      map['total_spent'] = Variable<double>(totalSpent.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ClientsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('reference: $reference, ')
          ..write('company: $company, ')
          ..write('email: $email, ')
          ..write('phone: $phone, ')
          ..write('projects: $projects, ')
          ..write('totalSpent: $totalSpent, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CalculationsTable extends Calculations
    with TableInfo<$CalculationsTable, CalculationData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CalculationsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _clientNameMeta =
      const VerificationMeta('clientName');
  @override
  late final GeneratedColumn<String> clientName = GeneratedColumn<String>(
      'client_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _clientReferenceMeta =
      const VerificationMeta('clientReference');
  @override
  late final GeneratedColumn<String> clientReference = GeneratedColumn<String>(
      'client_reference', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _materialMeta =
      const VerificationMeta('material');
  @override
  late final GeneratedColumn<String> material = GeneratedColumn<String>(
      'material', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _thicknessMeta =
      const VerificationMeta('thickness');
  @override
  late final GeneratedColumn<double> thickness = GeneratedColumn<double>(
      'thickness', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _gasTypeMeta =
      const VerificationMeta('gasType');
  @override
  late final GeneratedColumn<String> gasType = GeneratedColumn<String>(
      'gas_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _linearMetersMeta =
      const VerificationMeta('linearMeters');
  @override
  late final GeneratedColumn<double> linearMeters = GeneratedColumn<double>(
      'linear_meters', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _cuttingSpeedMeta =
      const VerificationMeta('cuttingSpeed');
  @override
  late final GeneratedColumn<double> cuttingSpeed = GeneratedColumn<double>(
      'cutting_speed', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _designProvidedMeta =
      const VerificationMeta('designProvided');
  @override
  late final GeneratedColumn<bool> designProvided = GeneratedColumn<bool>(
      'design_provided', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("design_provided" IN (0, 1))'));
  static const VerificationMeta _designReferenceMeta =
      const VerificationMeta('designReference');
  @override
  late final GeneratedColumn<String> designReference = GeneratedColumn<String>(
      'design_reference', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _totalPriceMeta =
      const VerificationMeta('totalPrice');
  @override
  late final GeneratedColumn<double> totalPrice = GeneratedColumn<double>(
      'total_price', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        clientName,
        clientReference,
        material,
        thickness,
        gasType,
        linearMeters,
        cuttingSpeed,
        designProvided,
        designReference,
        totalPrice,
        createdAt,
        status
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'calculations';
  @override
  VerificationContext validateIntegrity(Insertable<CalculationData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('client_name')) {
      context.handle(
          _clientNameMeta,
          clientName.isAcceptableOrUnknown(
              data['client_name']!, _clientNameMeta));
    } else if (isInserting) {
      context.missing(_clientNameMeta);
    }
    if (data.containsKey('client_reference')) {
      context.handle(
          _clientReferenceMeta,
          clientReference.isAcceptableOrUnknown(
              data['client_reference']!, _clientReferenceMeta));
    } else if (isInserting) {
      context.missing(_clientReferenceMeta);
    }
    if (data.containsKey('material')) {
      context.handle(_materialMeta,
          material.isAcceptableOrUnknown(data['material']!, _materialMeta));
    } else if (isInserting) {
      context.missing(_materialMeta);
    }
    if (data.containsKey('thickness')) {
      context.handle(_thicknessMeta,
          thickness.isAcceptableOrUnknown(data['thickness']!, _thicknessMeta));
    } else if (isInserting) {
      context.missing(_thicknessMeta);
    }
    if (data.containsKey('gas_type')) {
      context.handle(_gasTypeMeta,
          gasType.isAcceptableOrUnknown(data['gas_type']!, _gasTypeMeta));
    } else if (isInserting) {
      context.missing(_gasTypeMeta);
    }
    if (data.containsKey('linear_meters')) {
      context.handle(
          _linearMetersMeta,
          linearMeters.isAcceptableOrUnknown(
              data['linear_meters']!, _linearMetersMeta));
    } else if (isInserting) {
      context.missing(_linearMetersMeta);
    }
    if (data.containsKey('cutting_speed')) {
      context.handle(
          _cuttingSpeedMeta,
          cuttingSpeed.isAcceptableOrUnknown(
              data['cutting_speed']!, _cuttingSpeedMeta));
    } else if (isInserting) {
      context.missing(_cuttingSpeedMeta);
    }
    if (data.containsKey('design_provided')) {
      context.handle(
          _designProvidedMeta,
          designProvided.isAcceptableOrUnknown(
              data['design_provided']!, _designProvidedMeta));
    } else if (isInserting) {
      context.missing(_designProvidedMeta);
    }
    if (data.containsKey('design_reference')) {
      context.handle(
          _designReferenceMeta,
          designReference.isAcceptableOrUnknown(
              data['design_reference']!, _designReferenceMeta));
    }
    if (data.containsKey('total_price')) {
      context.handle(
          _totalPriceMeta,
          totalPrice.isAcceptableOrUnknown(
              data['total_price']!, _totalPriceMeta));
    } else if (isInserting) {
      context.missing(_totalPriceMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CalculationData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CalculationData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      clientName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}client_name'])!,
      clientReference: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}client_reference'])!,
      material: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}material'])!,
      thickness: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}thickness'])!,
      gasType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}gas_type'])!,
      linearMeters: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}linear_meters'])!,
      cuttingSpeed: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}cutting_speed'])!,
      designProvided: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}design_provided'])!,
      designReference: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}design_reference']),
      totalPrice: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}total_price'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
    );
  }

  @override
  $CalculationsTable createAlias(String alias) {
    return $CalculationsTable(attachedDatabase, alias);
  }
}

class CalculationData extends DataClass implements Insertable<CalculationData> {
  final String id;
  final String clientName;
  final String clientReference;
  final String material;
  final double thickness;
  final String gasType;
  final double linearMeters;
  final double cuttingSpeed;
  final bool designProvided;
  final String? designReference;
  final double totalPrice;
  final DateTime createdAt;
  final String status;
  const CalculationData(
      {required this.id,
      required this.clientName,
      required this.clientReference,
      required this.material,
      required this.thickness,
      required this.gasType,
      required this.linearMeters,
      required this.cuttingSpeed,
      required this.designProvided,
      this.designReference,
      required this.totalPrice,
      required this.createdAt,
      required this.status});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['client_name'] = Variable<String>(clientName);
    map['client_reference'] = Variable<String>(clientReference);
    map['material'] = Variable<String>(material);
    map['thickness'] = Variable<double>(thickness);
    map['gas_type'] = Variable<String>(gasType);
    map['linear_meters'] = Variable<double>(linearMeters);
    map['cutting_speed'] = Variable<double>(cuttingSpeed);
    map['design_provided'] = Variable<bool>(designProvided);
    if (!nullToAbsent || designReference != null) {
      map['design_reference'] = Variable<String>(designReference);
    }
    map['total_price'] = Variable<double>(totalPrice);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['status'] = Variable<String>(status);
    return map;
  }

  CalculationsCompanion toCompanion(bool nullToAbsent) {
    return CalculationsCompanion(
      id: Value(id),
      clientName: Value(clientName),
      clientReference: Value(clientReference),
      material: Value(material),
      thickness: Value(thickness),
      gasType: Value(gasType),
      linearMeters: Value(linearMeters),
      cuttingSpeed: Value(cuttingSpeed),
      designProvided: Value(designProvided),
      designReference: designReference == null && nullToAbsent
          ? const Value.absent()
          : Value(designReference),
      totalPrice: Value(totalPrice),
      createdAt: Value(createdAt),
      status: Value(status),
    );
  }

  factory CalculationData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CalculationData(
      id: serializer.fromJson<String>(json['id']),
      clientName: serializer.fromJson<String>(json['clientName']),
      clientReference: serializer.fromJson<String>(json['clientReference']),
      material: serializer.fromJson<String>(json['material']),
      thickness: serializer.fromJson<double>(json['thickness']),
      gasType: serializer.fromJson<String>(json['gasType']),
      linearMeters: serializer.fromJson<double>(json['linearMeters']),
      cuttingSpeed: serializer.fromJson<double>(json['cuttingSpeed']),
      designProvided: serializer.fromJson<bool>(json['designProvided']),
      designReference: serializer.fromJson<String?>(json['designReference']),
      totalPrice: serializer.fromJson<double>(json['totalPrice']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      status: serializer.fromJson<String>(json['status']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'clientName': serializer.toJson<String>(clientName),
      'clientReference': serializer.toJson<String>(clientReference),
      'material': serializer.toJson<String>(material),
      'thickness': serializer.toJson<double>(thickness),
      'gasType': serializer.toJson<String>(gasType),
      'linearMeters': serializer.toJson<double>(linearMeters),
      'cuttingSpeed': serializer.toJson<double>(cuttingSpeed),
      'designProvided': serializer.toJson<bool>(designProvided),
      'designReference': serializer.toJson<String?>(designReference),
      'totalPrice': serializer.toJson<double>(totalPrice),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'status': serializer.toJson<String>(status),
    };
  }

  CalculationData copyWith(
          {String? id,
          String? clientName,
          String? clientReference,
          String? material,
          double? thickness,
          String? gasType,
          double? linearMeters,
          double? cuttingSpeed,
          bool? designProvided,
          Value<String?> designReference = const Value.absent(),
          double? totalPrice,
          DateTime? createdAt,
          String? status}) =>
      CalculationData(
        id: id ?? this.id,
        clientName: clientName ?? this.clientName,
        clientReference: clientReference ?? this.clientReference,
        material: material ?? this.material,
        thickness: thickness ?? this.thickness,
        gasType: gasType ?? this.gasType,
        linearMeters: linearMeters ?? this.linearMeters,
        cuttingSpeed: cuttingSpeed ?? this.cuttingSpeed,
        designProvided: designProvided ?? this.designProvided,
        designReference: designReference.present
            ? designReference.value
            : this.designReference,
        totalPrice: totalPrice ?? this.totalPrice,
        createdAt: createdAt ?? this.createdAt,
        status: status ?? this.status,
      );
  CalculationData copyWithCompanion(CalculationsCompanion data) {
    return CalculationData(
      id: data.id.present ? data.id.value : this.id,
      clientName:
          data.clientName.present ? data.clientName.value : this.clientName,
      clientReference: data.clientReference.present
          ? data.clientReference.value
          : this.clientReference,
      material: data.material.present ? data.material.value : this.material,
      thickness: data.thickness.present ? data.thickness.value : this.thickness,
      gasType: data.gasType.present ? data.gasType.value : this.gasType,
      linearMeters: data.linearMeters.present
          ? data.linearMeters.value
          : this.linearMeters,
      cuttingSpeed: data.cuttingSpeed.present
          ? data.cuttingSpeed.value
          : this.cuttingSpeed,
      designProvided: data.designProvided.present
          ? data.designProvided.value
          : this.designProvided,
      designReference: data.designReference.present
          ? data.designReference.value
          : this.designReference,
      totalPrice:
          data.totalPrice.present ? data.totalPrice.value : this.totalPrice,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      status: data.status.present ? data.status.value : this.status,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CalculationData(')
          ..write('id: $id, ')
          ..write('clientName: $clientName, ')
          ..write('clientReference: $clientReference, ')
          ..write('material: $material, ')
          ..write('thickness: $thickness, ')
          ..write('gasType: $gasType, ')
          ..write('linearMeters: $linearMeters, ')
          ..write('cuttingSpeed: $cuttingSpeed, ')
          ..write('designProvided: $designProvided, ')
          ..write('designReference: $designReference, ')
          ..write('totalPrice: $totalPrice, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      clientName,
      clientReference,
      material,
      thickness,
      gasType,
      linearMeters,
      cuttingSpeed,
      designProvided,
      designReference,
      totalPrice,
      createdAt,
      status);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CalculationData &&
          other.id == this.id &&
          other.clientName == this.clientName &&
          other.clientReference == this.clientReference &&
          other.material == this.material &&
          other.thickness == this.thickness &&
          other.gasType == this.gasType &&
          other.linearMeters == this.linearMeters &&
          other.cuttingSpeed == this.cuttingSpeed &&
          other.designProvided == this.designProvided &&
          other.designReference == this.designReference &&
          other.totalPrice == this.totalPrice &&
          other.createdAt == this.createdAt &&
          other.status == this.status);
}

class CalculationsCompanion extends UpdateCompanion<CalculationData> {
  final Value<String> id;
  final Value<String> clientName;
  final Value<String> clientReference;
  final Value<String> material;
  final Value<double> thickness;
  final Value<String> gasType;
  final Value<double> linearMeters;
  final Value<double> cuttingSpeed;
  final Value<bool> designProvided;
  final Value<String?> designReference;
  final Value<double> totalPrice;
  final Value<DateTime> createdAt;
  final Value<String> status;
  final Value<int> rowid;
  const CalculationsCompanion({
    this.id = const Value.absent(),
    this.clientName = const Value.absent(),
    this.clientReference = const Value.absent(),
    this.material = const Value.absent(),
    this.thickness = const Value.absent(),
    this.gasType = const Value.absent(),
    this.linearMeters = const Value.absent(),
    this.cuttingSpeed = const Value.absent(),
    this.designProvided = const Value.absent(),
    this.designReference = const Value.absent(),
    this.totalPrice = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.status = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CalculationsCompanion.insert({
    required String id,
    required String clientName,
    required String clientReference,
    required String material,
    required double thickness,
    required String gasType,
    required double linearMeters,
    required double cuttingSpeed,
    required bool designProvided,
    this.designReference = const Value.absent(),
    required double totalPrice,
    required DateTime createdAt,
    required String status,
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        clientName = Value(clientName),
        clientReference = Value(clientReference),
        material = Value(material),
        thickness = Value(thickness),
        gasType = Value(gasType),
        linearMeters = Value(linearMeters),
        cuttingSpeed = Value(cuttingSpeed),
        designProvided = Value(designProvided),
        totalPrice = Value(totalPrice),
        createdAt = Value(createdAt),
        status = Value(status);
  static Insertable<CalculationData> custom({
    Expression<String>? id,
    Expression<String>? clientName,
    Expression<String>? clientReference,
    Expression<String>? material,
    Expression<double>? thickness,
    Expression<String>? gasType,
    Expression<double>? linearMeters,
    Expression<double>? cuttingSpeed,
    Expression<bool>? designProvided,
    Expression<String>? designReference,
    Expression<double>? totalPrice,
    Expression<DateTime>? createdAt,
    Expression<String>? status,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (clientName != null) 'client_name': clientName,
      if (clientReference != null) 'client_reference': clientReference,
      if (material != null) 'material': material,
      if (thickness != null) 'thickness': thickness,
      if (gasType != null) 'gas_type': gasType,
      if (linearMeters != null) 'linear_meters': linearMeters,
      if (cuttingSpeed != null) 'cutting_speed': cuttingSpeed,
      if (designProvided != null) 'design_provided': designProvided,
      if (designReference != null) 'design_reference': designReference,
      if (totalPrice != null) 'total_price': totalPrice,
      if (createdAt != null) 'created_at': createdAt,
      if (status != null) 'status': status,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CalculationsCompanion copyWith(
      {Value<String>? id,
      Value<String>? clientName,
      Value<String>? clientReference,
      Value<String>? material,
      Value<double>? thickness,
      Value<String>? gasType,
      Value<double>? linearMeters,
      Value<double>? cuttingSpeed,
      Value<bool>? designProvided,
      Value<String?>? designReference,
      Value<double>? totalPrice,
      Value<DateTime>? createdAt,
      Value<String>? status,
      Value<int>? rowid}) {
    return CalculationsCompanion(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      clientReference: clientReference ?? this.clientReference,
      material: material ?? this.material,
      thickness: thickness ?? this.thickness,
      gasType: gasType ?? this.gasType,
      linearMeters: linearMeters ?? this.linearMeters,
      cuttingSpeed: cuttingSpeed ?? this.cuttingSpeed,
      designProvided: designProvided ?? this.designProvided,
      designReference: designReference ?? this.designReference,
      totalPrice: totalPrice ?? this.totalPrice,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (clientName.present) {
      map['client_name'] = Variable<String>(clientName.value);
    }
    if (clientReference.present) {
      map['client_reference'] = Variable<String>(clientReference.value);
    }
    if (material.present) {
      map['material'] = Variable<String>(material.value);
    }
    if (thickness.present) {
      map['thickness'] = Variable<double>(thickness.value);
    }
    if (gasType.present) {
      map['gas_type'] = Variable<String>(gasType.value);
    }
    if (linearMeters.present) {
      map['linear_meters'] = Variable<double>(linearMeters.value);
    }
    if (cuttingSpeed.present) {
      map['cutting_speed'] = Variable<double>(cuttingSpeed.value);
    }
    if (designProvided.present) {
      map['design_provided'] = Variable<bool>(designProvided.value);
    }
    if (designReference.present) {
      map['design_reference'] = Variable<String>(designReference.value);
    }
    if (totalPrice.present) {
      map['total_price'] = Variable<double>(totalPrice.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CalculationsCompanion(')
          ..write('id: $id, ')
          ..write('clientName: $clientName, ')
          ..write('clientReference: $clientReference, ')
          ..write('material: $material, ')
          ..write('thickness: $thickness, ')
          ..write('gasType: $gasType, ')
          ..write('linearMeters: $linearMeters, ')
          ..write('cuttingSpeed: $cuttingSpeed, ')
          ..write('designProvided: $designProvided, ')
          ..write('designReference: $designReference, ')
          ..write('totalPrice: $totalPrice, ')
          ..write('createdAt: $createdAt, ')
          ..write('status: $status, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ConfigurationsTable extends Configurations
    with TableInfo<$ConfigurationsTable, ConfigurationData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ConfigurationsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'key', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [key, value, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'configurations';
  @override
  VerificationContext validateIntegrity(Insertable<ConfigurationData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('key')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['key']!, _keyMeta));
    } else if (isInserting) {
      context.missing(_keyMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {key};
  @override
  ConfigurationData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ConfigurationData(
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $ConfigurationsTable createAlias(String alias) {
    return $ConfigurationsTable(attachedDatabase, alias);
  }
}

class ConfigurationData extends DataClass
    implements Insertable<ConfigurationData> {
  final String key;
  final String value;
  final DateTime updatedAt;
  const ConfigurationData(
      {required this.key, required this.value, required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['key'] = Variable<String>(key);
    map['value'] = Variable<String>(value);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  ConfigurationsCompanion toCompanion(bool nullToAbsent) {
    return ConfigurationsCompanion(
      key: Value(key),
      value: Value(value),
      updatedAt: Value(updatedAt),
    );
  }

  factory ConfigurationData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ConfigurationData(
      key: serializer.fromJson<String>(json['key']),
      value: serializer.fromJson<String>(json['value']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'key': serializer.toJson<String>(key),
      'value': serializer.toJson<String>(value),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  ConfigurationData copyWith(
          {String? key, String? value, DateTime? updatedAt}) =>
      ConfigurationData(
        key: key ?? this.key,
        value: value ?? this.value,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  ConfigurationData copyWithCompanion(ConfigurationsCompanion data) {
    return ConfigurationData(
      key: data.key.present ? data.key.value : this.key,
      value: data.value.present ? data.value.value : this.value,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ConfigurationData(')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(key, value, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ConfigurationData &&
          other.key == this.key &&
          other.value == this.value &&
          other.updatedAt == this.updatedAt);
}

class ConfigurationsCompanion extends UpdateCompanion<ConfigurationData> {
  final Value<String> key;
  final Value<String> value;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const ConfigurationsCompanion({
    this.key = const Value.absent(),
    this.value = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ConfigurationsCompanion.insert({
    required String key,
    required String value,
    required DateTime updatedAt,
    this.rowid = const Value.absent(),
  })  : key = Value(key),
        value = Value(value),
        updatedAt = Value(updatedAt);
  static Insertable<ConfigurationData> custom({
    Expression<String>? key,
    Expression<String>? value,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (key != null) 'key': key,
      if (value != null) 'value': value,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ConfigurationsCompanion copyWith(
      {Value<String>? key,
      Value<String>? value,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return ConfigurationsCompanion(
      key: key ?? this.key,
      value: value ?? this.value,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (key.present) {
      map['key'] = Variable<String>(key.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(value.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ConfigurationsCompanion(')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $MaterialsTable extends Materials
    with TableInfo<$MaterialsTable, MaterialData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $MaterialsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  @override
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _rateMeta = const VerificationMeta('rate');
  @override
  late final GeneratedColumn<double> rate = GeneratedColumn<double>(
      'rate', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _isDefaultMeta =
      const VerificationMeta('isDefault');
  @override
  late final GeneratedColumn<bool> isDefault = GeneratedColumn<bool>(
      'is_default', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_default" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, name, displayName, rate, isDefault, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'materials';
  @override
  VerificationContext validateIntegrity(Insertable<MaterialData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    } else if (isInserting) {
      context.missing(_displayNameMeta);
    }
    if (data.containsKey('rate')) {
      context.handle(
          _rateMeta, rate.isAcceptableOrUnknown(data['rate']!, _rateMeta));
    } else if (isInserting) {
      context.missing(_rateMeta);
    }
    if (data.containsKey('is_default')) {
      context.handle(_isDefaultMeta,
          isDefault.isAcceptableOrUnknown(data['is_default']!, _isDefaultMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  MaterialData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return MaterialData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name'])!,
      rate: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}rate'])!,
      isDefault: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_default'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $MaterialsTable createAlias(String alias) {
    return $MaterialsTable(attachedDatabase, alias);
  }
}

class MaterialData extends DataClass implements Insertable<MaterialData> {
  final int id;
  final String name;
  final String displayName;
  final double rate;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;
  const MaterialData(
      {required this.id,
      required this.name,
      required this.displayName,
      required this.rate,
      required this.isDefault,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['display_name'] = Variable<String>(displayName);
    map['rate'] = Variable<double>(rate);
    map['is_default'] = Variable<bool>(isDefault);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  MaterialsCompanion toCompanion(bool nullToAbsent) {
    return MaterialsCompanion(
      id: Value(id),
      name: Value(name),
      displayName: Value(displayName),
      rate: Value(rate),
      isDefault: Value(isDefault),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory MaterialData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return MaterialData(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      displayName: serializer.fromJson<String>(json['displayName']),
      rate: serializer.fromJson<double>(json['rate']),
      isDefault: serializer.fromJson<bool>(json['isDefault']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'displayName': serializer.toJson<String>(displayName),
      'rate': serializer.toJson<double>(rate),
      'isDefault': serializer.toJson<bool>(isDefault),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  MaterialData copyWith(
          {int? id,
          String? name,
          String? displayName,
          double? rate,
          bool? isDefault,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      MaterialData(
        id: id ?? this.id,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        rate: rate ?? this.rate,
        isDefault: isDefault ?? this.isDefault,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  MaterialData copyWithCompanion(MaterialsCompanion data) {
    return MaterialData(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      displayName:
          data.displayName.present ? data.displayName.value : this.displayName,
      rate: data.rate.present ? data.rate.value : this.rate,
      isDefault: data.isDefault.present ? data.isDefault.value : this.isDefault,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('MaterialData(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('rate: $rate, ')
          ..write('isDefault: $isDefault, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, name, displayName, rate, isDefault, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is MaterialData &&
          other.id == this.id &&
          other.name == this.name &&
          other.displayName == this.displayName &&
          other.rate == this.rate &&
          other.isDefault == this.isDefault &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class MaterialsCompanion extends UpdateCompanion<MaterialData> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> displayName;
  final Value<double> rate;
  final Value<bool> isDefault;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const MaterialsCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.displayName = const Value.absent(),
    this.rate = const Value.absent(),
    this.isDefault = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  MaterialsCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String displayName,
    required double rate,
    this.isDefault = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : name = Value(name),
        displayName = Value(displayName),
        rate = Value(rate);
  static Insertable<MaterialData> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? displayName,
    Expression<double>? rate,
    Expression<bool>? isDefault,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (displayName != null) 'display_name': displayName,
      if (rate != null) 'rate': rate,
      if (isDefault != null) 'is_default': isDefault,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  MaterialsCompanion copyWith(
      {Value<int>? id,
      Value<String>? name,
      Value<String>? displayName,
      Value<double>? rate,
      Value<bool>? isDefault,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return MaterialsCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      rate: rate ?? this.rate,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (rate.present) {
      map['rate'] = Variable<double>(rate.value);
    }
    if (isDefault.present) {
      map['is_default'] = Variable<bool>(isDefault.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('MaterialsCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('rate: $rate, ')
          ..write('isDefault: $isDefault, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $GasTypesTable extends GasTypes
    with TableInfo<$GasTypesTable, GasTypeData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $GasTypesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  @override
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _isDefaultMeta =
      const VerificationMeta('isDefault');
  @override
  late final GeneratedColumn<bool> isDefault = GeneratedColumn<bool>(
      'is_default', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_default" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, name, displayName, isDefault, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'gas_types';
  @override
  VerificationContext validateIntegrity(Insertable<GasTypeData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    } else if (isInserting) {
      context.missing(_displayNameMeta);
    }
    if (data.containsKey('is_default')) {
      context.handle(_isDefaultMeta,
          isDefault.isAcceptableOrUnknown(data['is_default']!, _isDefaultMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  GasTypeData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return GasTypeData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name'])!,
      isDefault: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_default'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $GasTypesTable createAlias(String alias) {
    return $GasTypesTable(attachedDatabase, alias);
  }
}

class GasTypeData extends DataClass implements Insertable<GasTypeData> {
  final int id;
  final String name;
  final String displayName;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;
  const GasTypeData(
      {required this.id,
      required this.name,
      required this.displayName,
      required this.isDefault,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['display_name'] = Variable<String>(displayName);
    map['is_default'] = Variable<bool>(isDefault);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  GasTypesCompanion toCompanion(bool nullToAbsent) {
    return GasTypesCompanion(
      id: Value(id),
      name: Value(name),
      displayName: Value(displayName),
      isDefault: Value(isDefault),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory GasTypeData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return GasTypeData(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      displayName: serializer.fromJson<String>(json['displayName']),
      isDefault: serializer.fromJson<bool>(json['isDefault']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'displayName': serializer.toJson<String>(displayName),
      'isDefault': serializer.toJson<bool>(isDefault),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  GasTypeData copyWith(
          {int? id,
          String? name,
          String? displayName,
          bool? isDefault,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      GasTypeData(
        id: id ?? this.id,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        isDefault: isDefault ?? this.isDefault,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  GasTypeData copyWithCompanion(GasTypesCompanion data) {
    return GasTypeData(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      displayName:
          data.displayName.present ? data.displayName.value : this.displayName,
      isDefault: data.isDefault.present ? data.isDefault.value : this.isDefault,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('GasTypeData(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('isDefault: $isDefault, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, name, displayName, isDefault, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is GasTypeData &&
          other.id == this.id &&
          other.name == this.name &&
          other.displayName == this.displayName &&
          other.isDefault == this.isDefault &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class GasTypesCompanion extends UpdateCompanion<GasTypeData> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> displayName;
  final Value<bool> isDefault;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const GasTypesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.displayName = const Value.absent(),
    this.isDefault = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  GasTypesCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String displayName,
    this.isDefault = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : name = Value(name),
        displayName = Value(displayName);
  static Insertable<GasTypeData> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? displayName,
    Expression<bool>? isDefault,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (displayName != null) 'display_name': displayName,
      if (isDefault != null) 'is_default': isDefault,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  GasTypesCompanion copyWith(
      {Value<int>? id,
      Value<String>? name,
      Value<String>? displayName,
      Value<bool>? isDefault,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return GasTypesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (isDefault.present) {
      map['is_default'] = Variable<bool>(isDefault.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('GasTypesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('isDefault: $isDefault, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $ThicknessRangesTable extends ThicknessRanges
    with TableInfo<$ThicknessRangesTable, ThicknessRangeData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ThicknessRangesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _displayNameMeta =
      const VerificationMeta('displayName');
  @override
  late final GeneratedColumn<String> displayName = GeneratedColumn<String>(
      'display_name', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _minThicknessMeta =
      const VerificationMeta('minThickness');
  @override
  late final GeneratedColumn<double> minThickness = GeneratedColumn<double>(
      'min_thickness', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _maxThicknessMeta =
      const VerificationMeta('maxThickness');
  @override
  late final GeneratedColumn<double> maxThickness = GeneratedColumn<double>(
      'max_thickness', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _sortOrderMeta =
      const VerificationMeta('sortOrder');
  @override
  late final GeneratedColumn<int> sortOrder = GeneratedColumn<int>(
      'sort_order', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        name,
        displayName,
        minThickness,
        maxThickness,
        sortOrder,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'thickness_ranges';
  @override
  VerificationContext validateIntegrity(Insertable<ThicknessRangeData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('display_name')) {
      context.handle(
          _displayNameMeta,
          displayName.isAcceptableOrUnknown(
              data['display_name']!, _displayNameMeta));
    } else if (isInserting) {
      context.missing(_displayNameMeta);
    }
    if (data.containsKey('min_thickness')) {
      context.handle(
          _minThicknessMeta,
          minThickness.isAcceptableOrUnknown(
              data['min_thickness']!, _minThicknessMeta));
    } else if (isInserting) {
      context.missing(_minThicknessMeta);
    }
    if (data.containsKey('max_thickness')) {
      context.handle(
          _maxThicknessMeta,
          maxThickness.isAcceptableOrUnknown(
              data['max_thickness']!, _maxThicknessMeta));
    }
    if (data.containsKey('sort_order')) {
      context.handle(_sortOrderMeta,
          sortOrder.isAcceptableOrUnknown(data['sort_order']!, _sortOrderMeta));
    } else if (isInserting) {
      context.missing(_sortOrderMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ThicknessRangeData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ThicknessRangeData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      displayName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}display_name'])!,
      minThickness: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}min_thickness'])!,
      maxThickness: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}max_thickness']),
      sortOrder: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}sort_order'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $ThicknessRangesTable createAlias(String alias) {
    return $ThicknessRangesTable(attachedDatabase, alias);
  }
}

class ThicknessRangeData extends DataClass
    implements Insertable<ThicknessRangeData> {
  final int id;
  final String name;
  final String displayName;
  final double minThickness;
  final double? maxThickness;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;
  const ThicknessRangeData(
      {required this.id,
      required this.name,
      required this.displayName,
      required this.minThickness,
      this.maxThickness,
      required this.sortOrder,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['name'] = Variable<String>(name);
    map['display_name'] = Variable<String>(displayName);
    map['min_thickness'] = Variable<double>(minThickness);
    if (!nullToAbsent || maxThickness != null) {
      map['max_thickness'] = Variable<double>(maxThickness);
    }
    map['sort_order'] = Variable<int>(sortOrder);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  ThicknessRangesCompanion toCompanion(bool nullToAbsent) {
    return ThicknessRangesCompanion(
      id: Value(id),
      name: Value(name),
      displayName: Value(displayName),
      minThickness: Value(minThickness),
      maxThickness: maxThickness == null && nullToAbsent
          ? const Value.absent()
          : Value(maxThickness),
      sortOrder: Value(sortOrder),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory ThicknessRangeData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ThicknessRangeData(
      id: serializer.fromJson<int>(json['id']),
      name: serializer.fromJson<String>(json['name']),
      displayName: serializer.fromJson<String>(json['displayName']),
      minThickness: serializer.fromJson<double>(json['minThickness']),
      maxThickness: serializer.fromJson<double?>(json['maxThickness']),
      sortOrder: serializer.fromJson<int>(json['sortOrder']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'name': serializer.toJson<String>(name),
      'displayName': serializer.toJson<String>(displayName),
      'minThickness': serializer.toJson<double>(minThickness),
      'maxThickness': serializer.toJson<double?>(maxThickness),
      'sortOrder': serializer.toJson<int>(sortOrder),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  ThicknessRangeData copyWith(
          {int? id,
          String? name,
          String? displayName,
          double? minThickness,
          Value<double?> maxThickness = const Value.absent(),
          int? sortOrder,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      ThicknessRangeData(
        id: id ?? this.id,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        minThickness: minThickness ?? this.minThickness,
        maxThickness:
            maxThickness.present ? maxThickness.value : this.maxThickness,
        sortOrder: sortOrder ?? this.sortOrder,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  ThicknessRangeData copyWithCompanion(ThicknessRangesCompanion data) {
    return ThicknessRangeData(
      id: data.id.present ? data.id.value : this.id,
      name: data.name.present ? data.name.value : this.name,
      displayName:
          data.displayName.present ? data.displayName.value : this.displayName,
      minThickness: data.minThickness.present
          ? data.minThickness.value
          : this.minThickness,
      maxThickness: data.maxThickness.present
          ? data.maxThickness.value
          : this.maxThickness,
      sortOrder: data.sortOrder.present ? data.sortOrder.value : this.sortOrder,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ThicknessRangeData(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('minThickness: $minThickness, ')
          ..write('maxThickness: $maxThickness, ')
          ..write('sortOrder: $sortOrder, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, name, displayName, minThickness,
      maxThickness, sortOrder, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ThicknessRangeData &&
          other.id == this.id &&
          other.name == this.name &&
          other.displayName == this.displayName &&
          other.minThickness == this.minThickness &&
          other.maxThickness == this.maxThickness &&
          other.sortOrder == this.sortOrder &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class ThicknessRangesCompanion extends UpdateCompanion<ThicknessRangeData> {
  final Value<int> id;
  final Value<String> name;
  final Value<String> displayName;
  final Value<double> minThickness;
  final Value<double?> maxThickness;
  final Value<int> sortOrder;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const ThicknessRangesCompanion({
    this.id = const Value.absent(),
    this.name = const Value.absent(),
    this.displayName = const Value.absent(),
    this.minThickness = const Value.absent(),
    this.maxThickness = const Value.absent(),
    this.sortOrder = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  ThicknessRangesCompanion.insert({
    this.id = const Value.absent(),
    required String name,
    required String displayName,
    required double minThickness,
    this.maxThickness = const Value.absent(),
    required int sortOrder,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : name = Value(name),
        displayName = Value(displayName),
        minThickness = Value(minThickness),
        sortOrder = Value(sortOrder);
  static Insertable<ThicknessRangeData> custom({
    Expression<int>? id,
    Expression<String>? name,
    Expression<String>? displayName,
    Expression<double>? minThickness,
    Expression<double>? maxThickness,
    Expression<int>? sortOrder,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (name != null) 'name': name,
      if (displayName != null) 'display_name': displayName,
      if (minThickness != null) 'min_thickness': minThickness,
      if (maxThickness != null) 'max_thickness': maxThickness,
      if (sortOrder != null) 'sort_order': sortOrder,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  ThicknessRangesCompanion copyWith(
      {Value<int>? id,
      Value<String>? name,
      Value<String>? displayName,
      Value<double>? minThickness,
      Value<double?>? maxThickness,
      Value<int>? sortOrder,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return ThicknessRangesCompanion(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      minThickness: minThickness ?? this.minThickness,
      maxThickness: maxThickness ?? this.maxThickness,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (displayName.present) {
      map['display_name'] = Variable<String>(displayName.value);
    }
    if (minThickness.present) {
      map['min_thickness'] = Variable<double>(minThickness.value);
    }
    if (maxThickness.present) {
      map['max_thickness'] = Variable<double>(maxThickness.value);
    }
    if (sortOrder.present) {
      map['sort_order'] = Variable<int>(sortOrder.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ThicknessRangesCompanion(')
          ..write('id: $id, ')
          ..write('name: $name, ')
          ..write('displayName: $displayName, ')
          ..write('minThickness: $minThickness, ')
          ..write('maxThickness: $maxThickness, ')
          ..write('sortOrder: $sortOrder, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $GasCostsTable extends GasCosts
    with TableInfo<$GasCostsTable, GasCostData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $GasCostsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _gasTypeIdMeta =
      const VerificationMeta('gasTypeId');
  @override
  late final GeneratedColumn<int> gasTypeId = GeneratedColumn<int>(
      'gas_type_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES gas_types (id) ON DELETE CASCADE'));
  static const VerificationMeta _thicknessRangeIdMeta =
      const VerificationMeta('thicknessRangeId');
  @override
  late final GeneratedColumn<int> thicknessRangeId = GeneratedColumn<int>(
      'thickness_range_id', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES thickness_ranges (id) ON DELETE CASCADE'));
  static const VerificationMeta _costPerHourMeta =
      const VerificationMeta('costPerHour');
  @override
  late final GeneratedColumn<double> costPerHour = GeneratedColumn<double>(
      'cost_per_hour', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, gasTypeId, thicknessRangeId, costPerHour, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'gas_costs';
  @override
  VerificationContext validateIntegrity(Insertable<GasCostData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('gas_type_id')) {
      context.handle(
          _gasTypeIdMeta,
          gasTypeId.isAcceptableOrUnknown(
              data['gas_type_id']!, _gasTypeIdMeta));
    } else if (isInserting) {
      context.missing(_gasTypeIdMeta);
    }
    if (data.containsKey('thickness_range_id')) {
      context.handle(
          _thicknessRangeIdMeta,
          thicknessRangeId.isAcceptableOrUnknown(
              data['thickness_range_id']!, _thicknessRangeIdMeta));
    } else if (isInserting) {
      context.missing(_thicknessRangeIdMeta);
    }
    if (data.containsKey('cost_per_hour')) {
      context.handle(
          _costPerHourMeta,
          costPerHour.isAcceptableOrUnknown(
              data['cost_per_hour']!, _costPerHourMeta));
    } else if (isInserting) {
      context.missing(_costPerHourMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {gasTypeId, thicknessRangeId},
      ];
  @override
  GasCostData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return GasCostData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      gasTypeId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}gas_type_id'])!,
      thicknessRangeId: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}thickness_range_id'])!,
      costPerHour: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}cost_per_hour'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $GasCostsTable createAlias(String alias) {
    return $GasCostsTable(attachedDatabase, alias);
  }
}

class GasCostData extends DataClass implements Insertable<GasCostData> {
  final int id;
  final int gasTypeId;
  final int thicknessRangeId;
  final double costPerHour;
  final DateTime createdAt;
  final DateTime updatedAt;
  const GasCostData(
      {required this.id,
      required this.gasTypeId,
      required this.thicknessRangeId,
      required this.costPerHour,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['gas_type_id'] = Variable<int>(gasTypeId);
    map['thickness_range_id'] = Variable<int>(thicknessRangeId);
    map['cost_per_hour'] = Variable<double>(costPerHour);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  GasCostsCompanion toCompanion(bool nullToAbsent) {
    return GasCostsCompanion(
      id: Value(id),
      gasTypeId: Value(gasTypeId),
      thicknessRangeId: Value(thicknessRangeId),
      costPerHour: Value(costPerHour),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory GasCostData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return GasCostData(
      id: serializer.fromJson<int>(json['id']),
      gasTypeId: serializer.fromJson<int>(json['gasTypeId']),
      thicknessRangeId: serializer.fromJson<int>(json['thicknessRangeId']),
      costPerHour: serializer.fromJson<double>(json['costPerHour']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'gasTypeId': serializer.toJson<int>(gasTypeId),
      'thicknessRangeId': serializer.toJson<int>(thicknessRangeId),
      'costPerHour': serializer.toJson<double>(costPerHour),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  GasCostData copyWith(
          {int? id,
          int? gasTypeId,
          int? thicknessRangeId,
          double? costPerHour,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      GasCostData(
        id: id ?? this.id,
        gasTypeId: gasTypeId ?? this.gasTypeId,
        thicknessRangeId: thicknessRangeId ?? this.thicknessRangeId,
        costPerHour: costPerHour ?? this.costPerHour,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  GasCostData copyWithCompanion(GasCostsCompanion data) {
    return GasCostData(
      id: data.id.present ? data.id.value : this.id,
      gasTypeId: data.gasTypeId.present ? data.gasTypeId.value : this.gasTypeId,
      thicknessRangeId: data.thicknessRangeId.present
          ? data.thicknessRangeId.value
          : this.thicknessRangeId,
      costPerHour:
          data.costPerHour.present ? data.costPerHour.value : this.costPerHour,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('GasCostData(')
          ..write('id: $id, ')
          ..write('gasTypeId: $gasTypeId, ')
          ..write('thicknessRangeId: $thicknessRangeId, ')
          ..write('costPerHour: $costPerHour, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, gasTypeId, thicknessRangeId, costPerHour, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is GasCostData &&
          other.id == this.id &&
          other.gasTypeId == this.gasTypeId &&
          other.thicknessRangeId == this.thicknessRangeId &&
          other.costPerHour == this.costPerHour &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class GasCostsCompanion extends UpdateCompanion<GasCostData> {
  final Value<int> id;
  final Value<int> gasTypeId;
  final Value<int> thicknessRangeId;
  final Value<double> costPerHour;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const GasCostsCompanion({
    this.id = const Value.absent(),
    this.gasTypeId = const Value.absent(),
    this.thicknessRangeId = const Value.absent(),
    this.costPerHour = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  GasCostsCompanion.insert({
    this.id = const Value.absent(),
    required int gasTypeId,
    required int thicknessRangeId,
    required double costPerHour,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : gasTypeId = Value(gasTypeId),
        thicknessRangeId = Value(thicknessRangeId),
        costPerHour = Value(costPerHour);
  static Insertable<GasCostData> custom({
    Expression<int>? id,
    Expression<int>? gasTypeId,
    Expression<int>? thicknessRangeId,
    Expression<double>? costPerHour,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (gasTypeId != null) 'gas_type_id': gasTypeId,
      if (thicknessRangeId != null) 'thickness_range_id': thicknessRangeId,
      if (costPerHour != null) 'cost_per_hour': costPerHour,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  GasCostsCompanion copyWith(
      {Value<int>? id,
      Value<int>? gasTypeId,
      Value<int>? thicknessRangeId,
      Value<double>? costPerHour,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return GasCostsCompanion(
      id: id ?? this.id,
      gasTypeId: gasTypeId ?? this.gasTypeId,
      thicknessRangeId: thicknessRangeId ?? this.thicknessRangeId,
      costPerHour: costPerHour ?? this.costPerHour,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (gasTypeId.present) {
      map['gas_type_id'] = Variable<int>(gasTypeId.value);
    }
    if (thicknessRangeId.present) {
      map['thickness_range_id'] = Variable<int>(thicknessRangeId.value);
    }
    if (costPerHour.present) {
      map['cost_per_hour'] = Variable<double>(costPerHour.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('GasCostsCompanion(')
          ..write('id: $id, ')
          ..write('gasTypeId: $gasTypeId, ')
          ..write('thicknessRangeId: $thicknessRangeId, ')
          ..write('costPerHour: $costPerHour, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $AppSettingsTable extends AppSettings
    with TableInfo<$AppSettingsTable, AppSettingData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AppSettingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _categoryMeta =
      const VerificationMeta('category');
  @override
  late final GeneratedColumn<String> category = GeneratedColumn<String>(
      'category', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'key', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<String> value = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _valueTypeMeta =
      const VerificationMeta('valueType');
  @override
  late final GeneratedColumn<String> valueType = GeneratedColumn<String>(
      'value_type', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 20),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, category, key, value, valueType, description, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'app_settings';
  @override
  VerificationContext validateIntegrity(Insertable<AppSettingData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('category')) {
      context.handle(_categoryMeta,
          category.isAcceptableOrUnknown(data['category']!, _categoryMeta));
    } else if (isInserting) {
      context.missing(_categoryMeta);
    }
    if (data.containsKey('key')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['key']!, _keyMeta));
    } else if (isInserting) {
      context.missing(_keyMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _valueMeta, value.isAcceptableOrUnknown(data['value']!, _valueMeta));
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('value_type')) {
      context.handle(_valueTypeMeta,
          valueType.isAcceptableOrUnknown(data['value_type']!, _valueTypeMeta));
    } else if (isInserting) {
      context.missing(_valueTypeMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {category, key},
      ];
  @override
  AppSettingData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AppSettingData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      category: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category'])!,
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      value: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
      valueType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value_type'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $AppSettingsTable createAlias(String alias) {
    return $AppSettingsTable(attachedDatabase, alias);
  }
}

class AppSettingData extends DataClass implements Insertable<AppSettingData> {
  final int id;
  final String category;
  final String key;
  final String value;
  final String valueType;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;
  const AppSettingData(
      {required this.id,
      required this.category,
      required this.key,
      required this.value,
      required this.valueType,
      this.description,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['category'] = Variable<String>(category);
    map['key'] = Variable<String>(key);
    map['value'] = Variable<String>(value);
    map['value_type'] = Variable<String>(valueType);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  AppSettingsCompanion toCompanion(bool nullToAbsent) {
    return AppSettingsCompanion(
      id: Value(id),
      category: Value(category),
      key: Value(key),
      value: Value(value),
      valueType: Value(valueType),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory AppSettingData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AppSettingData(
      id: serializer.fromJson<int>(json['id']),
      category: serializer.fromJson<String>(json['category']),
      key: serializer.fromJson<String>(json['key']),
      value: serializer.fromJson<String>(json['value']),
      valueType: serializer.fromJson<String>(json['valueType']),
      description: serializer.fromJson<String?>(json['description']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'category': serializer.toJson<String>(category),
      'key': serializer.toJson<String>(key),
      'value': serializer.toJson<String>(value),
      'valueType': serializer.toJson<String>(valueType),
      'description': serializer.toJson<String?>(description),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  AppSettingData copyWith(
          {int? id,
          String? category,
          String? key,
          String? value,
          String? valueType,
          Value<String?> description = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      AppSettingData(
        id: id ?? this.id,
        category: category ?? this.category,
        key: key ?? this.key,
        value: value ?? this.value,
        valueType: valueType ?? this.valueType,
        description: description.present ? description.value : this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  AppSettingData copyWithCompanion(AppSettingsCompanion data) {
    return AppSettingData(
      id: data.id.present ? data.id.value : this.id,
      category: data.category.present ? data.category.value : this.category,
      key: data.key.present ? data.key.value : this.key,
      value: data.value.present ? data.value.value : this.value,
      valueType: data.valueType.present ? data.valueType.value : this.valueType,
      description:
          data.description.present ? data.description.value : this.description,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AppSettingData(')
          ..write('id: $id, ')
          ..write('category: $category, ')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('valueType: $valueType, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id, category, key, value, valueType, description, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AppSettingData &&
          other.id == this.id &&
          other.category == this.category &&
          other.key == this.key &&
          other.value == this.value &&
          other.valueType == this.valueType &&
          other.description == this.description &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class AppSettingsCompanion extends UpdateCompanion<AppSettingData> {
  final Value<int> id;
  final Value<String> category;
  final Value<String> key;
  final Value<String> value;
  final Value<String> valueType;
  final Value<String?> description;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const AppSettingsCompanion({
    this.id = const Value.absent(),
    this.category = const Value.absent(),
    this.key = const Value.absent(),
    this.value = const Value.absent(),
    this.valueType = const Value.absent(),
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  AppSettingsCompanion.insert({
    this.id = const Value.absent(),
    required String category,
    required String key,
    required String value,
    required String valueType,
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : category = Value(category),
        key = Value(key),
        value = Value(value),
        valueType = Value(valueType);
  static Insertable<AppSettingData> custom({
    Expression<int>? id,
    Expression<String>? category,
    Expression<String>? key,
    Expression<String>? value,
    Expression<String>? valueType,
    Expression<String>? description,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (category != null) 'category': category,
      if (key != null) 'key': key,
      if (value != null) 'value': value,
      if (valueType != null) 'value_type': valueType,
      if (description != null) 'description': description,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  AppSettingsCompanion copyWith(
      {Value<int>? id,
      Value<String>? category,
      Value<String>? key,
      Value<String>? value,
      Value<String>? valueType,
      Value<String?>? description,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return AppSettingsCompanion(
      id: id ?? this.id,
      category: category ?? this.category,
      key: key ?? this.key,
      value: value ?? this.value,
      valueType: valueType ?? this.valueType,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (category.present) {
      map['category'] = Variable<String>(category.value);
    }
    if (key.present) {
      map['key'] = Variable<String>(key.value);
    }
    if (value.present) {
      map['value'] = Variable<String>(value.value);
    }
    if (valueType.present) {
      map['value_type'] = Variable<String>(valueType.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AppSettingsCompanion(')
          ..write('id: $id, ')
          ..write('category: $category, ')
          ..write('key: $key, ')
          ..write('value: $value, ')
          ..write('valueType: $valueType, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

class $LicensesTable extends Licenses
    with TableInfo<$LicensesTable, LicenseData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LicensesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _serialNumberMeta =
      const VerificationMeta('serialNumber');
  @override
  late final GeneratedColumn<String> serialNumber = GeneratedColumn<String>(
      'serial_number', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _hardwareIdMeta =
      const VerificationMeta('hardwareId');
  @override
  late final GeneratedColumn<String> hardwareId = GeneratedColumn<String>(
      'hardware_id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _isValidMeta =
      const VerificationMeta('isValid');
  @override
  late final GeneratedColumn<bool> isValid = GeneratedColumn<bool>(
      'is_valid', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_valid" IN (0, 1))'));
  static const VerificationMeta _responseCodeMeta =
      const VerificationMeta('responseCode');
  @override
  late final GeneratedColumn<String> responseCode = GeneratedColumn<String>(
      'response_code', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 50),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _messageMeta =
      const VerificationMeta('message');
  @override
  late final GeneratedColumn<String> message = GeneratedColumn<String>(
      'message', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _expiryDateMeta =
      const VerificationMeta('expiryDate');
  @override
  late final GeneratedColumn<DateTime> expiryDate = GeneratedColumn<DateTime>(
      'expiry_date', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _lastValidatedMeta =
      const VerificationMeta('lastValidated');
  @override
  late final GeneratedColumn<DateTime> lastValidated =
      GeneratedColumn<DateTime>('last_validated', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _firstValidatedMeta =
      const VerificationMeta('firstValidated');
  @override
  late final GeneratedColumn<DateTime> firstValidated =
      GeneratedColumn<DateTime>('first_validated', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _serverUrlMeta =
      const VerificationMeta('serverUrl');
  @override
  late final GeneratedColumn<String> serverUrl = GeneratedColumn<String>(
      'server_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _serverResponseMeta =
      const VerificationMeta('serverResponse');
  @override
  late final GeneratedColumn<String> serverResponse = GeneratedColumn<String>(
      'server_response', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        serialNumber,
        hardwareId,
        isValid,
        responseCode,
        message,
        expiryDate,
        lastValidated,
        firstValidated,
        serverUrl,
        serverResponse,
        isActive,
        createdAt,
        updatedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'licenses';
  @override
  VerificationContext validateIntegrity(Insertable<LicenseData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('serial_number')) {
      context.handle(
          _serialNumberMeta,
          serialNumber.isAcceptableOrUnknown(
              data['serial_number']!, _serialNumberMeta));
    } else if (isInserting) {
      context.missing(_serialNumberMeta);
    }
    if (data.containsKey('hardware_id')) {
      context.handle(
          _hardwareIdMeta,
          hardwareId.isAcceptableOrUnknown(
              data['hardware_id']!, _hardwareIdMeta));
    } else if (isInserting) {
      context.missing(_hardwareIdMeta);
    }
    if (data.containsKey('is_valid')) {
      context.handle(_isValidMeta,
          isValid.isAcceptableOrUnknown(data['is_valid']!, _isValidMeta));
    } else if (isInserting) {
      context.missing(_isValidMeta);
    }
    if (data.containsKey('response_code')) {
      context.handle(
          _responseCodeMeta,
          responseCode.isAcceptableOrUnknown(
              data['response_code']!, _responseCodeMeta));
    } else if (isInserting) {
      context.missing(_responseCodeMeta);
    }
    if (data.containsKey('message')) {
      context.handle(_messageMeta,
          message.isAcceptableOrUnknown(data['message']!, _messageMeta));
    } else if (isInserting) {
      context.missing(_messageMeta);
    }
    if (data.containsKey('expiry_date')) {
      context.handle(
          _expiryDateMeta,
          expiryDate.isAcceptableOrUnknown(
              data['expiry_date']!, _expiryDateMeta));
    }
    if (data.containsKey('last_validated')) {
      context.handle(
          _lastValidatedMeta,
          lastValidated.isAcceptableOrUnknown(
              data['last_validated']!, _lastValidatedMeta));
    } else if (isInserting) {
      context.missing(_lastValidatedMeta);
    }
    if (data.containsKey('first_validated')) {
      context.handle(
          _firstValidatedMeta,
          firstValidated.isAcceptableOrUnknown(
              data['first_validated']!, _firstValidatedMeta));
    } else if (isInserting) {
      context.missing(_firstValidatedMeta);
    }
    if (data.containsKey('server_url')) {
      context.handle(_serverUrlMeta,
          serverUrl.isAcceptableOrUnknown(data['server_url']!, _serverUrlMeta));
    }
    if (data.containsKey('server_response')) {
      context.handle(
          _serverResponseMeta,
          serverResponse.isAcceptableOrUnknown(
              data['server_response']!, _serverResponseMeta));
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  List<Set<GeneratedColumn>> get uniqueKeys => [
        {serialNumber},
      ];
  @override
  LicenseData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LicenseData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      serialNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}serial_number'])!,
      hardwareId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}hardware_id'])!,
      isValid: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_valid'])!,
      responseCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}response_code'])!,
      message: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}message'])!,
      expiryDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}expiry_date']),
      lastValidated: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_validated'])!,
      firstValidated: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}first_validated'])!,
      serverUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}server_url']),
      serverResponse: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}server_response']),
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $LicensesTable createAlias(String alias) {
    return $LicensesTable(attachedDatabase, alias);
  }
}

class LicenseData extends DataClass implements Insertable<LicenseData> {
  final int id;
  final String serialNumber;
  final String hardwareId;
  final bool isValid;
  final String responseCode;
  final String message;
  final DateTime? expiryDate;
  final DateTime lastValidated;
  final DateTime firstValidated;
  final String? serverUrl;
  final String? serverResponse;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  const LicenseData(
      {required this.id,
      required this.serialNumber,
      required this.hardwareId,
      required this.isValid,
      required this.responseCode,
      required this.message,
      this.expiryDate,
      required this.lastValidated,
      required this.firstValidated,
      this.serverUrl,
      this.serverResponse,
      required this.isActive,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['serial_number'] = Variable<String>(serialNumber);
    map['hardware_id'] = Variable<String>(hardwareId);
    map['is_valid'] = Variable<bool>(isValid);
    map['response_code'] = Variable<String>(responseCode);
    map['message'] = Variable<String>(message);
    if (!nullToAbsent || expiryDate != null) {
      map['expiry_date'] = Variable<DateTime>(expiryDate);
    }
    map['last_validated'] = Variable<DateTime>(lastValidated);
    map['first_validated'] = Variable<DateTime>(firstValidated);
    if (!nullToAbsent || serverUrl != null) {
      map['server_url'] = Variable<String>(serverUrl);
    }
    if (!nullToAbsent || serverResponse != null) {
      map['server_response'] = Variable<String>(serverResponse);
    }
    map['is_active'] = Variable<bool>(isActive);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  LicensesCompanion toCompanion(bool nullToAbsent) {
    return LicensesCompanion(
      id: Value(id),
      serialNumber: Value(serialNumber),
      hardwareId: Value(hardwareId),
      isValid: Value(isValid),
      responseCode: Value(responseCode),
      message: Value(message),
      expiryDate: expiryDate == null && nullToAbsent
          ? const Value.absent()
          : Value(expiryDate),
      lastValidated: Value(lastValidated),
      firstValidated: Value(firstValidated),
      serverUrl: serverUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(serverUrl),
      serverResponse: serverResponse == null && nullToAbsent
          ? const Value.absent()
          : Value(serverResponse),
      isActive: Value(isActive),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory LicenseData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return LicenseData(
      id: serializer.fromJson<int>(json['id']),
      serialNumber: serializer.fromJson<String>(json['serialNumber']),
      hardwareId: serializer.fromJson<String>(json['hardwareId']),
      isValid: serializer.fromJson<bool>(json['isValid']),
      responseCode: serializer.fromJson<String>(json['responseCode']),
      message: serializer.fromJson<String>(json['message']),
      expiryDate: serializer.fromJson<DateTime?>(json['expiryDate']),
      lastValidated: serializer.fromJson<DateTime>(json['lastValidated']),
      firstValidated: serializer.fromJson<DateTime>(json['firstValidated']),
      serverUrl: serializer.fromJson<String?>(json['serverUrl']),
      serverResponse: serializer.fromJson<String?>(json['serverResponse']),
      isActive: serializer.fromJson<bool>(json['isActive']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'serialNumber': serializer.toJson<String>(serialNumber),
      'hardwareId': serializer.toJson<String>(hardwareId),
      'isValid': serializer.toJson<bool>(isValid),
      'responseCode': serializer.toJson<String>(responseCode),
      'message': serializer.toJson<String>(message),
      'expiryDate': serializer.toJson<DateTime?>(expiryDate),
      'lastValidated': serializer.toJson<DateTime>(lastValidated),
      'firstValidated': serializer.toJson<DateTime>(firstValidated),
      'serverUrl': serializer.toJson<String?>(serverUrl),
      'serverResponse': serializer.toJson<String?>(serverResponse),
      'isActive': serializer.toJson<bool>(isActive),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  LicenseData copyWith(
          {int? id,
          String? serialNumber,
          String? hardwareId,
          bool? isValid,
          String? responseCode,
          String? message,
          Value<DateTime?> expiryDate = const Value.absent(),
          DateTime? lastValidated,
          DateTime? firstValidated,
          Value<String?> serverUrl = const Value.absent(),
          Value<String?> serverResponse = const Value.absent(),
          bool? isActive,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      LicenseData(
        id: id ?? this.id,
        serialNumber: serialNumber ?? this.serialNumber,
        hardwareId: hardwareId ?? this.hardwareId,
        isValid: isValid ?? this.isValid,
        responseCode: responseCode ?? this.responseCode,
        message: message ?? this.message,
        expiryDate: expiryDate.present ? expiryDate.value : this.expiryDate,
        lastValidated: lastValidated ?? this.lastValidated,
        firstValidated: firstValidated ?? this.firstValidated,
        serverUrl: serverUrl.present ? serverUrl.value : this.serverUrl,
        serverResponse:
            serverResponse.present ? serverResponse.value : this.serverResponse,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  LicenseData copyWithCompanion(LicensesCompanion data) {
    return LicenseData(
      id: data.id.present ? data.id.value : this.id,
      serialNumber: data.serialNumber.present
          ? data.serialNumber.value
          : this.serialNumber,
      hardwareId:
          data.hardwareId.present ? data.hardwareId.value : this.hardwareId,
      isValid: data.isValid.present ? data.isValid.value : this.isValid,
      responseCode: data.responseCode.present
          ? data.responseCode.value
          : this.responseCode,
      message: data.message.present ? data.message.value : this.message,
      expiryDate:
          data.expiryDate.present ? data.expiryDate.value : this.expiryDate,
      lastValidated: data.lastValidated.present
          ? data.lastValidated.value
          : this.lastValidated,
      firstValidated: data.firstValidated.present
          ? data.firstValidated.value
          : this.firstValidated,
      serverUrl: data.serverUrl.present ? data.serverUrl.value : this.serverUrl,
      serverResponse: data.serverResponse.present
          ? data.serverResponse.value
          : this.serverResponse,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LicenseData(')
          ..write('id: $id, ')
          ..write('serialNumber: $serialNumber, ')
          ..write('hardwareId: $hardwareId, ')
          ..write('isValid: $isValid, ')
          ..write('responseCode: $responseCode, ')
          ..write('message: $message, ')
          ..write('expiryDate: $expiryDate, ')
          ..write('lastValidated: $lastValidated, ')
          ..write('firstValidated: $firstValidated, ')
          ..write('serverUrl: $serverUrl, ')
          ..write('serverResponse: $serverResponse, ')
          ..write('isActive: $isActive, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      serialNumber,
      hardwareId,
      isValid,
      responseCode,
      message,
      expiryDate,
      lastValidated,
      firstValidated,
      serverUrl,
      serverResponse,
      isActive,
      createdAt,
      updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LicenseData &&
          other.id == this.id &&
          other.serialNumber == this.serialNumber &&
          other.hardwareId == this.hardwareId &&
          other.isValid == this.isValid &&
          other.responseCode == this.responseCode &&
          other.message == this.message &&
          other.expiryDate == this.expiryDate &&
          other.lastValidated == this.lastValidated &&
          other.firstValidated == this.firstValidated &&
          other.serverUrl == this.serverUrl &&
          other.serverResponse == this.serverResponse &&
          other.isActive == this.isActive &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class LicensesCompanion extends UpdateCompanion<LicenseData> {
  final Value<int> id;
  final Value<String> serialNumber;
  final Value<String> hardwareId;
  final Value<bool> isValid;
  final Value<String> responseCode;
  final Value<String> message;
  final Value<DateTime?> expiryDate;
  final Value<DateTime> lastValidated;
  final Value<DateTime> firstValidated;
  final Value<String?> serverUrl;
  final Value<String?> serverResponse;
  final Value<bool> isActive;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  const LicensesCompanion({
    this.id = const Value.absent(),
    this.serialNumber = const Value.absent(),
    this.hardwareId = const Value.absent(),
    this.isValid = const Value.absent(),
    this.responseCode = const Value.absent(),
    this.message = const Value.absent(),
    this.expiryDate = const Value.absent(),
    this.lastValidated = const Value.absent(),
    this.firstValidated = const Value.absent(),
    this.serverUrl = const Value.absent(),
    this.serverResponse = const Value.absent(),
    this.isActive = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  });
  LicensesCompanion.insert({
    this.id = const Value.absent(),
    required String serialNumber,
    required String hardwareId,
    required bool isValid,
    required String responseCode,
    required String message,
    this.expiryDate = const Value.absent(),
    required DateTime lastValidated,
    required DateTime firstValidated,
    this.serverUrl = const Value.absent(),
    this.serverResponse = const Value.absent(),
    this.isActive = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
  })  : serialNumber = Value(serialNumber),
        hardwareId = Value(hardwareId),
        isValid = Value(isValid),
        responseCode = Value(responseCode),
        message = Value(message),
        lastValidated = Value(lastValidated),
        firstValidated = Value(firstValidated);
  static Insertable<LicenseData> custom({
    Expression<int>? id,
    Expression<String>? serialNumber,
    Expression<String>? hardwareId,
    Expression<bool>? isValid,
    Expression<String>? responseCode,
    Expression<String>? message,
    Expression<DateTime>? expiryDate,
    Expression<DateTime>? lastValidated,
    Expression<DateTime>? firstValidated,
    Expression<String>? serverUrl,
    Expression<String>? serverResponse,
    Expression<bool>? isActive,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (serialNumber != null) 'serial_number': serialNumber,
      if (hardwareId != null) 'hardware_id': hardwareId,
      if (isValid != null) 'is_valid': isValid,
      if (responseCode != null) 'response_code': responseCode,
      if (message != null) 'message': message,
      if (expiryDate != null) 'expiry_date': expiryDate,
      if (lastValidated != null) 'last_validated': lastValidated,
      if (firstValidated != null) 'first_validated': firstValidated,
      if (serverUrl != null) 'server_url': serverUrl,
      if (serverResponse != null) 'server_response': serverResponse,
      if (isActive != null) 'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
    });
  }

  LicensesCompanion copyWith(
      {Value<int>? id,
      Value<String>? serialNumber,
      Value<String>? hardwareId,
      Value<bool>? isValid,
      Value<String>? responseCode,
      Value<String>? message,
      Value<DateTime?>? expiryDate,
      Value<DateTime>? lastValidated,
      Value<DateTime>? firstValidated,
      Value<String?>? serverUrl,
      Value<String?>? serverResponse,
      Value<bool>? isActive,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt}) {
    return LicensesCompanion(
      id: id ?? this.id,
      serialNumber: serialNumber ?? this.serialNumber,
      hardwareId: hardwareId ?? this.hardwareId,
      isValid: isValid ?? this.isValid,
      responseCode: responseCode ?? this.responseCode,
      message: message ?? this.message,
      expiryDate: expiryDate ?? this.expiryDate,
      lastValidated: lastValidated ?? this.lastValidated,
      firstValidated: firstValidated ?? this.firstValidated,
      serverUrl: serverUrl ?? this.serverUrl,
      serverResponse: serverResponse ?? this.serverResponse,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (serialNumber.present) {
      map['serial_number'] = Variable<String>(serialNumber.value);
    }
    if (hardwareId.present) {
      map['hardware_id'] = Variable<String>(hardwareId.value);
    }
    if (isValid.present) {
      map['is_valid'] = Variable<bool>(isValid.value);
    }
    if (responseCode.present) {
      map['response_code'] = Variable<String>(responseCode.value);
    }
    if (message.present) {
      map['message'] = Variable<String>(message.value);
    }
    if (expiryDate.present) {
      map['expiry_date'] = Variable<DateTime>(expiryDate.value);
    }
    if (lastValidated.present) {
      map['last_validated'] = Variable<DateTime>(lastValidated.value);
    }
    if (firstValidated.present) {
      map['first_validated'] = Variable<DateTime>(firstValidated.value);
    }
    if (serverUrl.present) {
      map['server_url'] = Variable<String>(serverUrl.value);
    }
    if (serverResponse.present) {
      map['server_response'] = Variable<String>(serverResponse.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LicensesCompanion(')
          ..write('id: $id, ')
          ..write('serialNumber: $serialNumber, ')
          ..write('hardwareId: $hardwareId, ')
          ..write('isValid: $isValid, ')
          ..write('responseCode: $responseCode, ')
          ..write('message: $message, ')
          ..write('expiryDate: $expiryDate, ')
          ..write('lastValidated: $lastValidated, ')
          ..write('firstValidated: $firstValidated, ')
          ..write('serverUrl: $serverUrl, ')
          ..write('serverResponse: $serverResponse, ')
          ..write('isActive: $isActive, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $ClientsTable clients = $ClientsTable(this);
  late final $CalculationsTable calculations = $CalculationsTable(this);
  late final $ConfigurationsTable configurations = $ConfigurationsTable(this);
  late final $MaterialsTable materials = $MaterialsTable(this);
  late final $GasTypesTable gasTypes = $GasTypesTable(this);
  late final $ThicknessRangesTable thicknessRanges =
      $ThicknessRangesTable(this);
  late final $GasCostsTable gasCosts = $GasCostsTable(this);
  late final $AppSettingsTable appSettings = $AppSettingsTable(this);
  late final $LicensesTable licenses = $LicensesTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        clients,
        calculations,
        configurations,
        materials,
        gasTypes,
        thicknessRanges,
        gasCosts,
        appSettings,
        licenses
      ];
  @override
  StreamQueryUpdateRules get streamUpdateRules => const StreamQueryUpdateRules(
        [
          WritePropagation(
            on: TableUpdateQuery.onTableName('gas_types',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('gas_costs', kind: UpdateKind.delete),
            ],
          ),
          WritePropagation(
            on: TableUpdateQuery.onTableName('thickness_ranges',
                limitUpdateKind: UpdateKind.delete),
            result: [
              TableUpdate('gas_costs', kind: UpdateKind.delete),
            ],
          ),
        ],
      );
}

typedef $$ClientsTableCreateCompanionBuilder = ClientsCompanion Function({
  required String id,
  required String name,
  required String reference,
  Value<String?> company,
  Value<String?> email,
  Value<String?> phone,
  Value<int> projects,
  Value<double> totalSpent,
  required DateTime createdAt,
  Value<int> rowid,
});
typedef $$ClientsTableUpdateCompanionBuilder = ClientsCompanion Function({
  Value<String> id,
  Value<String> name,
  Value<String> reference,
  Value<String?> company,
  Value<String?> email,
  Value<String?> phone,
  Value<int> projects,
  Value<double> totalSpent,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

class $$ClientsTableFilterComposer
    extends Composer<_$AppDatabase, $ClientsTable> {
  $$ClientsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get reference => $composableBuilder(
      column: $table.reference, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get company => $composableBuilder(
      column: $table.company, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get phone => $composableBuilder(
      column: $table.phone, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get projects => $composableBuilder(
      column: $table.projects, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get totalSpent => $composableBuilder(
      column: $table.totalSpent, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$ClientsTableOrderingComposer
    extends Composer<_$AppDatabase, $ClientsTable> {
  $$ClientsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get reference => $composableBuilder(
      column: $table.reference, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get company => $composableBuilder(
      column: $table.company, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get phone => $composableBuilder(
      column: $table.phone, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get projects => $composableBuilder(
      column: $table.projects, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get totalSpent => $composableBuilder(
      column: $table.totalSpent, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$ClientsTableAnnotationComposer
    extends Composer<_$AppDatabase, $ClientsTable> {
  $$ClientsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get reference =>
      $composableBuilder(column: $table.reference, builder: (column) => column);

  GeneratedColumn<String> get company =>
      $composableBuilder(column: $table.company, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<String> get phone =>
      $composableBuilder(column: $table.phone, builder: (column) => column);

  GeneratedColumn<int> get projects =>
      $composableBuilder(column: $table.projects, builder: (column) => column);

  GeneratedColumn<double> get totalSpent => $composableBuilder(
      column: $table.totalSpent, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$ClientsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ClientsTable,
    ClientData,
    $$ClientsTableFilterComposer,
    $$ClientsTableOrderingComposer,
    $$ClientsTableAnnotationComposer,
    $$ClientsTableCreateCompanionBuilder,
    $$ClientsTableUpdateCompanionBuilder,
    (ClientData, BaseReferences<_$AppDatabase, $ClientsTable, ClientData>),
    ClientData,
    PrefetchHooks Function()> {
  $$ClientsTableTableManager(_$AppDatabase db, $ClientsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ClientsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ClientsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ClientsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> reference = const Value.absent(),
            Value<String?> company = const Value.absent(),
            Value<String?> email = const Value.absent(),
            Value<String?> phone = const Value.absent(),
            Value<int> projects = const Value.absent(),
            Value<double> totalSpent = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ClientsCompanion(
            id: id,
            name: name,
            reference: reference,
            company: company,
            email: email,
            phone: phone,
            projects: projects,
            totalSpent: totalSpent,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String name,
            required String reference,
            Value<String?> company = const Value.absent(),
            Value<String?> email = const Value.absent(),
            Value<String?> phone = const Value.absent(),
            Value<int> projects = const Value.absent(),
            Value<double> totalSpent = const Value.absent(),
            required DateTime createdAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              ClientsCompanion.insert(
            id: id,
            name: name,
            reference: reference,
            company: company,
            email: email,
            phone: phone,
            projects: projects,
            totalSpent: totalSpent,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ClientsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ClientsTable,
    ClientData,
    $$ClientsTableFilterComposer,
    $$ClientsTableOrderingComposer,
    $$ClientsTableAnnotationComposer,
    $$ClientsTableCreateCompanionBuilder,
    $$ClientsTableUpdateCompanionBuilder,
    (ClientData, BaseReferences<_$AppDatabase, $ClientsTable, ClientData>),
    ClientData,
    PrefetchHooks Function()>;
typedef $$CalculationsTableCreateCompanionBuilder = CalculationsCompanion
    Function({
  required String id,
  required String clientName,
  required String clientReference,
  required String material,
  required double thickness,
  required String gasType,
  required double linearMeters,
  required double cuttingSpeed,
  required bool designProvided,
  Value<String?> designReference,
  required double totalPrice,
  required DateTime createdAt,
  required String status,
  Value<int> rowid,
});
typedef $$CalculationsTableUpdateCompanionBuilder = CalculationsCompanion
    Function({
  Value<String> id,
  Value<String> clientName,
  Value<String> clientReference,
  Value<String> material,
  Value<double> thickness,
  Value<String> gasType,
  Value<double> linearMeters,
  Value<double> cuttingSpeed,
  Value<bool> designProvided,
  Value<String?> designReference,
  Value<double> totalPrice,
  Value<DateTime> createdAt,
  Value<String> status,
  Value<int> rowid,
});

class $$CalculationsTableFilterComposer
    extends Composer<_$AppDatabase, $CalculationsTable> {
  $$CalculationsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get clientName => $composableBuilder(
      column: $table.clientName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get clientReference => $composableBuilder(
      column: $table.clientReference,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get material => $composableBuilder(
      column: $table.material, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get thickness => $composableBuilder(
      column: $table.thickness, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get gasType => $composableBuilder(
      column: $table.gasType, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get linearMeters => $composableBuilder(
      column: $table.linearMeters, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get cuttingSpeed => $composableBuilder(
      column: $table.cuttingSpeed, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get designProvided => $composableBuilder(
      column: $table.designProvided,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get designReference => $composableBuilder(
      column: $table.designReference,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get totalPrice => $composableBuilder(
      column: $table.totalPrice, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));
}

class $$CalculationsTableOrderingComposer
    extends Composer<_$AppDatabase, $CalculationsTable> {
  $$CalculationsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get clientName => $composableBuilder(
      column: $table.clientName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get clientReference => $composableBuilder(
      column: $table.clientReference,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get material => $composableBuilder(
      column: $table.material, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get thickness => $composableBuilder(
      column: $table.thickness, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get gasType => $composableBuilder(
      column: $table.gasType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get linearMeters => $composableBuilder(
      column: $table.linearMeters,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get cuttingSpeed => $composableBuilder(
      column: $table.cuttingSpeed,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get designProvided => $composableBuilder(
      column: $table.designProvided,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get designReference => $composableBuilder(
      column: $table.designReference,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get totalPrice => $composableBuilder(
      column: $table.totalPrice, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));
}

class $$CalculationsTableAnnotationComposer
    extends Composer<_$AppDatabase, $CalculationsTable> {
  $$CalculationsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get clientName => $composableBuilder(
      column: $table.clientName, builder: (column) => column);

  GeneratedColumn<String> get clientReference => $composableBuilder(
      column: $table.clientReference, builder: (column) => column);

  GeneratedColumn<String> get material =>
      $composableBuilder(column: $table.material, builder: (column) => column);

  GeneratedColumn<double> get thickness =>
      $composableBuilder(column: $table.thickness, builder: (column) => column);

  GeneratedColumn<String> get gasType =>
      $composableBuilder(column: $table.gasType, builder: (column) => column);

  GeneratedColumn<double> get linearMeters => $composableBuilder(
      column: $table.linearMeters, builder: (column) => column);

  GeneratedColumn<double> get cuttingSpeed => $composableBuilder(
      column: $table.cuttingSpeed, builder: (column) => column);

  GeneratedColumn<bool> get designProvided => $composableBuilder(
      column: $table.designProvided, builder: (column) => column);

  GeneratedColumn<String> get designReference => $composableBuilder(
      column: $table.designReference, builder: (column) => column);

  GeneratedColumn<double> get totalPrice => $composableBuilder(
      column: $table.totalPrice, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);
}

class $$CalculationsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CalculationsTable,
    CalculationData,
    $$CalculationsTableFilterComposer,
    $$CalculationsTableOrderingComposer,
    $$CalculationsTableAnnotationComposer,
    $$CalculationsTableCreateCompanionBuilder,
    $$CalculationsTableUpdateCompanionBuilder,
    (
      CalculationData,
      BaseReferences<_$AppDatabase, $CalculationsTable, CalculationData>
    ),
    CalculationData,
    PrefetchHooks Function()> {
  $$CalculationsTableTableManager(_$AppDatabase db, $CalculationsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CalculationsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CalculationsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CalculationsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> clientName = const Value.absent(),
            Value<String> clientReference = const Value.absent(),
            Value<String> material = const Value.absent(),
            Value<double> thickness = const Value.absent(),
            Value<String> gasType = const Value.absent(),
            Value<double> linearMeters = const Value.absent(),
            Value<double> cuttingSpeed = const Value.absent(),
            Value<bool> designProvided = const Value.absent(),
            Value<String?> designReference = const Value.absent(),
            Value<double> totalPrice = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CalculationsCompanion(
            id: id,
            clientName: clientName,
            clientReference: clientReference,
            material: material,
            thickness: thickness,
            gasType: gasType,
            linearMeters: linearMeters,
            cuttingSpeed: cuttingSpeed,
            designProvided: designProvided,
            designReference: designReference,
            totalPrice: totalPrice,
            createdAt: createdAt,
            status: status,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String clientName,
            required String clientReference,
            required String material,
            required double thickness,
            required String gasType,
            required double linearMeters,
            required double cuttingSpeed,
            required bool designProvided,
            Value<String?> designReference = const Value.absent(),
            required double totalPrice,
            required DateTime createdAt,
            required String status,
            Value<int> rowid = const Value.absent(),
          }) =>
              CalculationsCompanion.insert(
            id: id,
            clientName: clientName,
            clientReference: clientReference,
            material: material,
            thickness: thickness,
            gasType: gasType,
            linearMeters: linearMeters,
            cuttingSpeed: cuttingSpeed,
            designProvided: designProvided,
            designReference: designReference,
            totalPrice: totalPrice,
            createdAt: createdAt,
            status: status,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CalculationsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $CalculationsTable,
    CalculationData,
    $$CalculationsTableFilterComposer,
    $$CalculationsTableOrderingComposer,
    $$CalculationsTableAnnotationComposer,
    $$CalculationsTableCreateCompanionBuilder,
    $$CalculationsTableUpdateCompanionBuilder,
    (
      CalculationData,
      BaseReferences<_$AppDatabase, $CalculationsTable, CalculationData>
    ),
    CalculationData,
    PrefetchHooks Function()>;
typedef $$ConfigurationsTableCreateCompanionBuilder = ConfigurationsCompanion
    Function({
  required String key,
  required String value,
  required DateTime updatedAt,
  Value<int> rowid,
});
typedef $$ConfigurationsTableUpdateCompanionBuilder = ConfigurationsCompanion
    Function({
  Value<String> key,
  Value<String> value,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

class $$ConfigurationsTableFilterComposer
    extends Composer<_$AppDatabase, $ConfigurationsTable> {
  $$ConfigurationsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$ConfigurationsTableOrderingComposer
    extends Composer<_$AppDatabase, $ConfigurationsTable> {
  $$ConfigurationsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$ConfigurationsTableAnnotationComposer
    extends Composer<_$AppDatabase, $ConfigurationsTable> {
  $$ConfigurationsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get key =>
      $composableBuilder(column: $table.key, builder: (column) => column);

  GeneratedColumn<String> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$ConfigurationsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ConfigurationsTable,
    ConfigurationData,
    $$ConfigurationsTableFilterComposer,
    $$ConfigurationsTableOrderingComposer,
    $$ConfigurationsTableAnnotationComposer,
    $$ConfigurationsTableCreateCompanionBuilder,
    $$ConfigurationsTableUpdateCompanionBuilder,
    (
      ConfigurationData,
      BaseReferences<_$AppDatabase, $ConfigurationsTable, ConfigurationData>
    ),
    ConfigurationData,
    PrefetchHooks Function()> {
  $$ConfigurationsTableTableManager(
      _$AppDatabase db, $ConfigurationsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ConfigurationsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ConfigurationsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ConfigurationsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> key = const Value.absent(),
            Value<String> value = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ConfigurationsCompanion(
            key: key,
            value: value,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String key,
            required String value,
            required DateTime updatedAt,
            Value<int> rowid = const Value.absent(),
          }) =>
              ConfigurationsCompanion.insert(
            key: key,
            value: value,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ConfigurationsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ConfigurationsTable,
    ConfigurationData,
    $$ConfigurationsTableFilterComposer,
    $$ConfigurationsTableOrderingComposer,
    $$ConfigurationsTableAnnotationComposer,
    $$ConfigurationsTableCreateCompanionBuilder,
    $$ConfigurationsTableUpdateCompanionBuilder,
    (
      ConfigurationData,
      BaseReferences<_$AppDatabase, $ConfigurationsTable, ConfigurationData>
    ),
    ConfigurationData,
    PrefetchHooks Function()>;
typedef $$MaterialsTableCreateCompanionBuilder = MaterialsCompanion Function({
  Value<int> id,
  required String name,
  required String displayName,
  required double rate,
  Value<bool> isDefault,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$MaterialsTableUpdateCompanionBuilder = MaterialsCompanion Function({
  Value<int> id,
  Value<String> name,
  Value<String> displayName,
  Value<double> rate,
  Value<bool> isDefault,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

class $$MaterialsTableFilterComposer
    extends Composer<_$AppDatabase, $MaterialsTable> {
  $$MaterialsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get rate => $composableBuilder(
      column: $table.rate, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isDefault => $composableBuilder(
      column: $table.isDefault, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$MaterialsTableOrderingComposer
    extends Composer<_$AppDatabase, $MaterialsTable> {
  $$MaterialsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get rate => $composableBuilder(
      column: $table.rate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isDefault => $composableBuilder(
      column: $table.isDefault, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$MaterialsTableAnnotationComposer
    extends Composer<_$AppDatabase, $MaterialsTable> {
  $$MaterialsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => column);

  GeneratedColumn<double> get rate =>
      $composableBuilder(column: $table.rate, builder: (column) => column);

  GeneratedColumn<bool> get isDefault =>
      $composableBuilder(column: $table.isDefault, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$MaterialsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $MaterialsTable,
    MaterialData,
    $$MaterialsTableFilterComposer,
    $$MaterialsTableOrderingComposer,
    $$MaterialsTableAnnotationComposer,
    $$MaterialsTableCreateCompanionBuilder,
    $$MaterialsTableUpdateCompanionBuilder,
    (
      MaterialData,
      BaseReferences<_$AppDatabase, $MaterialsTable, MaterialData>
    ),
    MaterialData,
    PrefetchHooks Function()> {
  $$MaterialsTableTableManager(_$AppDatabase db, $MaterialsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$MaterialsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$MaterialsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$MaterialsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> displayName = const Value.absent(),
            Value<double> rate = const Value.absent(),
            Value<bool> isDefault = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              MaterialsCompanion(
            id: id,
            name: name,
            displayName: displayName,
            rate: rate,
            isDefault: isDefault,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required String displayName,
            required double rate,
            Value<bool> isDefault = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              MaterialsCompanion.insert(
            id: id,
            name: name,
            displayName: displayName,
            rate: rate,
            isDefault: isDefault,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$MaterialsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $MaterialsTable,
    MaterialData,
    $$MaterialsTableFilterComposer,
    $$MaterialsTableOrderingComposer,
    $$MaterialsTableAnnotationComposer,
    $$MaterialsTableCreateCompanionBuilder,
    $$MaterialsTableUpdateCompanionBuilder,
    (
      MaterialData,
      BaseReferences<_$AppDatabase, $MaterialsTable, MaterialData>
    ),
    MaterialData,
    PrefetchHooks Function()>;
typedef $$GasTypesTableCreateCompanionBuilder = GasTypesCompanion Function({
  Value<int> id,
  required String name,
  required String displayName,
  Value<bool> isDefault,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$GasTypesTableUpdateCompanionBuilder = GasTypesCompanion Function({
  Value<int> id,
  Value<String> name,
  Value<String> displayName,
  Value<bool> isDefault,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

final class $$GasTypesTableReferences
    extends BaseReferences<_$AppDatabase, $GasTypesTable, GasTypeData> {
  $$GasTypesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$GasCostsTable, List<GasCostData>>
      _gasCostsRefsTable(_$AppDatabase db) =>
          MultiTypedResultKey.fromTable(db.gasCosts,
              aliasName:
                  $_aliasNameGenerator(db.gasTypes.id, db.gasCosts.gasTypeId));

  $$GasCostsTableProcessedTableManager get gasCostsRefs {
    final manager = $$GasCostsTableTableManager($_db, $_db.gasCosts)
        .filter((f) => f.gasTypeId.id($_item.id));

    final cache = $_typedResult.readTableOrNull(_gasCostsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$GasTypesTableFilterComposer
    extends Composer<_$AppDatabase, $GasTypesTable> {
  $$GasTypesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isDefault => $composableBuilder(
      column: $table.isDefault, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  Expression<bool> gasCostsRefs(
      Expression<bool> Function($$GasCostsTableFilterComposer f) f) {
    final $$GasCostsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.gasCosts,
        getReferencedColumn: (t) => t.gasTypeId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasCostsTableFilterComposer(
              $db: $db,
              $table: $db.gasCosts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$GasTypesTableOrderingComposer
    extends Composer<_$AppDatabase, $GasTypesTable> {
  $$GasTypesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isDefault => $composableBuilder(
      column: $table.isDefault, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$GasTypesTableAnnotationComposer
    extends Composer<_$AppDatabase, $GasTypesTable> {
  $$GasTypesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => column);

  GeneratedColumn<bool> get isDefault =>
      $composableBuilder(column: $table.isDefault, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  Expression<T> gasCostsRefs<T extends Object>(
      Expression<T> Function($$GasCostsTableAnnotationComposer a) f) {
    final $$GasCostsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.gasCosts,
        getReferencedColumn: (t) => t.gasTypeId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasCostsTableAnnotationComposer(
              $db: $db,
              $table: $db.gasCosts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$GasTypesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $GasTypesTable,
    GasTypeData,
    $$GasTypesTableFilterComposer,
    $$GasTypesTableOrderingComposer,
    $$GasTypesTableAnnotationComposer,
    $$GasTypesTableCreateCompanionBuilder,
    $$GasTypesTableUpdateCompanionBuilder,
    (GasTypeData, $$GasTypesTableReferences),
    GasTypeData,
    PrefetchHooks Function({bool gasCostsRefs})> {
  $$GasTypesTableTableManager(_$AppDatabase db, $GasTypesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$GasTypesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$GasTypesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$GasTypesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> displayName = const Value.absent(),
            Value<bool> isDefault = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              GasTypesCompanion(
            id: id,
            name: name,
            displayName: displayName,
            isDefault: isDefault,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required String displayName,
            Value<bool> isDefault = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              GasTypesCompanion.insert(
            id: id,
            name: name,
            displayName: displayName,
            isDefault: isDefault,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$GasTypesTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({gasCostsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (gasCostsRefs) db.gasCosts],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (gasCostsRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable:
                            $$GasTypesTableReferences._gasCostsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$GasTypesTableReferences(db, table, p0)
                                .gasCostsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.gasTypeId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$GasTypesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $GasTypesTable,
    GasTypeData,
    $$GasTypesTableFilterComposer,
    $$GasTypesTableOrderingComposer,
    $$GasTypesTableAnnotationComposer,
    $$GasTypesTableCreateCompanionBuilder,
    $$GasTypesTableUpdateCompanionBuilder,
    (GasTypeData, $$GasTypesTableReferences),
    GasTypeData,
    PrefetchHooks Function({bool gasCostsRefs})>;
typedef $$ThicknessRangesTableCreateCompanionBuilder = ThicknessRangesCompanion
    Function({
  Value<int> id,
  required String name,
  required String displayName,
  required double minThickness,
  Value<double?> maxThickness,
  required int sortOrder,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$ThicknessRangesTableUpdateCompanionBuilder = ThicknessRangesCompanion
    Function({
  Value<int> id,
  Value<String> name,
  Value<String> displayName,
  Value<double> minThickness,
  Value<double?> maxThickness,
  Value<int> sortOrder,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

final class $$ThicknessRangesTableReferences extends BaseReferences<
    _$AppDatabase, $ThicknessRangesTable, ThicknessRangeData> {
  $$ThicknessRangesTableReferences(
      super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$GasCostsTable, List<GasCostData>>
      _gasCostsRefsTable(_$AppDatabase db) =>
          MultiTypedResultKey.fromTable(db.gasCosts,
              aliasName: $_aliasNameGenerator(
                  db.thicknessRanges.id, db.gasCosts.thicknessRangeId));

  $$GasCostsTableProcessedTableManager get gasCostsRefs {
    final manager = $$GasCostsTableTableManager($_db, $_db.gasCosts)
        .filter((f) => f.thicknessRangeId.id($_item.id));

    final cache = $_typedResult.readTableOrNull(_gasCostsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$ThicknessRangesTableFilterComposer
    extends Composer<_$AppDatabase, $ThicknessRangesTable> {
  $$ThicknessRangesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get minThickness => $composableBuilder(
      column: $table.minThickness, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get maxThickness => $composableBuilder(
      column: $table.maxThickness, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get sortOrder => $composableBuilder(
      column: $table.sortOrder, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  Expression<bool> gasCostsRefs(
      Expression<bool> Function($$GasCostsTableFilterComposer f) f) {
    final $$GasCostsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.gasCosts,
        getReferencedColumn: (t) => t.thicknessRangeId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasCostsTableFilterComposer(
              $db: $db,
              $table: $db.gasCosts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$ThicknessRangesTableOrderingComposer
    extends Composer<_$AppDatabase, $ThicknessRangesTable> {
  $$ThicknessRangesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get name => $composableBuilder(
      column: $table.name, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get minThickness => $composableBuilder(
      column: $table.minThickness,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get maxThickness => $composableBuilder(
      column: $table.maxThickness,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get sortOrder => $composableBuilder(
      column: $table.sortOrder, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$ThicknessRangesTableAnnotationComposer
    extends Composer<_$AppDatabase, $ThicknessRangesTable> {
  $$ThicknessRangesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get displayName => $composableBuilder(
      column: $table.displayName, builder: (column) => column);

  GeneratedColumn<double> get minThickness => $composableBuilder(
      column: $table.minThickness, builder: (column) => column);

  GeneratedColumn<double> get maxThickness => $composableBuilder(
      column: $table.maxThickness, builder: (column) => column);

  GeneratedColumn<int> get sortOrder =>
      $composableBuilder(column: $table.sortOrder, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  Expression<T> gasCostsRefs<T extends Object>(
      Expression<T> Function($$GasCostsTableAnnotationComposer a) f) {
    final $$GasCostsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.gasCosts,
        getReferencedColumn: (t) => t.thicknessRangeId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasCostsTableAnnotationComposer(
              $db: $db,
              $table: $db.gasCosts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$ThicknessRangesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ThicknessRangesTable,
    ThicknessRangeData,
    $$ThicknessRangesTableFilterComposer,
    $$ThicknessRangesTableOrderingComposer,
    $$ThicknessRangesTableAnnotationComposer,
    $$ThicknessRangesTableCreateCompanionBuilder,
    $$ThicknessRangesTableUpdateCompanionBuilder,
    (ThicknessRangeData, $$ThicknessRangesTableReferences),
    ThicknessRangeData,
    PrefetchHooks Function({bool gasCostsRefs})> {
  $$ThicknessRangesTableTableManager(
      _$AppDatabase db, $ThicknessRangesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ThicknessRangesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ThicknessRangesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ThicknessRangesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> name = const Value.absent(),
            Value<String> displayName = const Value.absent(),
            Value<double> minThickness = const Value.absent(),
            Value<double?> maxThickness = const Value.absent(),
            Value<int> sortOrder = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              ThicknessRangesCompanion(
            id: id,
            name: name,
            displayName: displayName,
            minThickness: minThickness,
            maxThickness: maxThickness,
            sortOrder: sortOrder,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String name,
            required String displayName,
            required double minThickness,
            Value<double?> maxThickness = const Value.absent(),
            required int sortOrder,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              ThicknessRangesCompanion.insert(
            id: id,
            name: name,
            displayName: displayName,
            minThickness: minThickness,
            maxThickness: maxThickness,
            sortOrder: sortOrder,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$ThicknessRangesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({gasCostsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (gasCostsRefs) db.gasCosts],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (gasCostsRefs)
                    await $_getPrefetchedData(
                        currentTable: table,
                        referencedTable: $$ThicknessRangesTableReferences
                            ._gasCostsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$ThicknessRangesTableReferences(db, table, p0)
                                .gasCostsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.thicknessRangeId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$ThicknessRangesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ThicknessRangesTable,
    ThicknessRangeData,
    $$ThicknessRangesTableFilterComposer,
    $$ThicknessRangesTableOrderingComposer,
    $$ThicknessRangesTableAnnotationComposer,
    $$ThicknessRangesTableCreateCompanionBuilder,
    $$ThicknessRangesTableUpdateCompanionBuilder,
    (ThicknessRangeData, $$ThicknessRangesTableReferences),
    ThicknessRangeData,
    PrefetchHooks Function({bool gasCostsRefs})>;
typedef $$GasCostsTableCreateCompanionBuilder = GasCostsCompanion Function({
  Value<int> id,
  required int gasTypeId,
  required int thicknessRangeId,
  required double costPerHour,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$GasCostsTableUpdateCompanionBuilder = GasCostsCompanion Function({
  Value<int> id,
  Value<int> gasTypeId,
  Value<int> thicknessRangeId,
  Value<double> costPerHour,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

final class $$GasCostsTableReferences
    extends BaseReferences<_$AppDatabase, $GasCostsTable, GasCostData> {
  $$GasCostsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $GasTypesTable _gasTypeIdTable(_$AppDatabase db) => db.gasTypes
      .createAlias($_aliasNameGenerator(db.gasCosts.gasTypeId, db.gasTypes.id));

  $$GasTypesTableProcessedTableManager get gasTypeId {
    final manager = $$GasTypesTableTableManager($_db, $_db.gasTypes)
        .filter((f) => f.id($_item.gasTypeId));
    final item = $_typedResult.readTableOrNull(_gasTypeIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static $ThicknessRangesTable _thicknessRangeIdTable(_$AppDatabase db) =>
      db.thicknessRanges.createAlias($_aliasNameGenerator(
          db.gasCosts.thicknessRangeId, db.thicknessRanges.id));

  $$ThicknessRangesTableProcessedTableManager get thicknessRangeId {
    final manager =
        $$ThicknessRangesTableTableManager($_db, $_db.thicknessRanges)
            .filter((f) => f.id($_item.thicknessRangeId));
    final item = $_typedResult.readTableOrNull(_thicknessRangeIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$GasCostsTableFilterComposer
    extends Composer<_$AppDatabase, $GasCostsTable> {
  $$GasCostsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get costPerHour => $composableBuilder(
      column: $table.costPerHour, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$GasTypesTableFilterComposer get gasTypeId {
    final $$GasTypesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.gasTypeId,
        referencedTable: $db.gasTypes,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasTypesTableFilterComposer(
              $db: $db,
              $table: $db.gasTypes,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ThicknessRangesTableFilterComposer get thicknessRangeId {
    final $$ThicknessRangesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.thicknessRangeId,
        referencedTable: $db.thicknessRanges,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ThicknessRangesTableFilterComposer(
              $db: $db,
              $table: $db.thicknessRanges,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$GasCostsTableOrderingComposer
    extends Composer<_$AppDatabase, $GasCostsTable> {
  $$GasCostsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get costPerHour => $composableBuilder(
      column: $table.costPerHour, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$GasTypesTableOrderingComposer get gasTypeId {
    final $$GasTypesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.gasTypeId,
        referencedTable: $db.gasTypes,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasTypesTableOrderingComposer(
              $db: $db,
              $table: $db.gasTypes,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ThicknessRangesTableOrderingComposer get thicknessRangeId {
    final $$ThicknessRangesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.thicknessRangeId,
        referencedTable: $db.thicknessRanges,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ThicknessRangesTableOrderingComposer(
              $db: $db,
              $table: $db.thicknessRanges,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$GasCostsTableAnnotationComposer
    extends Composer<_$AppDatabase, $GasCostsTable> {
  $$GasCostsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<double> get costPerHour => $composableBuilder(
      column: $table.costPerHour, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$GasTypesTableAnnotationComposer get gasTypeId {
    final $$GasTypesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.gasTypeId,
        referencedTable: $db.gasTypes,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$GasTypesTableAnnotationComposer(
              $db: $db,
              $table: $db.gasTypes,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  $$ThicknessRangesTableAnnotationComposer get thicknessRangeId {
    final $$ThicknessRangesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.thicknessRangeId,
        referencedTable: $db.thicknessRanges,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ThicknessRangesTableAnnotationComposer(
              $db: $db,
              $table: $db.thicknessRanges,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$GasCostsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $GasCostsTable,
    GasCostData,
    $$GasCostsTableFilterComposer,
    $$GasCostsTableOrderingComposer,
    $$GasCostsTableAnnotationComposer,
    $$GasCostsTableCreateCompanionBuilder,
    $$GasCostsTableUpdateCompanionBuilder,
    (GasCostData, $$GasCostsTableReferences),
    GasCostData,
    PrefetchHooks Function({bool gasTypeId, bool thicknessRangeId})> {
  $$GasCostsTableTableManager(_$AppDatabase db, $GasCostsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$GasCostsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$GasCostsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$GasCostsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<int> gasTypeId = const Value.absent(),
            Value<int> thicknessRangeId = const Value.absent(),
            Value<double> costPerHour = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              GasCostsCompanion(
            id: id,
            gasTypeId: gasTypeId,
            thicknessRangeId: thicknessRangeId,
            costPerHour: costPerHour,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required int gasTypeId,
            required int thicknessRangeId,
            required double costPerHour,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              GasCostsCompanion.insert(
            id: id,
            gasTypeId: gasTypeId,
            thicknessRangeId: thicknessRangeId,
            costPerHour: costPerHour,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$GasCostsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: (
              {gasTypeId = false, thicknessRangeId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (gasTypeId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.gasTypeId,
                    referencedTable:
                        $$GasCostsTableReferences._gasTypeIdTable(db),
                    referencedColumn:
                        $$GasCostsTableReferences._gasTypeIdTable(db).id,
                  ) as T;
                }
                if (thicknessRangeId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.thicknessRangeId,
                    referencedTable:
                        $$GasCostsTableReferences._thicknessRangeIdTable(db),
                    referencedColumn:
                        $$GasCostsTableReferences._thicknessRangeIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$GasCostsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $GasCostsTable,
    GasCostData,
    $$GasCostsTableFilterComposer,
    $$GasCostsTableOrderingComposer,
    $$GasCostsTableAnnotationComposer,
    $$GasCostsTableCreateCompanionBuilder,
    $$GasCostsTableUpdateCompanionBuilder,
    (GasCostData, $$GasCostsTableReferences),
    GasCostData,
    PrefetchHooks Function({bool gasTypeId, bool thicknessRangeId})>;
typedef $$AppSettingsTableCreateCompanionBuilder = AppSettingsCompanion
    Function({
  Value<int> id,
  required String category,
  required String key,
  required String value,
  required String valueType,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$AppSettingsTableUpdateCompanionBuilder = AppSettingsCompanion
    Function({
  Value<int> id,
  Value<String> category,
  Value<String> key,
  Value<String> value,
  Value<String> valueType,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

class $$AppSettingsTableFilterComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get valueType => $composableBuilder(
      column: $table.valueType, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$AppSettingsTableOrderingComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get key => $composableBuilder(
      column: $table.key, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get value => $composableBuilder(
      column: $table.value, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get valueType => $composableBuilder(
      column: $table.valueType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$AppSettingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $AppSettingsTable> {
  $$AppSettingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get category =>
      $composableBuilder(column: $table.category, builder: (column) => column);

  GeneratedColumn<String> get key =>
      $composableBuilder(column: $table.key, builder: (column) => column);

  GeneratedColumn<String> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumn<String> get valueType =>
      $composableBuilder(column: $table.valueType, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$AppSettingsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $AppSettingsTable,
    AppSettingData,
    $$AppSettingsTableFilterComposer,
    $$AppSettingsTableOrderingComposer,
    $$AppSettingsTableAnnotationComposer,
    $$AppSettingsTableCreateCompanionBuilder,
    $$AppSettingsTableUpdateCompanionBuilder,
    (
      AppSettingData,
      BaseReferences<_$AppDatabase, $AppSettingsTable, AppSettingData>
    ),
    AppSettingData,
    PrefetchHooks Function()> {
  $$AppSettingsTableTableManager(_$AppDatabase db, $AppSettingsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AppSettingsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AppSettingsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AppSettingsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> category = const Value.absent(),
            Value<String> key = const Value.absent(),
            Value<String> value = const Value.absent(),
            Value<String> valueType = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              AppSettingsCompanion(
            id: id,
            category: category,
            key: key,
            value: value,
            valueType: valueType,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String category,
            required String key,
            required String value,
            required String valueType,
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              AppSettingsCompanion.insert(
            id: id,
            category: category,
            key: key,
            value: value,
            valueType: valueType,
            description: description,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$AppSettingsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $AppSettingsTable,
    AppSettingData,
    $$AppSettingsTableFilterComposer,
    $$AppSettingsTableOrderingComposer,
    $$AppSettingsTableAnnotationComposer,
    $$AppSettingsTableCreateCompanionBuilder,
    $$AppSettingsTableUpdateCompanionBuilder,
    (
      AppSettingData,
      BaseReferences<_$AppDatabase, $AppSettingsTable, AppSettingData>
    ),
    AppSettingData,
    PrefetchHooks Function()>;
typedef $$LicensesTableCreateCompanionBuilder = LicensesCompanion Function({
  Value<int> id,
  required String serialNumber,
  required String hardwareId,
  required bool isValid,
  required String responseCode,
  required String message,
  Value<DateTime?> expiryDate,
  required DateTime lastValidated,
  required DateTime firstValidated,
  Value<String?> serverUrl,
  Value<String?> serverResponse,
  Value<bool> isActive,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});
typedef $$LicensesTableUpdateCompanionBuilder = LicensesCompanion Function({
  Value<int> id,
  Value<String> serialNumber,
  Value<String> hardwareId,
  Value<bool> isValid,
  Value<String> responseCode,
  Value<String> message,
  Value<DateTime?> expiryDate,
  Value<DateTime> lastValidated,
  Value<DateTime> firstValidated,
  Value<String?> serverUrl,
  Value<String?> serverResponse,
  Value<bool> isActive,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
});

class $$LicensesTableFilterComposer
    extends Composer<_$AppDatabase, $LicensesTable> {
  $$LicensesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get serialNumber => $composableBuilder(
      column: $table.serialNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get hardwareId => $composableBuilder(
      column: $table.hardwareId, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isValid => $composableBuilder(
      column: $table.isValid, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get responseCode => $composableBuilder(
      column: $table.responseCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get expiryDate => $composableBuilder(
      column: $table.expiryDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastValidated => $composableBuilder(
      column: $table.lastValidated, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get firstValidated => $composableBuilder(
      column: $table.firstValidated,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get serverUrl => $composableBuilder(
      column: $table.serverUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get serverResponse => $composableBuilder(
      column: $table.serverResponse,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));
}

class $$LicensesTableOrderingComposer
    extends Composer<_$AppDatabase, $LicensesTable> {
  $$LicensesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get serialNumber => $composableBuilder(
      column: $table.serialNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get hardwareId => $composableBuilder(
      column: $table.hardwareId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isValid => $composableBuilder(
      column: $table.isValid, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get responseCode => $composableBuilder(
      column: $table.responseCode,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get expiryDate => $composableBuilder(
      column: $table.expiryDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastValidated => $composableBuilder(
      column: $table.lastValidated,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get firstValidated => $composableBuilder(
      column: $table.firstValidated,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get serverUrl => $composableBuilder(
      column: $table.serverUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get serverResponse => $composableBuilder(
      column: $table.serverResponse,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));
}

class $$LicensesTableAnnotationComposer
    extends Composer<_$AppDatabase, $LicensesTable> {
  $$LicensesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get serialNumber => $composableBuilder(
      column: $table.serialNumber, builder: (column) => column);

  GeneratedColumn<String> get hardwareId => $composableBuilder(
      column: $table.hardwareId, builder: (column) => column);

  GeneratedColumn<bool> get isValid =>
      $composableBuilder(column: $table.isValid, builder: (column) => column);

  GeneratedColumn<String> get responseCode => $composableBuilder(
      column: $table.responseCode, builder: (column) => column);

  GeneratedColumn<String> get message =>
      $composableBuilder(column: $table.message, builder: (column) => column);

  GeneratedColumn<DateTime> get expiryDate => $composableBuilder(
      column: $table.expiryDate, builder: (column) => column);

  GeneratedColumn<DateTime> get lastValidated => $composableBuilder(
      column: $table.lastValidated, builder: (column) => column);

  GeneratedColumn<DateTime> get firstValidated => $composableBuilder(
      column: $table.firstValidated, builder: (column) => column);

  GeneratedColumn<String> get serverUrl =>
      $composableBuilder(column: $table.serverUrl, builder: (column) => column);

  GeneratedColumn<String> get serverResponse => $composableBuilder(
      column: $table.serverResponse, builder: (column) => column);

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);
}

class $$LicensesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $LicensesTable,
    LicenseData,
    $$LicensesTableFilterComposer,
    $$LicensesTableOrderingComposer,
    $$LicensesTableAnnotationComposer,
    $$LicensesTableCreateCompanionBuilder,
    $$LicensesTableUpdateCompanionBuilder,
    (LicenseData, BaseReferences<_$AppDatabase, $LicensesTable, LicenseData>),
    LicenseData,
    PrefetchHooks Function()> {
  $$LicensesTableTableManager(_$AppDatabase db, $LicensesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$LicensesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$LicensesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$LicensesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> serialNumber = const Value.absent(),
            Value<String> hardwareId = const Value.absent(),
            Value<bool> isValid = const Value.absent(),
            Value<String> responseCode = const Value.absent(),
            Value<String> message = const Value.absent(),
            Value<DateTime?> expiryDate = const Value.absent(),
            Value<DateTime> lastValidated = const Value.absent(),
            Value<DateTime> firstValidated = const Value.absent(),
            Value<String?> serverUrl = const Value.absent(),
            Value<String?> serverResponse = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              LicensesCompanion(
            id: id,
            serialNumber: serialNumber,
            hardwareId: hardwareId,
            isValid: isValid,
            responseCode: responseCode,
            message: message,
            expiryDate: expiryDate,
            lastValidated: lastValidated,
            firstValidated: firstValidated,
            serverUrl: serverUrl,
            serverResponse: serverResponse,
            isActive: isActive,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String serialNumber,
            required String hardwareId,
            required bool isValid,
            required String responseCode,
            required String message,
            Value<DateTime?> expiryDate = const Value.absent(),
            required DateTime lastValidated,
            required DateTime firstValidated,
            Value<String?> serverUrl = const Value.absent(),
            Value<String?> serverResponse = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
          }) =>
              LicensesCompanion.insert(
            id: id,
            serialNumber: serialNumber,
            hardwareId: hardwareId,
            isValid: isValid,
            responseCode: responseCode,
            message: message,
            expiryDate: expiryDate,
            lastValidated: lastValidated,
            firstValidated: firstValidated,
            serverUrl: serverUrl,
            serverResponse: serverResponse,
            isActive: isActive,
            createdAt: createdAt,
            updatedAt: updatedAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$LicensesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $LicensesTable,
    LicenseData,
    $$LicensesTableFilterComposer,
    $$LicensesTableOrderingComposer,
    $$LicensesTableAnnotationComposer,
    $$LicensesTableCreateCompanionBuilder,
    $$LicensesTableUpdateCompanionBuilder,
    (LicenseData, BaseReferences<_$AppDatabase, $LicensesTable, LicenseData>),
    LicenseData,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$ClientsTableTableManager get clients =>
      $$ClientsTableTableManager(_db, _db.clients);
  $$CalculationsTableTableManager get calculations =>
      $$CalculationsTableTableManager(_db, _db.calculations);
  $$ConfigurationsTableTableManager get configurations =>
      $$ConfigurationsTableTableManager(_db, _db.configurations);
  $$MaterialsTableTableManager get materials =>
      $$MaterialsTableTableManager(_db, _db.materials);
  $$GasTypesTableTableManager get gasTypes =>
      $$GasTypesTableTableManager(_db, _db.gasTypes);
  $$ThicknessRangesTableTableManager get thicknessRanges =>
      $$ThicknessRangesTableTableManager(_db, _db.thicknessRanges);
  $$GasCostsTableTableManager get gasCosts =>
      $$GasCostsTableTableManager(_db, _db.gasCosts);
  $$AppSettingsTableTableManager get appSettings =>
      $$AppSettingsTableTableManager(_db, _db.appSettings);
  $$LicensesTableTableManager get licenses =>
      $$LicensesTableTableManager(_db, _db.licenses);
}
