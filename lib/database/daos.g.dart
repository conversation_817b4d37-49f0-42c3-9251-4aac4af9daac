// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daos.dart';

// ignore_for_file: type=lint
mixin _$MaterialsDaoMixin on DatabaseAccessor<AppDatabase> {
  $MaterialsTable get materials => attachedDatabase.materials;
}
mixin _$GasTypesDaoMixin on DatabaseAccessor<AppDatabase> {
  $GasTypesTable get gasTypes => attachedDatabase.gasTypes;
}
mixin _$ThicknessRangesDaoMixin on DatabaseAccessor<AppDatabase> {
  $ThicknessRangesTable get thicknessRanges => attachedDatabase.thicknessRanges;
}
mixin _$GasCostsDaoMixin on DatabaseAccessor<AppDatabase> {
  $GasTypesTable get gasTypes => attachedDatabase.gasTypes;
  $ThicknessRangesTable get thicknessRanges => attachedDatabase.thicknessRanges;
  $GasCostsTable get gasCosts => attachedDatabase.gasCosts;
}
mixin _$LicensesDaoMixin on DatabaseAccessor<AppDatabase> {
  $LicensesTable get licenses => attachedDatabase.licenses;
}
mixin _$AppSettingsDaoMixin on DatabaseAccessor<AppDatabase> {
  $AppSettingsTable get appSettings => attachedDatabase.appSettings;
}
