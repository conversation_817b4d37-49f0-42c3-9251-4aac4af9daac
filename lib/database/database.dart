import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

import 'tables.dart';
import '../models/client.dart';
import '../models/calculation.dart';

part 'database.g.dart';

@DriftDatabase(tables: [Clients, Calculations, Configurations, Materials, GasTypes, ThicknessRanges, GasCosts, AppSettings, Licenses])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());
  AppDatabase.forTesting() : super(_openTestConnection());

  @override
  int get schemaVersion => 3;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        // Seed initial data for new installations
        await _seedInitialNormalizedData();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 2) {
          // Migration from schema version 1 to 2: Normalize configuration data
          await _migrateToNormalizedSchema(m);
        }
        if (from < 3) {
          // Migration from schema version 2 to 3: Add license table
          await _addLicenseTable(m);
        }
      },
    );
  }

  // Client operations
  Future<List<Client>> getAllClients() async {
    final rows = await select(clients).get();
    return rows.map((row) => _clientFromRow(row)).toList();
  }

  Future<Client?> getClientById(String id) async {
    final row = await (select(clients)..where((c) => c.id.equals(id))).getSingleOrNull();
    return row != null ? _clientFromRow(row) : null;
  }

  Future<void> insertClient(Client client) async {
    await into(clients).insert(ClientsCompanion(
      id: Value(client.id),
      name: Value(client.name),
      reference: Value(client.reference),
      company: Value(client.company),
      email: Value(client.email),
      phone: Value(client.phone),
      projects: Value(client.projects),
      totalSpent: Value(client.totalSpent),
      createdAt: Value(client.createdAt),
    ));
  }

  Future<void> updateClient(Client client) async {
    await (update(clients)..where((c) => c.id.equals(client.id))).write(ClientsCompanion(
      name: Value(client.name),
      reference: Value(client.reference),
      company: Value(client.company),
      email: Value(client.email),
      phone: Value(client.phone),
      projects: Value(client.projects),
      totalSpent: Value(client.totalSpent),
    ));
  }

  Future<void> deleteClient(String id) async {
    await (delete(clients)..where((c) => c.id.equals(id))).go();
  }

  // Calculation operations
  Future<List<Calculation>> getAllCalculations() async {
    final rows = await select(calculations).get();
    return rows.map((row) => _calculationFromRow(row)).toList();
  }

  Future<Calculation?> getCalculationById(String id) async {
    final row = await (select(calculations)..where((c) => c.id.equals(id))).getSingleOrNull();
    return row != null ? _calculationFromRow(row) : null;
  }

  Future<void> insertCalculation(Calculation calculation) async {
    await into(calculations).insert(CalculationsCompanion(
      id: Value(calculation.id),
      clientName: Value(calculation.clientName),
      clientReference: Value(calculation.clientReference),
      material: Value(calculation.material),
      thickness: Value(calculation.thickness),
      gasType: Value(calculation.gasType),
      linearMeters: Value(calculation.linearMeters),
      cuttingSpeed: Value(calculation.cuttingSpeed),
      designProvided: Value(calculation.designProvided),
      designReference: Value(calculation.designReference),
      totalPrice: Value(calculation.totalPrice),
      createdAt: Value(calculation.createdAt),
      status: Value(calculation.status.toString().split('.').last),
    ));
  }

  Future<void> updateCalculation(Calculation calculation) async {
    await (update(calculations)..where((c) => c.id.equals(calculation.id))).write(CalculationsCompanion(
      clientName: Value(calculation.clientName),
      clientReference: Value(calculation.clientReference),
      material: Value(calculation.material),
      thickness: Value(calculation.thickness),
      gasType: Value(calculation.gasType),
      linearMeters: Value(calculation.linearMeters),
      cuttingSpeed: Value(calculation.cuttingSpeed),
      designProvided: Value(calculation.designProvided),
      designReference: Value(calculation.designReference),
      totalPrice: Value(calculation.totalPrice),
      status: Value(calculation.status.toString().split('.').last),
    ));
  }

  Future<void> deleteCalculation(String id) async {
    await (delete(calculations)..where((c) => c.id.equals(id))).go();
  }

  // Configuration operations
  Future<String?> getConfigValue(String key) async {
    final row = await (select(configurations)..where((c) => c.key.equals(key))).getSingleOrNull();
    return row?.value;
  }

  Future<void> setConfigValue(String key, String value) async {
    await into(configurations).insertOnConflictUpdate(ConfigurationsCompanion(
      key: Value(key),
      value: Value(value),
      updatedAt: Value(DateTime.now()),
    ));
  }

  Future<void> deleteConfigValue(String key) async {
    await (delete(configurations)..where((c) => c.key.equals(key))).go();
  }

  /// Get all configuration keys that start with a prefix
  Future<Map<String, String>> getConfigValuesByPrefix(String prefix) async {
    final results = await (select(configurations)..where((c) => c.key.like('$prefix%'))).get();

    final Map<String, String> values = {};
    for (final result in results) {
      if (result.value.isNotEmpty) {
        values[result.key] = result.value;
      }
    }
    return values;
  }

  // Helper methods to convert database rows to model objects
  Client _clientFromRow(ClientData row) {
    return Client(
      id: row.id,
      name: row.name,
      reference: row.reference,
      company: row.company,
      email: row.email,
      phone: row.phone,
      projects: row.projects,
      totalSpent: row.totalSpent,
      createdAt: row.createdAt,
    );
  }

  Calculation _calculationFromRow(CalculationData row) {
    return Calculation(
      id: row.id,
      clientName: row.clientName,
      clientReference: row.clientReference,
      material: row.material,
      thickness: row.thickness,
      gasType: row.gasType,
      linearMeters: row.linearMeters,
      cuttingSpeed: row.cuttingSpeed,
      designProvided: row.designProvided,
      designReference: row.designReference,
      totalPrice: row.totalPrice,
      createdAt: row.createdAt,
      status: CalculationStatus.values.firstWhere(
        (e) => e.toString().split('.').last == row.status,
        orElse: () => CalculationStatus.pending,
      ),
      // Add default values for new required fields
      cuttingDurationHours: 1.0,
      setupFees: 0.0,
      machineCost: 0.0,
      gasCost: 0.0,
      designCost: 0.0,
    );
  }

  // ============================================================================
  // MIGRATION METHODS
  // ============================================================================

  /// Migrate from denormalized configuration table to normalized schema
  Future<void> _migrateToNormalizedSchema(Migrator m) async {
    print('Starting migration to normalized schema...');

    try {
      // Create new normalized tables
      await m.createTable(materials);
      await m.createTable(gasTypes);
      await m.createTable(thicknessRanges);
      await m.createTable(gasCosts);
      await m.createTable(appSettings);

      // Read existing configuration data
      final existingConfigs = await select(configurations).get();
      print('Found ${existingConfigs.length} configuration entries to migrate');

      // Create maps to store parsed data
      final Map<String, double> materialRates = {};
      final Map<String, Map<String, double>> gasCostsData = {};
      final Map<String, String> appSettingsData = {};

      // Parse existing configuration data
      for (final config in existingConfigs) {
        final key = config.key;
        final value = config.value;

        if (key.startsWith('material_rate_')) {
          // Parse material rates: material_rate_acier -> acier: 25.0
          final materialName = key.substring('material_rate_'.length);
          final rate = double.tryParse(value) ?? 0.0;
          materialRates[materialName] = rate;
        } else if (key.startsWith('gas_cost_')) {
          // Parse gas costs: gas_cost_oxygene_o2_1_5_mm -> oxygene_o2: {1_5_mm: 8.0}
          final parts = key.substring('gas_cost_'.length).split('_');
          if (parts.length >= 3) {
            // Reconstruct gas type name and thickness range
            final thicknessRange = parts.skip(parts.length - 2).join('_'); // e.g., "1_5_mm"
            final gasTypeName = parts.take(parts.length - 2).join('_'); // e.g., "oxygene_o2"

            final cost = double.tryParse(value) ?? 0.0;
            gasCostsData.putIfAbsent(gasTypeName, () => {});
            gasCostsData[gasTypeName]![thicknessRange] = cost;
          }
        } else {
          // Store other settings as app settings
          appSettingsData[key] = value;
        }
      }

      // Migrate materials
      await _migrateMaterials(materialRates);
      print('✓ Materials migrated');

      // Migrate thickness ranges
      await _migrateThicknessRanges();
      print('✓ Thickness ranges migrated');

      // Migrate gas types and costs
      await _migrateGasTypesAndCosts(gasCostsData);
      print('✓ Gas types and costs migrated');

      // Migrate app settings
      await _migrateAppSettings(appSettingsData);
      print('✓ App settings migrated');

      print('Migration to normalized schema completed successfully!');
    } catch (e) {
      print('Error during migration: $e');
      rethrow;
    }
  }

  /// Migrate material data
  Future<void> _migrateMaterials(Map<String, double> materialRates) async {
    // Define material display names mapping
    final materialDisplayNames = {
      'acier': 'Acier (Steel)',
      'inox': 'Inox (Stainless Steel)',
      'cuivre': 'Cuivre (Copper)',
      'aluminum': 'Aluminum',
      'tole_galvanisee': 'Tôle Galvanisée',
    };

    // Get default material from app settings
    String? defaultMaterial;
    try {
      final defaultConfig = await (select(configurations)
          ..where((c) => c.key.equals('default_material'))).getSingleOrNull();
      defaultMaterial = defaultConfig?.value;
    } catch (e) {
      // Ignore error, will use fallback
    }

    for (final entry in materialRates.entries) {
      final materialName = entry.key;
      final rate = entry.value;
      final displayName = materialDisplayNames[materialName] ?? materialName;
      final isDefault = materialName == (defaultMaterial ?? 'acier');

      await into(materials).insert(MaterialsCompanion(
        name: Value(materialName),
        displayName: Value(displayName),
        rate: Value(rate),
        isDefault: Value(isDefault),
      ));
    }
  }

  /// Migrate thickness ranges
  Future<void> _migrateThicknessRanges() async {
    final thicknessRangeData = [
      {'name': '1_5_mm', 'displayName': '1-5 mm', 'min': 1.0, 'max': 5.0, 'sort': 1},
      {'name': '5_10_mm', 'displayName': '5-10 mm', 'min': 5.0, 'max': 10.0, 'sort': 2},
      {'name': '10_15_mm', 'displayName': '10-15 mm', 'min': 10.0, 'max': 15.0, 'sort': 3},
      {'name': 'gt_15_mm', 'displayName': '> 15 mm', 'min': 15.0, 'max': null, 'sort': 4},
    ];

    for (final range in thicknessRangeData) {
      await into(thicknessRanges).insert(ThicknessRangesCompanion(
        name: Value(range['name'] as String),
        displayName: Value(range['displayName'] as String),
        minThickness: Value(range['min'] as double),
        maxThickness: Value(range['max'] as double?),
        sortOrder: Value(range['sort'] as int),
      ));
    }
  }

  /// Migrate gas types and costs
  Future<void> _migrateGasTypesAndCosts(Map<String, Map<String, double>> gasCosts) async {
    // Define gas type display names mapping
    final gasTypeDisplayNames = {
      'oxygene_o2': 'Oxygène (O₂)',
      'azote_n2': 'Azote (N₂)',
      'air_comprime': 'Air Comprimé',
    };

    // Get default gas type from app settings
    String? defaultGasType;
    try {
      final defaultConfig = await (select(configurations)
          ..where((c) => c.key.equals('default_gas_type'))).getSingleOrNull();
      defaultGasType = defaultConfig?.value;
    } catch (e) {
      // Ignore error, will use fallback
    }

    // Insert gas types
    final gasTypeIds = <String, int>{};
    for (final gasTypeName in gasCosts.keys) {
      final displayName = gasTypeDisplayNames[gasTypeName] ?? gasTypeName;
      final isDefault = displayName == (defaultGasType ?? 'Oxygène (O₂)');

      final gasTypeId = await into(gasTypes).insert(GasTypesCompanion(
        name: Value(gasTypeName),
        displayName: Value(displayName),
        isDefault: Value(isDefault),
      ));
      gasTypeIds[gasTypeName] = gasTypeId;
    }

    // Get thickness range IDs
    final thicknessRangeIds = <String, int>{};
    final ranges = await select(thicknessRanges).get();
    for (final range in ranges) {
      thicknessRangeIds[range.name] = range.id;
    }

    // Insert gas costs
    for (final gasTypeEntry in gasCosts.entries) {
      final gasTypeName = gasTypeEntry.key;
      final gasTypeId = gasTypeIds[gasTypeName];
      if (gasTypeId == null) continue;

      for (final costEntry in gasTypeEntry.value.entries) {
        final thicknessRangeName = costEntry.key;
        final cost = costEntry.value;
        final thicknessRangeId = thicknessRangeIds[thicknessRangeName];
        if (thicknessRangeId == null) continue;

        await into(this.gasCosts).insert(GasCostsCompanion(
          gasTypeId: Value(gasTypeId),
          thicknessRangeId: Value(thicknessRangeId),
          costPerHour: Value(cost),
        ));
      }
    }
  }

  /// Migrate app settings
  Future<void> _migrateAppSettings(Map<String, String> settings) async {
    // Define setting categories and types
    final settingDefinitions = {
      'default_thickness': {'category': 'defaults', 'type': 'double'},
      'default_cutting_speed': {'category': 'defaults', 'type': 'double'},
      'design_service_price': {'category': 'pricing', 'type': 'double'},
      'price_per_meter': {'category': 'pricing', 'type': 'double'},
      'app_version': {'category': 'app', 'type': 'string'},
      'currency': {'category': 'app', 'type': 'string'},
      'tax_rate': {'category': 'pricing', 'type': 'double'},
      'min_order_amount': {'category': 'pricing', 'type': 'double'},
      'database_seeded': {'category': 'system', 'type': 'bool'},
      'database_seed_date': {'category': 'system', 'type': 'datetime'},
      'migration_version': {'category': 'system', 'type': 'string'},
    };

    for (final entry in settings.entries) {
      final key = entry.key;
      final value = entry.value;
      final definition = settingDefinitions[key];

      if (definition != null) {
        await into(appSettings).insert(AppSettingsCompanion(
          category: Value(definition['category']!),
          key: Value(key),
          value: Value(value),
          valueType: Value(definition['type']!),
        ));
      }
    }
  }

  /// Seed initial data for new installations
  Future<void> _seedInitialNormalizedData() async {
    print('Seeding initial normalized data...');

    try {
      // Seed thickness ranges
      await _seedThicknessRanges();

      // Seed default materials
      await _seedDefaultMaterials();

      // Seed default gas types
      await _seedDefaultGasTypes();

      // Seed default app settings
      await _seedDefaultAppSettings();

      print('Initial normalized data seeded successfully!');
    } catch (e) {
      print('Error seeding initial data: $e');
      rethrow;
    }
  }

  Future<void> _seedThicknessRanges() async {
    final ranges = [
      {'name': '1_5_mm', 'displayName': '1-5 mm', 'min': 1.0, 'max': 5.0, 'sort': 1},
      {'name': '5_10_mm', 'displayName': '5-10 mm', 'min': 5.0, 'max': 10.0, 'sort': 2},
      {'name': '10_15_mm', 'displayName': '10-15 mm', 'min': 10.0, 'max': 15.0, 'sort': 3},
      {'name': 'gt_15_mm', 'displayName': '> 15 mm', 'min': 15.0, 'max': null, 'sort': 4},
    ];

    for (final range in ranges) {
      await into(thicknessRanges).insert(ThicknessRangesCompanion(
        name: Value(range['name'] as String),
        displayName: Value(range['displayName'] as String),
        minThickness: Value(range['min'] as double),
        maxThickness: Value(range['max'] as double?),
        sortOrder: Value(range['sort'] as int),
      ));
    }
  }

  Future<void> _seedDefaultMaterials() async {
    final materialsData = [
      {'name': 'acier', 'displayName': 'Acier (Steel)', 'rate': 25.0, 'isDefault': true},
      {'name': 'inox', 'displayName': 'Inox (Stainless Steel)', 'rate': 35.0, 'isDefault': false},
      {'name': 'cuivre', 'displayName': 'Cuivre (Copper)', 'rate': 45.0, 'isDefault': false},
      {'name': 'aluminum', 'displayName': 'Aluminum', 'rate': 30.0, 'isDefault': false},
      {'name': 'tole_galvanisee', 'displayName': 'Tôle Galvanisée', 'rate': 22.0, 'isDefault': false},
    ];

    for (final material in materialsData) {
      await into(materials).insert(MaterialsCompanion(
        name: Value(material['name'] as String),
        displayName: Value(material['displayName'] as String),
        rate: Value(material['rate'] as double),
        isDefault: Value(material['isDefault'] as bool),
      ));
    }
  }

  Future<void> _seedDefaultGasTypes() async {
    // Insert gas types
    final gasTypesData = [
      {'name': 'oxygene_o2', 'displayName': 'Oxygène (O₂)', 'isDefault': true},
      {'name': 'azote_n2', 'displayName': 'Azote (N₂)', 'isDefault': false},
      {'name': 'air_comprime', 'displayName': 'Air Comprimé', 'isDefault': false},
    ];

    final gasTypeIds = <String, int>{};
    for (final gasType in gasTypesData) {
      final id = await into(gasTypes).insert(GasTypesCompanion(
        name: Value(gasType['name'] as String),
        displayName: Value(gasType['displayName'] as String),
        isDefault: Value(gasType['isDefault'] as bool),
      ));
      gasTypeIds[gasType['name'] as String] = id;
    }

    // Get thickness range IDs
    final thicknessRangeIds = <String, int>{};
    final ranges = await select(thicknessRanges).get();
    for (final range in ranges) {
      thicknessRangeIds[range.name] = range.id;
    }

    // Insert gas costs
    final gasCostsData = {
      'oxygene_o2': {'1_5_mm': 8.0, '5_10_mm': 12.0, '10_15_mm': 18.0, 'gt_15_mm': 25.0},
      'azote_n2': {'1_5_mm': 6.0, '5_10_mm': 9.0, '10_15_mm': 14.0, 'gt_15_mm': 20.0},
      'air_comprime': {'1_5_mm': 4.0, '5_10_mm': 6.0, '10_15_mm': 9.0, 'gt_15_mm': 12.0},
    };

    for (final gasTypeEntry in gasCostsData.entries) {
      final gasTypeName = gasTypeEntry.key;
      final gasTypeId = gasTypeIds[gasTypeName];
      if (gasTypeId == null) continue;

      for (final costEntry in gasTypeEntry.value.entries) {
        final thicknessRangeName = costEntry.key;
        final cost = costEntry.value;
        final thicknessRangeId = thicknessRangeIds[thicknessRangeName];
        if (thicknessRangeId == null) continue;

        await into(gasCosts).insert(GasCostsCompanion(
          gasTypeId: Value(gasTypeId),
          thicknessRangeId: Value(thicknessRangeId),
          costPerHour: Value(cost),
        ));
      }
    }
  }

  Future<void> _seedDefaultAppSettings() async {
    final settingsData = [
      {'category': 'defaults', 'key': 'default_thickness', 'value': '5.0', 'type': 'double'},
      {'category': 'defaults', 'key': 'default_cutting_speed', 'value': '100.0', 'type': 'double'},
      {'category': 'pricing', 'key': 'design_service_price', 'value': '50.0', 'type': 'double'},
      {'category': 'pricing', 'key': 'price_per_meter', 'value': '15.0', 'type': 'double'},
      {'category': 'app', 'key': 'app_version', 'value': '1.0.0', 'type': 'string'},
      {'category': 'app', 'key': 'currency', 'value': 'EUR', 'type': 'string'},
      {'category': 'pricing', 'key': 'tax_rate', 'value': '20.0', 'type': 'double'},
      {'category': 'pricing', 'key': 'min_order_amount', 'value': '25.0', 'type': 'double'},
      {'category': 'system', 'key': 'database_seeded', 'value': 'true', 'type': 'bool'},
      {'category': 'system', 'key': 'database_seed_date', 'value': DateTime.now().toIso8601String(), 'type': 'datetime'},
    ];

    for (final setting in settingsData) {
      await into(appSettings).insert(AppSettingsCompanion(
        category: Value(setting['category']!),
        key: Value(setting['key']!),
        value: Value(setting['value']!),
        valueType: Value(setting['type']!),
      ));
    }
  }

  /// Add license table for schema version 3
  Future<void> _addLicenseTable(Migrator m) async {
    if (kDebugMode) {
      print('Adding license table...');
    }

    try {
      // Create the new license table
      await m.createTable(licenses);

      if (kDebugMode) {
        print('✓ License table created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating license table: $e');
      }
      rethrow;
    }
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    // Make sure to initialize sqlite3_flutter_libs for mobile platforms
    if (Platform.isAndroid || Platform.isIOS) {
      await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
    }

    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'laser_cutting_calculator.db'));

    if (kDebugMode) {
      print('Database file path: ${file.path}');
    }

    return NativeDatabase.createInBackground(file);
  });
}

// Test-specific database connection
LazyDatabase _openTestConnection() {
  return LazyDatabase(() async {
    return NativeDatabase.memory();
  });
}