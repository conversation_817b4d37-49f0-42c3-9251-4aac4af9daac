import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/calculator_provider.dart';
import 'providers/client_provider.dart';
import 'providers/history_provider.dart';
import 'providers/configuration_provider.dart';
import 'providers/material_provider.dart';
import 'providers/gas_provider.dart';
import 'providers/license_provider.dart';
import 'screens/calculator_screen.dart';
import 'screens/client_management_screen.dart';
import 'screens/calculation_history_screen.dart';
import 'screens/configuration_screen.dart';
import 'widgets/sidebar.dart';
import 'widgets/license_guard.dart';
import 'services/database_service.dart';
import 'services/migration_service.dart';
import 'database/database_seeder.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize database service (starts empty)
  final databaseService = DatabaseService.instance;

  runApp(MyApp(databaseService: databaseService));
}

class MyApp extends StatelessWidget {
  final DatabaseService databaseService;

  const MyApp({super.key, required this.databaseService});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LicenseProvider(databaseService.database)),
        ChangeNotifierProvider(create: (_) => CalculatorProvider()),
        ChangeNotifierProvider(create: (_) => ClientProvider()),
        ChangeNotifierProvider(create: (_) => HistoryProvider()),
        ChangeNotifierProvider(create: (_) => ConfigurationProvider()),
        ChangeNotifierProvider(create: (_) => MaterialProvider()),
        ChangeNotifierProvider(create: (_) => GasProvider()),
      ],
      child: MaterialApp(
        title: 'Laser Cutting Calculator',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const AppInitializer(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class LaserCuttingCalculator extends StatefulWidget {
  const LaserCuttingCalculator({super.key});

  @override
  State<LaserCuttingCalculator> createState() => _LaserCuttingCalculatorState();
}

class _LaserCuttingCalculatorState extends State<LaserCuttingCalculator> {
  String _currentPage = 'calculator';
  Key _configurationKey = UniqueKey();

  Widget _getCurrentScreen() {
    switch (_currentPage) {
      case 'clients':
        return const ClientManagementScreen();
      case 'history':
        return const CalculationHistoryScreen();
      case 'configuration':
        return ConfigurationScreen(key: _configurationKey);
      default:
        return const CalculatorScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Row(
        children: [
          // Sidebar
          Sidebar(
            currentPage: _currentPage,
            onPageChange: (page) {
              setState(() {
                _currentPage = page;
                // Force configuration screen to rebuild when switching to it
                if (page == 'configuration') {
                  _configurationKey = UniqueKey();
                }
              });
            },
          ),
          
          // Main Content
          Expanded(
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(24),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[900],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.calculate,
                                  color: Colors.white,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Calculateur de Coût Découpe Laser',
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    'Système professionnel d\'estimation de projet et de devis',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                      if (_currentPage == 'calculator')
                        Consumer<CalculatorProvider>(
                          builder: (context, provider, child) {
                            return OutlinedButton.icon(
                              onPressed: () async {
                                await provider.resetCalculator();
                              },
                              icon: const Icon(Icons.refresh),
                              label: const Text('Réinitialiser'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.grey[700],
                                side: BorderSide(color: Colors.grey[300]!),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child: _getCurrentScreen(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  bool _isInitialized = false;
  String _initializationStatus = 'Initializing application...';

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Step 1: License validation (critical for app launch)
      if (!mounted) return;
      setState(() {
        _initializationStatus = 'Validating license...';
      });

      final licenseProvider = Provider.of<LicenseProvider>(context, listen: false);
      await licenseProvider.initialize();

      debugPrint('License validation completed during app initialization');

      // Step 2: Database setup
      if (!mounted) return;
      setState(() {
        _initializationStatus = 'Setting up database...';
      });

      final migrationService = MigrationService();
      await migrationService.runMigrations();

      // Check if database needs seeding and seed if necessary
      if (await DatabaseSeeder.needsSeeding()) {
        setState(() {
          _initializationStatus = 'Seeding initial data...';
        });
        await DatabaseSeeder.run();
      }

      // Step 3: Load application data
      if (!mounted) return;
      setState(() {
        _initializationStatus = 'Loading application data...';
      });

      final clientProvider = Provider.of<ClientProvider>(context, listen: false);
      final historyProvider = Provider.of<HistoryProvider>(context, listen: false);
      final materialProvider = Provider.of<MaterialProvider>(context, listen: false);
      final gasProvider = Provider.of<GasProvider>(context, listen: false);

      await Future.wait([
        clientProvider.loadClients(),
        historyProvider.loadCalculations(),
        materialProvider.initialize(),
        gasProvider.initialize(),
      ]);

      // Step 4: Finalization
      if (!mounted) return;
      setState(() {
        _initializationStatus = 'Finalizing...';
        _isInitialized = true;
      });

      debugPrint('App initialization completed successfully');

    } catch (e) {
      debugPrint('Error initializing app: $e');
      if (!mounted) return;
      setState(() {
        _initializationStatus = 'Initialization completed with errors';
        _isInitialized = true; // Continue even if there's an error
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                _initializationStatus,
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Please wait while the application starts up...',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return const LicenseGuard(
      child: LaserCuttingCalculator(),
    );
  }
}

