import 'package:flutter/material.dart';

/// Enhanced text field that supports customization with default values from configuration
class CustomizableTextField extends StatefulWidget {
  final String label;
  final String value;
  final String defaultValue;
  final bool isCustomized;
  final ValueChanged<String> onChanged;
  final VoidCallback onCustomize;
  final VoidCallback onCancel;
  final String? hint;
  final TextInputType? keyboardType;
  final String? suffix;

  const CustomizableTextField({
    super.key,
    required this.label,
    required this.value,
    required this.defaultValue,
    required this.isCustomized,
    required this.onChanged,
    required this.onCustomize,
    required this.onCancel,
    this.hint,
    this.keyboardType,
    this.suffix,
  });

  @override
  State<CustomizableTextField> createState() => _CustomizableTextFieldState();
}

class _CustomizableTextFieldState extends State<CustomizableTextField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _isEditing = false;
  bool _isUserTyping = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value);
    _focusNode = FocusNode();
    
    // Listen to focus changes to detect when editing starts/stops
    _focusNode.addListener(() {
      if (_focusNode.hasFocus && !widget.isCustomized) {
        // User clicked on the field while it's in default mode
        // Don't automatically start editing, they need to click customize icon
        _focusNode.unfocus();
      } else if (!_focusNode.hasFocus) {
        // Reset typing flag when focus is lost
        _isUserTyping = false;
      }
    });
  }

  @override
  void didUpdateWidget(CustomizableTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update controller text if the user is not actively typing
    // and the value has actually changed from an external source
    if (oldWidget.value != widget.value && !_isUserTyping && !_focusNode.hasFocus) {
      _controller.text = widget.value;
    }
    if (oldWidget.isCustomized != widget.isCustomized) {
      _isEditing = widget.isCustomized;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _startCustomization() {
    setState(() {
      _isEditing = true;
    });
    widget.onCustomize();
    // Focus the field after customization starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }



  void _cancelChanges() {
    setState(() {
      _isEditing = false;
    });
    _controller.text = widget.defaultValue;
    widget.onCancel();
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final isDefaultMode = !widget.isCustomized && !_isEditing;
    final isCustomMode = widget.isCustomized || _isEditing;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _controller,
                focusNode: _focusNode,
                enabled: isCustomMode,
                onChanged: isCustomMode ? (value) {
                  _isUserTyping = true;
                  widget.onChanged(value);
                } : null,
                keyboardType: widget.keyboardType,
                style: TextStyle(
                  color: isDefaultMode ? Colors.grey[600] : Colors.black87,
                  fontStyle: isDefaultMode ? FontStyle.italic : FontStyle.normal,
                ),
                decoration: InputDecoration(
                  hintText: widget.hint,
                  suffixText: widget.suffix,
                  border: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isDefaultMode ? Colors.grey[300]! : Colors.grey[400]!,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isDefaultMode ? Colors.grey[300]! : Colors.grey[400]!,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isCustomMode ? Colors.blue : Colors.grey[400]!,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                  filled: true,
                  fillColor: isDefaultMode ? Colors.grey[100] : Colors.grey[50],
                ),
              ),
            ),
            const SizedBox(width: 8),
            if (isDefaultMode) ...[
              // Customize icon - only shown in default mode
              IconButton(
                onPressed: _startCustomization,
                icon: const Icon(Icons.edit),
                tooltip: 'Personnaliser la valeur',
                iconSize: 20,
                color: Colors.blue[600],
                style: IconButton.styleFrom(
                  backgroundColor: Colors.blue[50],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ] else if (isCustomMode) ...[
              // Cancel icon - only shown in custom mode
              const SizedBox(width: 8),
              IconButton(
                onPressed: _cancelChanges,
                icon: const Icon(Icons.close),
                tooltip: 'Annuler',
                iconSize: 20,
                color: Colors.red[600],
                style: IconButton.styleFrom(
                  backgroundColor: Colors.red[50],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ),
        if (isDefaultMode)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Valeur par défaut de la configuration',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        if (isCustomMode && widget.isCustomized)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Valeur personnalisée',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}
