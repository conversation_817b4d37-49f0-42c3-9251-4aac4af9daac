import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/license_provider.dart';
import '../screens/license_validation_screen.dart';

class Sidebar extends StatelessWidget {
  final String currentPage;
  final ValueChanged<String> onPageChange;

  const Sidebar({
    super.key,
    required this.currentPage,
    required this.onPageChange,
  });

  @override
  Widget build(BuildContext context) {
    final menuItems = [
      {
        'id': 'calculator',
        'label': 'Calculateur',
        'icon': Icons.calculate,
        'description': 'Outil d\'estimation de prix',
      },
      {
        'id': 'clients',
        'label': 'Clients',
        'icon': Icons.people,
        'description': 'Gestion base de données clients',
      },
      {
        'id': 'history',
        'label': 'Historique',
        'icon': Icons.history,
        'description': 'Enregistrements de calculs',
      },
      {
        'id': 'configuration',
        'label': 'Configuration',
        'icon': Icons.settings,
        'description': 'Paramètres de tarification',
      },
    ];

    return Container(
      width: 256,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        border: Border(
          right: BorderSide(
            color: Colors.white.withOpacity(0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calculate,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Laser Pro',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      'Suite de Gestion',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Menu Items
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Column(
                children: menuItems.map((item) {
                  final isActive = currentPage == item['id'];
                  
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Material(
                      borderRadius: BorderRadius.circular(8),
                      color: isActive ? Colors.blue[600] : Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () => onPageChange(item['id'] as String),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          child: Row(
                            children: [
                              Icon(
                                item['icon'] as IconData,
                                color: isActive ? Colors.white : Colors.grey[700],
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      item['label'] as String,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: isActive ? Colors.white : Colors.grey[700],
                                      ),
                                    ),
                                    Text(
                                      item['description'] as String,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: isActive ? Colors.blue[100] : Colors.grey[500],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),

          // License Status Section
          Container(
            padding: const EdgeInsets.all(16.0),
            margin: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Consumer<LicenseProvider>(
              builder: (context, licenseProvider, _) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getLicenseIcon(licenseProvider),
                          color: _getLicenseColor(licenseProvider),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Statut Licence',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getLicenseStatusText(licenseProvider),
                      style: TextStyle(
                        fontSize: 10,
                        color: _getLicenseColor(licenseProvider),
                      ),
                    ),
                    // Show expiry information if available
                    if (licenseProvider.licenseExpiryDate != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        _getExpiryText(licenseProvider),
                        style: TextStyle(
                          fontSize: 9,
                          color: _getExpiryColor(licenseProvider),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _showLicenseScreen(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.grey[700],
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          textStyle: const TextStyle(fontSize: 11),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                        child: const Text('Gérer Licence'),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getLicenseIcon(LicenseProvider provider) {
    if (provider.isValidating) return Icons.hourglass_empty;
    if (provider.isLicenseValid) return Icons.check_circle;
    return Icons.error;
  }

  Color _getLicenseColor(LicenseProvider provider) {
    if (provider.isValidating) return Colors.orange;
    if (provider.isLicenseValid) return Colors.green;
    return Colors.red;
  }

  String _getLicenseStatusText(LicenseProvider provider) {
    if (provider.isValidating) return 'Validation...';
    if (provider.isLicenseValid) return 'Valide';
    return 'Non valide';
  }

  String _getExpiryText(LicenseProvider provider) {
    if (provider.licenseExpiryDate == null) return '';

    if (provider.isLicenseExpired) {
      return 'Expirée';
    } else if (provider.isLicenseExpiringSoon) {
      final days = provider.daysUntilExpiry ?? 0;
      return 'Expire dans $days j';
    } else {
      final expiryDate = provider.licenseExpiryDate!;
      return 'Expire ${expiryDate.day}/${expiryDate.month}/${expiryDate.year}';
    }
  }

  Color _getExpiryColor(LicenseProvider provider) {
    if (provider.isLicenseExpired) return Colors.red;
    if (provider.isLicenseExpiringSoon) return Colors.orange;
    return Colors.green;
  }

  void _showLicenseScreen(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LicenseValidationScreen(),
      ),
    );
  }
}
