import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/license_provider.dart';
import '../screens/license_validation_screen.dart';

/// Widget that protects content behind license validation
class LicenseGuard extends StatelessWidget {
  final Widget child;
  final String? lockedMessage;
  final Widget? customLockedScreen;

  const LicenseGuard({
    super.key,
    required this.child,
    this.lockedMessage,
    this.customLockedScreen,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LicenseProvider>(
      builder: (context, licenseProvider, _) {
        // Show loading while checking license
        if (licenseProvider.isValidating) {
          return const _LoadingScreen();
        }

        // Show locked screen if license is invalid
        if (!licenseProvider.isLicenseValid) {
          return customLockedScreen ?? 
                 _LicenseLockedScreen(message: lockedMessage);
        }

        // Show protected content
        return child;
      },
    );
  }
}

/// Loading screen shown while validating license
class _LoadingScreen extends StatelessWidget {
  const _LoadingScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Validation de la licence...',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
      ),
    );
  }
}

/// Screen shown when license is invalid or missing
class _LicenseLockedScreen extends StatelessWidget {
  final String? message;

  const _LicenseLockedScreen({this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Licence Requise'),
        backgroundColor: Theme.of(context).colorScheme.errorContainer,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.lock_outline,
                size: 80,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Licence Invalide',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                message ?? 
                'Une licence valide est requise pour utiliser cette fonctionnalité. '
                'Veuillez valider votre licence pour continuer.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => _showLicenseValidation(context),
                icon: const Icon(Icons.key),
                label: const Text('Valider la Licence'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Consumer<LicenseProvider>(
                builder: (context, licenseProvider, _) {
                  if (licenseProvider.storedCredentials != null) {
                    return OutlinedButton.icon(
                      onPressed: () => _retryValidation(context),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Réessayer'),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLicenseValidation(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LicenseValidationScreen(),
      ),
    );
  }

  void _retryValidation(BuildContext context) async {
    final licenseProvider = context.read<LicenseProvider>();
    await licenseProvider.validateWithStoredCredentials();
  }
}

/// Mixin that provides easy license checking for any widget
mixin LicenseAware<T extends StatefulWidget> on State<T> {
  bool get isLicenseValid {
    final licenseProvider = context.read<LicenseProvider>();
    return licenseProvider.isLicenseValid;
  }

  bool get isLicenseValidating {
    final licenseProvider = context.read<LicenseProvider>();
    return licenseProvider.isValidating;
  }

  void showLicenseValidationScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LicenseValidationScreen(),
      ),
    );
  }

  void showLicenseRequiredSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Licence valide requise pour cette action'),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: SnackBarAction(
          label: 'Valider',
          textColor: Colors.white,
          onPressed: showLicenseValidationScreen,
        ),
      ),
    );
  }
}

/// Extension to easily check license from any context
extension LicenseCheck on BuildContext {
  bool get hasValidLicense {
    final licenseProvider = read<LicenseProvider>();
    return licenseProvider.isLicenseValid;
  }

  void requireLicense(VoidCallback action) {
    if (hasValidLicense) {
      action();
    } else {
      ScaffoldMessenger.of(this).showSnackBar(
        SnackBar(
          content: const Text('Licence valide requise'),
          backgroundColor: Theme.of(this).colorScheme.error,
          action: SnackBarAction(
            label: 'Valider',
            textColor: Colors.white,
            onPressed: () {
              Navigator.of(this).push(
                MaterialPageRoute(
                  builder: (context) => const LicenseValidationScreen(),
                ),
              );
            },
          ),
        ),
      );
    }
  }
}
