import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/gas_provider.dart';

class GasTypeSelector extends StatelessWidget {
  final String selectedGas;
  final ValueChanged<String> onGasSelected;

  const GasTypeSelector({
    super.key,
    required this.selectedGas,
    required this.onGasSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<GasProvider>(
      builder: (context, gasProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Type de Gaz d\'Assistance *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            if (gasProvider.isLoading)
              const Center(child: CircularProgressIndicator())
            else if (gasProvider.error != null)
              Text('Erreur: ${gasProvider.error}')
            else
              Column(
                children: gasProvider.gasTypeEntries.map((entry) {
            final gasType = entry.key;
            final gasLabel = entry.value;
            final isSelected = selectedGas == gasType;
            
            String badgeText = '';
            if (gasType == 'oxygen') {
              badgeText = 'Premium';
            } else if (gasType == 'nitrogen') {
              badgeText = 'High Grade';
            } else {
              badgeText = 'Standard';
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () => onGasSelected(gasType),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected ? Colors.blue : Colors.grey[300]!,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected ? Colors.blue[50] : Colors.white,
                  ),
                  child: Row(
                    children: [
                      Radio<String>(
                        value: gasType,
                        groupValue: selectedGas,
                        onChanged: (value) {
                          if (value != null) {
                            onGasSelected(value);
                          }
                        },
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              gasLabel,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                badgeText,
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.black54,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
                }).toList(),
              ),
          ],
        );
      },
    );
  }
}
