import 'package:flutter/foundation.dart';
import '../models/job_config.dart';
import '../models/calculation.dart';
import '../models/client.dart';
import '../services/pricing_formula_service.dart';

import '../repositories/calculation_history_repository.dart';
import '../providers/client_provider.dart';
import '../providers/configuration_provider.dart';
import 'package:uuid/uuid.dart';

class CalculatorProvider with ChangeNotifier {
  final CalculationHistoryRepository _calculationRepository = CalculationHistoryRepository();
  final PricingFormulaService _pricingService = PricingFormulaService.instance;
  final ClientProvider _clientProvider = ClientProvider();
  final ConfigurationProvider _configurationProvider = ConfigurationProvider();
  final Uuid _uuid = const Uuid();

  JobConfig _config = JobConfig(
    clientName: '',
    clientReference: '',
    gasType: '',
    material: '',
    thickness: 1.0,
    cuttingSpeed: 100.0,
    linearMeters: 11.0,
    includeDesignFees: false,
    isNewClient: false, // Will be updated during initialization
    designReference: '',
    designServicePrice: 50.0, // Will be updated with database value
    cuttingDurationHours: 1.0,
    setupFees: 0.0,
    isDesignServicePriceCustomized: false,
  );
  double _totalPrice = 0.0;
  PricingBreakdown? _pricingBreakdown;
  bool _isInitialized = false;
  double _defaultDesignServicePrice = 50.0; // Cached default value from database

  JobConfig get config => _config;
  double get totalPrice => _totalPrice;
  PricingBreakdown? get pricingBreakdown => _pricingBreakdown;
  double get defaultDesignServicePrice => _defaultDesignServicePrice;

  /// Initialize the calculator with database values
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Load design service price from database
      final designServicePrice = await _configurationProvider.getDesignServicePrice();
      _defaultDesignServicePrice = designServicePrice;
      
      // Get initial config with proper isNewClient value
      final initialConfig = await JobConfig.initial;
      
      // Update the config with the database value and initial config
      _config = initialConfig.copyWith(designServicePrice: designServicePrice);
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing calculator provider: $e');
      // Keep default values if database loading fails
      _isInitialized = true;
    }
  }

  void updateConfig(JobConfig newConfig) {
    _config = newConfig;
    _calculatePrice();
    notifyListeners();
  }

  void updateClientName(String value) {
    _config = _config.copyWith(clientName: value);
    notifyListeners();
  }

  void updateClientReference(String value) {
    _config = _config.copyWith(clientReference: value);
    notifyListeners();
  }

  void updateGasType(String value) {
    _config = _config.copyWith(gasType: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateMaterial(String value) {
    _config = _config.copyWith(material: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateThickness(double value) {
    _config = _config.copyWith(thickness: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateCuttingSpeed(double value) {
    _config = _config.copyWith(cuttingSpeed: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateLinearMeters(double value) {
    _config = _config.copyWith(linearMeters: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateIsDesignProvided(bool value) {
    _config = _config.copyWith(includeDesignFees: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateIsNewClient(bool value) {
    _config = _config.copyWith(isNewClient: value);
    notifyListeners();
  }

  void updateDesignReference(String value) {
    _config = _config.copyWith(designReference: value);
    notifyListeners();
  }

  void updateDesignServicePrice(double value) {
    _config = _config.copyWith(designServicePrice: value);
    _calculatePrice();
    notifyListeners();
  }

  void startDesignServicePriceCustomization() {
    _config = _config.copyWith(isDesignServicePriceCustomized: true);
    notifyListeners();
  }

  void validateDesignServicePriceCustomization(double value) {
    _config = _config.copyWith(
      designServicePrice: value,
      isDesignServicePriceCustomized: true,
    );
    _calculatePrice();
    notifyListeners();
  }

  Future<void> cancelDesignServicePriceCustomization() async {
    // Use cached default value
    _config = _config.copyWith(
      designServicePrice: _defaultDesignServicePrice,
      isDesignServicePriceCustomized: false,
    );
    _calculatePrice();
    notifyListeners();
  }

  Future<double> getDefaultDesignServicePrice() async {
    return await _configurationProvider.getDesignServicePrice();
  }

  /// Refresh the design service price from database (called when configuration is updated)
  Future<void> refreshDesignServicePrice() async {
    final newPrice = await _configurationProvider.getDesignServicePrice();
    _defaultDesignServicePrice = newPrice;
    
    if (!_config.isDesignServicePriceCustomized) {
      _config = _config.copyWith(designServicePrice: newPrice);
      _calculatePrice();
    }
    notifyListeners();
  }

  void updateCuttingDurationHours(double value) {
    _config = _config.copyWith(cuttingDurationHours: value);
    _calculatePrice();
    notifyListeners();
  }

  void updateSetupFees(double value) {
    _config = _config.copyWith(setupFees: value);
    _calculatePrice();
    notifyListeners();
  }

  Future<void> resetCalculator() async {
    final initialConfig = await JobConfig.initial;
    _config = initialConfig.copyWith(designServicePrice: _defaultDesignServicePrice);
    _totalPrice = 0.0;
    _pricingBreakdown = null;
    notifyListeners();
  }

  Future<bool> saveCalculation() async {
    try {
      // Validate required fields
      if (_config.clientName.isEmpty || _config.clientReference.isEmpty) {
        return false;
      }

      if (_config.material.isEmpty || _config.gasType.isEmpty) {
        return false;
      }

      if (_totalPrice <= 0 || _pricingBreakdown == null) {
        return false;
      }

      // Create calculation object using pricing breakdown
      final calculation = Calculation(
        id: _uuid.v4(),
        clientName: _config.clientName,
        clientReference: _config.clientReference,
        material: _config.material,
        thickness: _config.thickness,
        gasType: _config.gasType,
        linearMeters: _config.linearMeters,
        cuttingSpeed: _config.cuttingSpeed,
        designProvided: _config.includeDesignFees,
        designReference: _config.includeDesignFees && _config.designReference.isNotEmpty
            ? _config.designReference
            : null,
        totalPrice: _totalPrice,
        createdAt: DateTime.now(),
        status: CalculationStatus.pending,
        cuttingDurationHours: _pricingBreakdown!.cuttingDurationHours,
        setupFees: _pricingBreakdown!.setupFees,
        machineCost: _pricingBreakdown!.machineCost,
        gasCost: _pricingBreakdown!.gasCost,
        designCost: _pricingBreakdown!.designCost,
      );

      // Save to database
      await _calculationRepository.addCalculation(calculation);

      // Update or create client with the new expense
      await _updateClientExpenses(calculation);

      return true;
    } catch (e) {
      debugPrint('Error saving calculation: $e');
      return false;
    }
  }

  /// Update client expenses when a calculation is saved
  Future<void> _updateClientExpenses(Calculation calculation) async {
    try {
      // Load clients to check if client exists
      await _clientProvider.loadClients();

      // Find existing client by reference
      final existingClient = _clientProvider.clients.firstWhere(
        (client) => client.reference == calculation.clientReference,
        orElse: () => Client(
          id: '',
          name: '',
          reference: '',
          projects: 0,
          totalSpent: 0.0,
          createdAt: DateTime.now(),
        ),
      );

      if (existingClient.id.isEmpty) {
        // Create new client
        final newClient = Client(
          id: _uuid.v4(),
          name: calculation.clientName,
          reference: calculation.clientReference,
          projects: 1,
          totalSpent: calculation.totalPrice,
          createdAt: DateTime.now(),
        );
        await _clientProvider.addClient(newClient);
      } else {
        // Update existing client
        await _clientProvider.updateClientTotalSpent(existingClient.id, calculation.totalPrice);
        await _clientProvider.incrementClientProjects(existingClient.id);
      }
    } catch (e) {
      debugPrint('Error updating client expenses: $e');
      // Don't throw error as calculation was already saved successfully
    }
  }

  void _calculatePrice() async {
    if (_config.gasType.isEmpty || _config.material.isEmpty) {
      _totalPrice = 0.0;
      _pricingBreakdown = null;
      return;
    }

    try {
      // Use the new pricing formula service
      _pricingBreakdown = await _pricingService.calculateTotalPrice(
        material: _config.material,
        cuttingDurationHours: _config.cuttingDurationHours,
        gasType: _config.gasType,
        thickness: _config.thickness,
        isDesignFeesIncluded: _config.includeDesignFees,
        customDesignCost: (_config.includeDesignFees && _config.isDesignServicePriceCustomized)
            ? _config.designServicePrice
            : null,
        customSetupFees: _config.setupFees > 0 ? _config.setupFees : null,
      );

      _totalPrice = _pricingBreakdown?.totalPrice ?? 0.0;
    } catch (e) {
      debugPrint('Error calculating price: $e');
      _totalPrice = 0.0;
      _pricingBreakdown = null;
    }
  }
}
