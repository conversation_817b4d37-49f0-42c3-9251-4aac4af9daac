import 'package:flutter/foundation.dart';
import 'package:drift/drift.dart';
import '../database/daos.dart';
import '../database/database.dart';
import '../services/database_service.dart';

class MaterialProvider with ChangeNotifier {
  late final MaterialsDao _materialsDao;
  
  Map<String, double> _materialRates = {};
  Map<String, String> _materialLabels = {};
  bool _isLoading = true;
  String? _error;

  MaterialProvider() {
    _materialsDao = MaterialsDao(DatabaseService.instance.database);
  }

  Map<String, double> get materialRates => _materialRates;
  Map<String, String> get materialLabels => _materialLabels;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize and load all materials
  Future<void> initialize() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await loadMaterials();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error initializing materials: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load all materials (default + custom)
  Future<void> loadMaterials() async {
    try {
      final materials = await _materialsDao.getAllMaterials();

      _materialRates.clear();
      _materialLabels.clear();

      for (final material in materials) {
        _materialRates[material.name] = material.rate;
        _materialLabels[material.name] = material.displayName;
      }

      _error = null;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading materials: $e');
      rethrow;
    }
  }

  /// Add a new custom material
  Future<bool> addMaterial(String name, double rate) async {
    try {
      // Create display name from name (capitalize first letter, replace underscores)
      final displayName = name.split('_').map((word) =>
        word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
      ).join(' ');

      await _materialsDao.insertMaterial(MaterialsCompanion(
        name: Value(name.toLowerCase().replaceAll(' ', '_')),
        displayName: Value(displayName),
        rate: Value(rate),
        isDefault: const Value(false),
      ));

      await loadMaterials(); // Reload to include the new material
      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error adding material: $e');
      notifyListeners();
      return false;
    }
  }

  /// Remove a material (both custom and default)
  Future<bool> removeMaterial(String key) async {
    try {
      final material = await _materialsDao.getMaterialByName(key);
      if (material != null) {
        final deleteCount = await _materialsDao.deleteMaterial(material.id);
        if (deleteCount > 0) {
          await loadMaterials(); // Reload to remove the material
          return true;
        }
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error removing material: $e');
      notifyListeners();
      return false;
    }
  }

  /// Update material rate
  Future<bool> updateMaterialRate(String key, double rate) async {
    try {
      final material = await _materialsDao.getMaterialByName(key);
      if (material != null) {
        final success = await _materialsDao.updateMaterial(
          material.id,
          MaterialsCompanion(
            rate: Value(rate),
            updatedAt: Value(DateTime.now()),
          ),
        );
        if (success) {
          _materialRates[key] = rate;
          notifyListeners();
          return true;
        }
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error updating material rate: $e');
      notifyListeners();
      return false;
    }
  }

  /// Get material rate by key
  Future<double?> getMaterialRate(String key) async {
    final material = await _materialsDao.getMaterialByName(key);
    return material?.rate;
  }

  /// Get material label by key
  Future<String> getMaterialLabel(String key) async {
    final material = await _materialsDao.getMaterialByName(key);
    return material?.displayName ?? key;
  }

  /// Check if a material is custom (non-default)
  bool isCustomMaterial(String key) {
    // In the new system, we can check if it's not a default material
    // For now, we'll consider all materials as potentially custom
    return !['acier', 'inox', 'cuivre', 'aluminum', 'tole_galvanisee'].contains(key);
  }

  /// Get list of material keys for dropdown
  List<String> get materialKeys => _materialRates.keys.toList();

  /// Get list of material entries for dropdown
  List<MapEntry<String, String>> get materialEntries => _materialLabels.entries.toList();
}
