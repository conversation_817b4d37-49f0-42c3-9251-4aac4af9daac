import 'package:flutter/foundation.dart';
import '../models/client.dart';
import '../repositories/client_repository.dart';

class ClientProvider with ChangeNotifier {
  final ClientRepository _clientRepository = ClientRepository();
  List<Client> _clients = [];
  String _searchTerm = '';
  bool _isLoading = false;

  List<Client> get clients => _clients;
  String get searchTerm => _searchTerm;
  bool get isLoading => _isLoading;

  List<Client> get filteredClients {
    if (_searchTerm.isEmpty) {
      return _clients;
    }
    return _clients.where((client) {
      final query = _searchTerm.toLowerCase();
      return client.name.toLowerCase().contains(query) ||
          client.reference.toLowerCase().contains(query) ||
          (client.company?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  Future<void> loadClients() async {
    _isLoading = true;
    notifyListeners();

    try {
      _clients = await _clientRepository.getAllClients();
    } catch (e) {
      debugPrint('Error loading clients: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void updateSearchTerm(String term) {
    _searchTerm = term;
    notifyListeners();
  }

  Future<void> addClient(Client client) async {
    try {
      await _clientRepository.addClient(client);
      await loadClients(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error adding client: $e');
      rethrow;
    }
  }

  Future<void> removeClient(String id) async {
    try {
      await _clientRepository.deleteClient(id);
      await loadClients(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error removing client: $e');
      rethrow;
    }
  }

  Future<void> updateClient(Client updatedClient) async {
    try {
      await _clientRepository.updateClient(updatedClient);
      await loadClients(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error updating client: $e');
      rethrow;
    }
  }

  Client? getClientById(String id) {
    try {
      return _clients.firstWhere((client) => client.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<bool> isReferenceUnique(String reference, {String? excludeId}) async {
    return await _clientRepository.isReferenceUnique(reference, excludeId: excludeId);
  }

  Future<void> incrementClientProjects(String clientId) async {
    try {
      await _clientRepository.incrementClientProjects(clientId);
      await loadClients(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error incrementing client projects: $e');
    }
  }

  Future<void> updateClientTotalSpent(String clientId, double amount) async {
    try {
      await _clientRepository.updateClientTotalSpent(clientId, amount);
      await loadClients(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error updating client total spent: $e');
    }
  }

  // Calculate total expenses across all clients
  double get totalExpenses {
    return _clients.fold(0.0, (sum, client) => sum + client.totalSpent);
  }

  // Calculate average expense per client
  double get averageExpensePerClient {
    if (_clients.isEmpty) return 0.0;
    return totalExpenses / _clients.length;
  }

  // Get the client with highest spending
  Client? get topSpendingClient {
    if (_clients.isEmpty) return null;
    return _clients.reduce((a, b) => a.totalSpent > b.totalSpent ? a : b);
  }

  // Get total number of projects across all clients
  int get totalProjects {
    return _clients.fold(0, (sum, client) => sum + client.projects);
  }

  // Get clients statistics
  Map<String, dynamic> get clientStatistics {
    return {
      'totalClients': _clients.length,
      'totalExpenses': totalExpenses,
      'averageExpensePerClient': averageExpensePerClient,
      'totalProjects': totalProjects,
      'topSpendingClient': topSpendingClient,
    };
  }
}
