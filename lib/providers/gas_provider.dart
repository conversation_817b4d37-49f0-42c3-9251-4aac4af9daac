import 'package:flutter/foundation.dart';
import 'package:drift/drift.dart';
import '../database/database.dart';
import '../database/daos.dart';
import '../services/database_service.dart';

class GasProvider with ChangeNotifier {
  late final GasTypesDao _gasTypesDao;
  late final GasCostsDao _gasCostsDao;
  late final ThicknessRangesDao _thicknessRangesDao;
  
  Map<String, Map<String, double>> _gasTypes = {};
  Map<String, String> _gasLabels = {};
  Map<String, String> _thicknessRangeLabels = {};
  bool _isLoading = true;
  String? _error;

  GasProvider() {
    final database = DatabaseService.instance.database;
    _gasTypesDao = GasTypesDao(database);
    _gasCostsDao = GasCostsDao(database);
    _thicknessRangesDao = ThicknessRangesDao(database);
  }

  Map<String, Map<String, double>> get gasTypes => _gasTypes;
  Map<String, String> get gasLabels => _gasLabels;
  Map<String, String> get thicknessRangeLabels => _thicknessRangeLabels;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize and load all gas types
  Future<void> initialize() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await loadGasTypes();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error initializing gas types: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load all gas types (default + custom)
  Future<void> loadGasTypes() async {
    try {
      _gasTypes.clear();
      _gasLabels.clear();
      _thicknessRangeLabels.clear();

      // Get all gas types
      final gasTypes = await _gasTypesDao.getAllGasTypes();

      // Get all gas costs with details
      final gasCostsWithDetails = await _gasCostsDao.getAllGasCostsWithDetails();

      // Get all thickness ranges to ensure completeness
      final thicknessRanges = await _thicknessRangesDao.getAllThicknessRanges();

      // Build thickness range labels map
      for (final range in thicknessRanges) {
        _thicknessRangeLabels[range.name] = range.displayName;
      }

      // Build gas types map
      for (final gasType in gasTypes) {
        _gasLabels[gasType.name] = gasType.displayName;
        _gasTypes[gasType.name] = {};

        // Find costs for this gas type
        final costsForGasType = gasCostsWithDetails
            .where((gc) => gc.gasCost.gasTypeId == gasType.id)
            .toList();

        // Ensure all thickness ranges are present
        for (final range in thicknessRanges) {
          final existingCost = costsForGasType
              .where((gc) => gc.thicknessRange?.id == range.id)
              .firstOrNull;

          if (existingCost != null) {
            _gasTypes[gasType.name]![range.name] = existingCost.gasCost.costPerHour;
          } else {
            // Create missing gas cost with default value 0.0
            await _gasCostsDao.upsertGasCost(gasType.id, range.id, 0.0);
            _gasTypes[gasType.name]![range.name] = 0.0;
          }
        }
      }

      _error = null;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      debugPrint('Error loading gas types: $e');
      rethrow;
    }
  }

  /// Add a new custom gas type
  Future<bool> addGasType(String gasName) async {
    try {
      // Create display name from name (capitalize first letter, replace underscores)
      final displayName = gasName.split('_').map((word) =>
        word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
      ).join(' ');

      // Insert the new gas type
      await _gasTypesDao.insertGasType(GasTypesCompanion(
        name: Value(gasName.toLowerCase().replaceAll(' ', '_')),
        displayName: Value(displayName),
        isDefault: const Value(false),
      ));

      // Get the newly created gas type to get its ID
      final newGasType = await _gasTypesDao.getGasTypeByName(gasName.toLowerCase().replaceAll(' ', '_'));

      if (newGasType != null) {
        // Get all thickness ranges
        final thicknessRanges = await _thicknessRangesDao.getAllThicknessRanges();

        // Create default gas costs (0.0) for all thickness ranges
        for (final range in thicknessRanges) {
          await _gasCostsDao.upsertGasCost(newGasType.id, range.id, 0.0);
        }
      }

      await loadGasTypes(); // Reload to include the new gas type with costs
      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error adding gas type: $e');
      notifyListeners();
      return false;
    }
  }

  /// Remove a gas type (both custom and default)
  Future<bool> removeGasType(String gasName) async {
    try {
      final gasType = await _gasTypesDao.getGasTypeByName(gasName);
      if (gasType != null) {
        final deleteCount = await _gasTypesDao.deleteGasType(gasType.id);
        if (deleteCount > 0) {
          await loadGasTypes(); // Reload to remove the gas type
          return true;
        }
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error removing gas type: $e');
      notifyListeners();
      return false;
    }
  }

  /// Update gas cost for specific thickness range
  Future<bool> updateGasCost(String gasName, String thicknessRange, double cost) async {
    try {
      final gasType = await _gasTypesDao.getGasTypeByName(gasName);
      final thicknessRangeData = await _thicknessRangesDao.getThicknessRangeByName(thicknessRange);

      if (gasType != null && thicknessRangeData != null) {
        await _gasCostsDao.upsertGasCost(gasType.id, thicknessRangeData.id, cost);

        // Update local state immediately for better UX
        if (_gasTypes.containsKey(gasName)) {
          _gasTypes[gasName]![thicknessRange] = cost;
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error updating gas cost: $e');
      notifyListeners();
      return false;
    }
  }

  /// Get gas cost for specific gas type and thickness range
  Future<double?> getGasCost(String gasName, String thicknessRange) async {
    // For now, return from local cache if available
    return _gasTypes[gasName]?[thicknessRange];
  }

  /// Get gas cost for specific gas type and thickness value
  Future<double?> getGasCostForThickness(String gasName, double thickness) async {
    return await _gasCostsDao.getGasCostForThickness(gasName, thickness);
  }

  /// Check if a gas type is custom (non-default)
  bool isCustomGasType(String gasName) {
    // In the new system, we can check if it's not a default gas type
    return !['oxygene_o2', 'azote_n2', 'air_comprime'].contains(gasName);
  }

  /// Get list of gas type names for dropdown
  List<String> get gasTypeNames => _gasTypes.keys.toList();

  /// Get list of gas type entries for dropdown
  List<MapEntry<String, String>> get gasTypeEntries => _gasLabels.entries.toList();

  /// Get thickness ranges for a specific gas type
  List<String> getThicknessRanges(String gasName) {
    return _gasTypes[gasName]?.keys.toList() ?? [];
  }

  /// Get cost for specific gas and thickness
  double? getCostForGasAndThickness(String gasName, String thicknessRange) {
    return _gasTypes[gasName]?[thicknessRange];
  }

  /// Get display name for thickness range
  String getThicknessRangeDisplayName(String thicknessRangeName) {
    return _thicknessRangeLabels[thicknessRangeName] ?? thicknessRangeName;
  }
}
