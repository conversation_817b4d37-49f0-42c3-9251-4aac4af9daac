import 'package:flutter/foundation.dart';
import '../models/calculation.dart';
import '../repositories/calculation_history_repository.dart';

class HistoryProvider with ChangeNotifier {
  final CalculationHistoryRepository _calculationRepository = CalculationHistoryRepository();
  List<Calculation> _calculations = [];
  String _searchTerm = '';
  CalculationStatus? _statusFilter;
  String _sortBy = 'date-desc';
  bool _isLoading = false;

  List<Calculation> get calculations => _calculations;
  String get searchTerm => _searchTerm;
  CalculationStatus? get statusFilter => _statusFilter;
  String get sortBy => _sortBy;
  bool get isLoading => _isLoading;

  Future<void> loadCalculations() async {
    _isLoading = true;
    notifyListeners();

    try {
      _calculations = await _calculationRepository.getAllCalculations();
    } catch (e) {
      debugPrint('Error loading calculations: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  List<Calculation> get filteredCalculations {
    List<Calculation> filtered = _calculations;

    // Apply search filter
    if (_searchTerm.isNotEmpty) {
      final query = _searchTerm.toLowerCase();
      filtered = filtered.where((calc) {
        return calc.clientName.toLowerCase().contains(query) ||
            calc.clientReference.toLowerCase().contains(query) ||
            calc.id.toLowerCase().contains(query);
      }).toList();
    }

    // Apply status filter
    if (_statusFilter != null) {
      filtered = filtered.where((calc) => calc.status == _statusFilter).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'date-desc':
          return b.createdAt.compareTo(a.createdAt);
        case 'date-asc':
          return a.createdAt.compareTo(b.createdAt);
        case 'price-desc':
          return b.totalPrice.compareTo(a.totalPrice);
        case 'price-asc':
          return a.totalPrice.compareTo(b.totalPrice);
        case 'client':
          return a.clientName.compareTo(b.clientName);
        default:
          return 0;
      }
    });

    return filtered;
  }

  double get totalValue {
    return filteredCalculations.fold(0.0, (sum, calc) => sum + calc.totalPrice);
  }

  int get completedCount {
    return filteredCalculations
        .where((calc) => calc.status == CalculationStatus.completed)
        .length;
  }

  void updateSearchTerm(String term) {
    _searchTerm = term;
    notifyListeners();
  }

  void updateStatusFilter(CalculationStatus? status) {
    _statusFilter = status;
    notifyListeners();
  }

  void updateSortBy(String sortBy) {
    _sortBy = sortBy;
    notifyListeners();
  }

  Future<void> addCalculation(Calculation calculation) async {
    try {
      await _calculationRepository.addCalculation(calculation);
      await loadCalculations(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error adding calculation: $e');
      rethrow;
    }
  }

  Future<void> removeCalculation(String id) async {
    try {
      await _calculationRepository.deleteCalculation(id);
      await loadCalculations(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error removing calculation: $e');
      rethrow;
    }
  }

  Future<void> updateCalculation(Calculation updatedCalculation) async {
    try {
      await _calculationRepository.updateCalculation(updatedCalculation);
      await loadCalculations(); // Reload to get updated data
    } catch (e) {
      debugPrint('Error updating calculation: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final calculations = await _calculationRepository.getAllCalculations();
      final completedCalculations = calculations.where((c) => c.status == CalculationStatus.completed).toList();
      final totalRevenue = completedCalculations.fold<double>(0.0, (sum, calc) => sum + calc.totalPrice);

      return {
        'totalCalculations': calculations.length,
        'completedCalculations': completedCalculations.length,
        'pendingCalculations': calculations.where((c) => c.status == CalculationStatus.pending).length,
        'cancelledCalculations': calculations.where((c) => c.status == CalculationStatus.cancelled).length,
        'totalRevenue': totalRevenue,
        'averageOrderValue': completedCalculations.isNotEmpty ? totalRevenue / completedCalculations.length : 0.0,
      };
    } catch (e) {
      debugPrint('Error getting statistics: $e');
      return {};
    }
  }
}
