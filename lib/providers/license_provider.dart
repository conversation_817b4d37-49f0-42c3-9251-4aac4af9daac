import 'package:flutter/foundation.dart';
import '../services/license_validation_service.dart';
import '../database/database.dart';

/// Provider for managing license validation state throughout the app
class LicenseProvider with ChangeNotifier {
  late final LicenseValidationService _licenseService;

  LicenseProvider(AppDatabase database) {
    _licenseService = LicenseValidationService.getInstance(database);
  }

  bool _isLicenseValid = false;
  bool _isValidating = false;
  LicenseValidationResult? _lastValidationResult;
  StoredLicenseCredentials? _storedCredentials;

  /// Current license validation status
  bool get isLicenseValid => _isLicenseValid;

  /// Whether license validation is in progress
  bool get isValidating => _isValidating;

  /// Last validation result details
  LicenseValidationResult? get lastValidationResult => _lastValidationResult;

  /// Stored license credentials (if any)
  StoredLicenseCredentials? get storedCredentials => _storedCredentials;

  /// Whether app should be locked due to invalid license
  bool get shouldLockApp => !_isLicenseValid && _lastValidationResult != null;

  /// Initialize license validation on app startup
  Future<void> initialize() async {
    _storedCredentials = await _licenseService.getStoredCredentials();
    
    // Check cached license first
    _isLicenseValid = await _licenseService.isLicenseValid();
    
    // If we have stored credentials but no valid cache, validate in background
    if (!_isLicenseValid && _storedCredentials != null) {
      _validateInBackground();
    }
    
    notifyListeners();
  }

  /// Validate license with provided credentials
  Future<bool> validateLicense({
    required String serialNumber,
    String? customServerUrl,
  }) async {
    if (_isValidating) return false;

    _isValidating = true;
    notifyListeners();

    try {
      _lastValidationResult = await _licenseService.validateLicense(
        serialNumber: serialNumber,
        customServerUrl: customServerUrl,
      );

      _isLicenseValid = _lastValidationResult!.isValid;
      
      if (_isLicenseValid) {
        _storedCredentials = StoredLicenseCredentials(
          serialNumber: serialNumber,
        );
      }

      return _isLicenseValid;
    } catch (e) {
      _lastValidationResult = LicenseValidationResult(
        isValid: false,
        responseCode: 'VALIDATION_ERROR',
        message: 'Error during validation: $e',
        timestamp: DateTime.now(),
      );
      _isLicenseValid = false;
      return false;
    } finally {
      _isValidating = false;
      notifyListeners();
    }
  }

  /// Validate using stored credentials
  Future<bool> validateWithStoredCredentials() async {
    if (_isValidating || _storedCredentials == null) return false;

    return validateLicense(
      serialNumber: _storedCredentials!.serialNumber,
    );
  }

  /// Clear all license data and force re-validation
  Future<void> clearLicenseData() async {
    await _licenseService.clearValidationCache();
    _isLicenseValid = false;
    _lastValidationResult = null;
    _storedCredentials = null;
    notifyListeners();
  }

  /// Force license re-validation
  Future<bool> refreshLicense() async {
    await _licenseService.clearValidationCache();
    return validateWithStoredCredentials();
  }

  /// Get user-friendly status message
  String getStatusMessage() {
    if (_isValidating) {
      return 'Validation en cours...';
    }
    
    if (_isLicenseValid) {
      return 'Licence valide';
    }
    
    if (_lastValidationResult != null) {
      return _lastValidationResult!.message;
    }
    
    return 'Aucune licence configurée';
  }

  /// Get status color based on current state
  LicenseStatusColor getStatusColor() {
    if (_isValidating) {
      return LicenseStatusColor.warning;
    }
    
    if (_isLicenseValid) {
      return LicenseStatusColor.success;
    }
    
    return LicenseStatusColor.error;
  }

  /// Background validation without UI updates
  Future<void> _validateInBackground() async {
    try {
      final result = await _licenseService.validateWithStoredCredentials();
      _isLicenseValid = result.isValid;
      _lastValidationResult = result;
      notifyListeners();
    } catch (e) {
      debugPrint('Background license validation failed: $e');
    }
  }

  /// Check if license needs renewal (based on cached validation age)
  bool shouldPromptForRenewal() {
    if (_lastValidationResult == null) return false;

    // Check if license is expired or expiring soon
    if (_lastValidationResult!.isExpired || _lastValidationResult!.isExpiringSoon) {
      return true;
    }

    final hoursSinceValidation = DateTime.now()
        .difference(_lastValidationResult!.timestamp)
        .inHours;

    // Prompt for renewal if validation is older than 7 days
    return hoursSinceValidation > 168;
  }

  /// Get license expiry date if available
  DateTime? get licenseExpiryDate => _lastValidationResult?.expiryDate;

  /// Check if license is expired
  bool get isLicenseExpired => _lastValidationResult?.isExpired ?? false;

  /// Check if license is expiring soon (within 30 days)
  bool get isLicenseExpiringSoon => _lastValidationResult?.isExpiringSoon ?? false;

  /// Get days until license expiry
  int? get daysUntilExpiry => _lastValidationResult?.daysUntilExpiry;
}

/// License status color enum for UI
enum LicenseStatusColor {
  success,
  warning,
  error,
}
