import 'package:flutter/foundation.dart';
import '../database/daos.dart';
import '../services/database_service.dart';

class ConfigurationProvider with ChangeNotifier {
  late final AppSettingsDao _appSettingsDao;

  ConfigurationProvider() {
    _appSettingsDao = AppSettingsDao(DatabaseService.instance.database);
  }



  // Default values getters (returns null if not set)
  Future<String?> getDefaultGasType() async {
    return await _appSettingsDao.getSettingValue('defaults', 'gas_type');
  }

  Future<String?> getDefaultMaterial() async {
    return await _appSettingsDao.getSettingValue('defaults', 'material');
  }

  Future<double> getDefaultThickness() async {
    final value = await _appSettingsDao.getSettingValueAsDouble('defaults', 'thickness');
    return value ?? 0.0;
  }

  Future<double> getDefaultCuttingSpeed() async {
    final value = await _appSettingsDao.getSettingValueAsDouble('defaults', 'cutting_speed');
    return value ?? 0.0;
  }

  Future<double> getDefaultLinearMeters() async {
    final value = await _appSettingsDao.getSettingValueAsDouble('defaults', 'linear_meters');
    return value ?? 0.0;
  }

  Future<double> getDesignServicePrice() async {
    return await _appSettingsDao.getDesignServicePrice() ?? 0.0;
  }

  Future<double> getPricePerMeter() async {
    return await _appSettingsDao.getPricePerMeter() ?? 0.0;
  }

  Future<double> getMinimumOrderValue() async {
    return await _appSettingsDao.getMinOrderAmount() ?? 0.0;
  }

  Future<double> getTaxRate() async {
    return await _appSettingsDao.getTaxRate() ?? 0.0;
  }

  // Company information getters (returns null if not set)
  Future<String?> getCompanyName() async {
    return await _appSettingsDao.getSettingValue('company', 'name');
  }

  Future<String?> getCompanyAddress() async {
    return await _appSettingsDao.getSettingValue('company', 'address');
  }

  Future<String?> getCompanyPhone() async {
    return await _appSettingsDao.getSettingValue('company', 'phone');
  }

  Future<String?> getCompanyEmail() async {
    return await _appSettingsDao.getSettingValue('company', 'email');
  }

  // Setters
  Future<void> setDefaultGasType(String value) async {
    try {
      await _appSettingsDao.upsertSetting('defaults', 'gas_type', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default gas type: $e');
      rethrow;
    }
  }

  Future<void> setDefaultMaterial(String value) async {
    try {
      await _appSettingsDao.upsertSetting('defaults', 'material', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default material: $e');
      rethrow;
    }
  }

  Future<void> setDefaultThickness(double value) async {
    try {
      await _appSettingsDao.upsertSetting('defaults', 'thickness', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default thickness: $e');
      rethrow;
    }
  }

  Future<void> setDefaultCuttingSpeed(double value) async {
    try {
      await _appSettingsDao.upsertSetting('defaults', 'cutting_speed', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default cutting speed: $e');
      rethrow;
    }
  }

  Future<void> setDefaultLinearMeters(double value) async {
    try {
      await _appSettingsDao.upsertSetting('defaults', 'linear_meters', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default linear meters: $e');
      rethrow;
    }
  }

  Future<void> setDesignServicePrice(double value) async {
    try {
      await _appSettingsDao.upsertSetting('pricing', 'design_service_price', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting design service price: $e');
      rethrow;
    }
  }

  Future<void> setPricePerMeter(double value) async {
    try {
      await _appSettingsDao.upsertSetting('pricing', 'price_per_meter', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting price per meter: $e');
      rethrow;
    }
  }

  Future<void> setMinimumOrderValue(double value) async {
    try {
      await _appSettingsDao.upsertSetting('pricing', 'min_order_amount', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting minimum order value: $e');
      rethrow;
    }
  }

  Future<void> setTaxRate(double value) async {
    try {
      await _appSettingsDao.upsertSetting('pricing', 'tax_rate', value.toString(), 'double');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting tax rate: $e');
      rethrow;
    }
  }

  Future<void> setCompanyName(String value) async {
    try {
      await _appSettingsDao.upsertSetting('company', 'name', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company name: $e');
      rethrow;
    }
  }

  Future<void> setCompanyAddress(String value) async {
    try {
      await _appSettingsDao.upsertSetting('company', 'address', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company address: $e');
      rethrow;
    }
  }

  Future<void> setCompanyPhone(String value) async {
    try {
      await _appSettingsDao.upsertSetting('company', 'phone', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company phone: $e');
      rethrow;
    }
  }

  Future<void> setCompanyEmail(String value) async {
    try {
      await _appSettingsDao.upsertSetting('company', 'email', value, 'string');
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting company email: $e');
      rethrow;
    }
  }

  // Generic configuration value setter (for backward compatibility)
  Future<void> setConfigValue(String key, String value) async {
    try {
      // Map keys to proper categories and types
      final categoryMapping = _getKeyMapping(key);
      await _appSettingsDao.upsertSetting(
        categoryMapping['category']!,
        key,
        value,
        categoryMapping['type']!,
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting config value: $e');
      rethrow;
    }
  }

  // Helper method to map configuration keys to proper categories and types
  Map<String, String> _getKeyMapping(String key) {
    const keyMappings = {
      'default_setup_fees': {'category': 'pricing', 'type': 'double'},
      'design_service_price': {'category': 'pricing', 'type': 'double'},
      'default_thickness': {'category': 'defaults', 'type': 'double'},
      'default_cutting_speed': {'category': 'defaults', 'type': 'double'},
      'price_per_meter': {'category': 'pricing', 'type': 'double'},
      'tax_rate': {'category': 'pricing', 'type': 'double'},
      'min_order_amount': {'category': 'pricing', 'type': 'double'},
      'app_version': {'category': 'app', 'type': 'string'},
      'currency': {'category': 'app', 'type': 'string'},
    };

    return keyMappings[key] ?? {'category': 'misc', 'type': 'string'};
  }

  // Generic configuration value deleter (for backward compatibility)
  Future<void> deleteConfigValue(String key) async {
    try {
      // Get the proper category for this key
      final categoryMapping = _getKeyMapping(key);

      // Try to find and delete from proper category first
      var setting = await _appSettingsDao.getSettingByCategoryAndKey(categoryMapping['category']!, key);
      if (setting != null) {
        await _appSettingsDao.deleteSetting(setting.id);
        notifyListeners();
        return;
      }

      // If not found in proper category, try legacy category
      setting = await _appSettingsDao.getSettingByCategoryAndKey('legacy', key);
      if (setting != null) {
        await _appSettingsDao.deleteSetting(setting.id);
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting config value: $e');
      rethrow;
    }
  }

  // Generic configuration value getter (for backward compatibility)
  Future<String?> getConfigValue(String key) async {
    try {
      // Get the proper category for this key
      final categoryMapping = _getKeyMapping(key);
      final value = await _appSettingsDao.getSettingValue(categoryMapping['category']!, key);

      // If not found in proper category, try legacy category for backward compatibility
      if (value == null) {
        return await _appSettingsDao.getSettingValue('legacy', key);
      }

      return value;
    } catch (e) {
      debugPrint('Error getting config value: $e');
      return null;
    }
  }

  // Get configuration values by prefix (for backward compatibility)
  Future<Map<String, String>> getConfigValuesByPrefix(String prefix) async {
    try {
      final allSettings = await _appSettingsDao.getAllSettings();
      final Map<String, String> result = {};

      for (final setting in allSettings) {
        if (setting.key.startsWith(prefix)) {
          result[setting.key] = setting.value;
        }
      }

      return result;
    } catch (e) {
      debugPrint('Error getting config values by prefix: $e');
      return {};
    }
  }
}