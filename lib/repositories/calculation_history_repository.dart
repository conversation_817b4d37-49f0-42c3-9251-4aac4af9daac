import '../models/calculation.dart';
import '../services/database_service.dart';

class CalculationHistoryRepository {
  final DatabaseService _databaseService = DatabaseService.instance;

  Future<List<Calculation>> getAllCalculations() async {
    return await _databaseService.getAllCalculations();
  }

  Future<Calculation?> getCalculationById(String id) async {
    return await _databaseService.getCalculationById(id);
  }

  Future<void> addCalculation(Calculation calculation) async {
    await _databaseService.insertCalculation(calculation);
  }

  Future<void> updateCalculation(Calculation calculation) async {
    await _databaseService.updateCalculation(calculation);
  }

  Future<void> deleteCalculation(String id) async {
    await _databaseService.deleteCalculation(id);
  }

  Future<List<Calculation>> searchCalculations(String query) async {
    return await _databaseService.searchCalculations(query);
  }

  Future<List<Calculation>> getCalculationsByStatus(CalculationStatus status) async {
    return await _databaseService.getCalculationsByStatus(status);
  }

  Future<List<Calculation>> getCalculationsByClient(String clientName) async {
    final allCalculations = await getAllCalculations();
    return allCalculations.where((calc) => calc.clientName == clientName).toList();
  }

  Future<List<Calculation>> getCalculationsByDateRange(DateTime start, DateTime end) async {
    final allCalculations = await getAllCalculations();
    return allCalculations.where((calc) =>
        calc.createdAt.isAfter(start) && calc.createdAt.isBefore(end)).toList();
  }

  Future<double> getTotalRevenue() async {
    final completedCalculations = await getCalculationsByStatus(CalculationStatus.completed);
    return completedCalculations.fold<double>(0.0, (sum, calc) => sum + calc.totalPrice);
  }

  Future<double> getRevenueByDateRange(DateTime start, DateTime end) async {
    final calculations = await getCalculationsByDateRange(start, end);
    final completedCalculations = calculations.where((calc) => calc.status == CalculationStatus.completed);
    return completedCalculations.fold<double>(0.0, (sum, calc) => sum + calc.totalPrice);
  }

  Future<Map<String, int>> getCalculationCountByStatus() async {
    final allCalculations = await getAllCalculations();
    final Map<String, int> counts = {};

    for (final status in CalculationStatus.values) {
      counts[status.toString().split('.').last] =
          allCalculations.where((calc) => calc.status == status).length;
    }

    return counts;
  }

  Future<List<Calculation>> getRecentCalculations({int limit = 10}) async {
    final allCalculations = await getAllCalculations();
    allCalculations.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return allCalculations.take(limit).toList();
  }
}