import '../models/client.dart';
import '../services/database_service.dart';

class ClientRepository {
  final DatabaseService _databaseService = DatabaseService.instance;

  Future<List<Client>> getAllClients() async {
    return await _databaseService.getAllClients();
  }

  Future<Client?> getClientById(String id) async {
    return await _databaseService.getClientById(id);
  }

  Future<void> addClient(Client client) async {
    await _databaseService.insertClient(client);
  }

  Future<void> updateClient(Client client) async {
    await _databaseService.updateClient(client);
  }

  Future<void> deleteClient(String id) async {
    await _databaseService.deleteClient(id);
  }

  Future<List<Client>> searchClients(String query) async {
    return await _databaseService.searchClients(query);
  }

  Future<Client?> getClientByReference(String reference) async {
    final clients = await getAllClients();
    try {
      return clients.firstWhere((client) => client.reference == reference);
    } catch (e) {
      return null;
    }
  }

  Future<bool> isReferenceUnique(String reference, {String? excludeId}) async {
    final clients = await getAllClients();
    return !clients.any((client) =>
        client.reference == reference && client.id != excludeId);
  }

  Future<void> incrementClientProjects(String clientId) async {
    final client = await getClientById(clientId);
    if (client != null) {
      final updatedClient = client.copyWith(projects: client.projects + 1);
      await updateClient(updatedClient);
    }
  }

  Future<void> updateClientTotalSpent(String clientId, double amount) async {
    final client = await getClientById(clientId);
    if (client != null) {
      final updatedClient = client.copyWith(totalSpent: client.totalSpent + amount);
      await updateClient(updatedClient);
    }
  }
}