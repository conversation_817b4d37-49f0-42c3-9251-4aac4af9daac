import '../services/database_service.dart';

class ConfigurationRepository {
  final DatabaseService _databaseService = DatabaseService.instance;

  // App configuration keys
  static const String keyDefaultGasType = 'default_gas_type';
  static const String keyDefaultMaterial = 'default_material';
  static const String keyDefaultThickness = 'default_thickness';
  static const String keyDefaultCuttingSpeed = 'default_cutting_speed';
  static const String keyDefaultLinearMeters = 'default_linear_meters';
  static const String keyDesignServicePrice = 'design_service_price';
  static const String keyPricePerMeter = 'price_per_meter';
  static const String keyMinimumOrderValue = 'minimum_order_value';
  static const String keyTaxRate = 'tax_rate';
  static const String keyCompanyName = 'company_name';
  static const String keyCompanyAddress = 'company_address';
  static const String keyCompanyPhone = 'company_phone';
  static const String keyCompanyEmail = 'company_email';

  Future<String?> getConfigValue(String key) async {
    return await _databaseService.getConfigValue(key);
  }

  Future<void> setConfigValue(String key, String value) async {
    await _databaseService.setConfigValue(key, value);
  }

  Future<void> deleteConfigValue(String key) async {
    await _databaseService.deleteConfigValue(key);
  }

  Future<Map<String, String>> getConfigValuesByPrefix(String prefix) async {
    return await _databaseService.getConfigValuesByPrefix(prefix);
  }

  // Typed getters for common configuration values (no defaults - returns null/0 if not set)
  Future<String?> getDefaultGasType() async {
    return await getConfigValue(keyDefaultGasType);
  }

  Future<String?> getDefaultMaterial() async {
    return await getConfigValue(keyDefaultMaterial);
  }

  Future<double> getDefaultThickness() async {
    final value = await getConfigValue(keyDefaultThickness);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getDefaultCuttingSpeed() async {
    final value = await getConfigValue(keyDefaultCuttingSpeed);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getDefaultLinearMeters() async {
    final value = await getConfigValue(keyDefaultLinearMeters);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getDesignServicePrice() async {
    final value = await getConfigValue(keyDesignServicePrice);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getPricePerMeter() async {
    final value = await getConfigValue(keyPricePerMeter);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getMinimumOrderValue() async {
    final value = await getConfigValue(keyMinimumOrderValue);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<double> getTaxRate() async {
    final value = await getConfigValue(keyTaxRate);
    return value != null ? double.tryParse(value) ?? 0.0 : 0.0;
  }

  Future<String?> getCompanyName() async {
    return await getConfigValue(keyCompanyName);
  }

  Future<String?> getCompanyAddress() async {
    return await getConfigValue(keyCompanyAddress);
  }

  Future<String?> getCompanyPhone() async {
    return await getConfigValue(keyCompanyPhone);
  }

  Future<String?> getCompanyEmail() async {
    return await getConfigValue(keyCompanyEmail);
  }

  // Typed setters for common configuration values
  Future<void> setDefaultGasType(String value) async {
    await setConfigValue(keyDefaultGasType, value);
  }

  Future<void> setDefaultMaterial(String value) async {
    await setConfigValue(keyDefaultMaterial, value);
  }

  Future<void> setDefaultThickness(double value) async {
    await setConfigValue(keyDefaultThickness, value.toString());
  }

  Future<void> setDefaultCuttingSpeed(double value) async {
    await setConfigValue(keyDefaultCuttingSpeed, value.toString());
  }

  Future<void> setDefaultLinearMeters(double value) async {
    await setConfigValue(keyDefaultLinearMeters, value.toString());
  }

  Future<void> setDesignServicePrice(double value) async {
    await setConfigValue(keyDesignServicePrice, value.toString());
  }

  Future<void> setPricePerMeter(double value) async {
    await setConfigValue(keyPricePerMeter, value.toString());
  }

  Future<void> setMinimumOrderValue(double value) async {
    await setConfigValue(keyMinimumOrderValue, value.toString());
  }

  Future<void> setTaxRate(double value) async {
    await setConfigValue(keyTaxRate, value.toString());
  }

  Future<void> setCompanyName(String value) async {
    await setConfigValue(keyCompanyName, value);
  }

  Future<void> setCompanyAddress(String value) async {
    await setConfigValue(keyCompanyAddress, value);
  }

  Future<void> setCompanyPhone(String value) async {
    await setConfigValue(keyCompanyPhone, value);
  }

  Future<void> setCompanyEmail(String value) async {
    await setConfigValue(keyCompanyEmail, value);
  }


}