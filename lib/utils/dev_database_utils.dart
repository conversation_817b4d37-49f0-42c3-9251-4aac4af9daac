import 'package:flutter/foundation.dart';
import '../database/database_seeder.dart';
import '../services/database_service.dart';

/// Development utilities for manual database operations
class DevDatabaseUtils {
  static final DatabaseService _dbService = DatabaseService.instance;

  /// Reset and seed database with fresh data
  static Future<void> resetDatabase() async {
    if (kDebugMode) {
      print('Resetting database...');
      await DatabaseSeeder.reset();
      await _printStats();
    }
  }

  /// Force re-seed database
  static Future<void> forceSeed() async {
    if (kDebugMode) {
      print('Force seeding database...');
      await DatabaseSeeder.run(force: true);
      await _printStats();
    }
  }

  /// Clear all data without seeding
  static Future<void> clearAllData() async {
    if (kDebugMode) {
      print('Clearing all data...');
      await _dbService.clearAllData();
      await _printStats();
    }
  }

  /// Print current database statistics
  static Future<void> printStats() async {
    if (kDebugMode) {
      await _printStats();
    }
  }

  /// Check database status
  static Future<void> checkStatus() async {
    if (kDebugMode) {
      print('Database Status:');
      
      final seedInfo = await DatabaseSeeder.getSeedInfo();
      print('Seeded: ${seedInfo['seeded']}');
      print('Seed Date: ${seedInfo['seed_date']}');
      print('App Version: ${seedInfo['app_version']}');
      
      final needsSeeding = await DatabaseSeeder.needsSeeding();
      print('Needs Seeding: $needsSeeding');
      
      await _printStats();
    }
  }

  /// Internal helper to print statistics
  static Future<void> _printStats() async {
    try {
      final stats = await _dbService.getStatistics();
      print('Statistics:');
      print('- Total clients: ${stats['totalClients']}');
      print('- Total calculations: ${stats['totalCalculations']}');
      print('- Completed calculations: ${stats['completedCalculations']}');
      print('- Pending calculations: ${stats['pendingCalculations']}');
      print('- Cancelled calculations: ${stats['cancelledCalculations']}');
      print('- Total revenue: €${stats['totalRevenue']?.toStringAsFixed(2)}');
      print('- Average order value: €${stats['averageOrderValue']?.toStringAsFixed(2)}');
    } catch (e) {
      print('Error getting statistics: $e');
    }
  }
}

/// Extension methods for easy access during development
extension DatabaseDevExtensions on DatabaseService {
  /// Quick access to dev utils from database service
  static DevDatabaseUtils get dev => DevDatabaseUtils();
}
