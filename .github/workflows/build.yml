name: Build Multi-Platform Release
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
      
      - name: Enable Windows desktop
        run: flutter config --enable-windows-desktop
      
      - name: Get dependencies
        run: flutter pub get
      
      - name: Clean build
        run: flutter clean
      
      - name: Generate code (database, etc.)
        run: dart run build_runner build --delete-conflicting-outputs
      
      - name: Download SQLite3 DLL
        run: |
          $url = "https://www.sqlite.org/2024/sqlite-dll-win-x64-3460000.zip"
          $output = "sqlite3.zip"
          Invoke-WebRequest -Uri $url -OutFile $output
          Expand-Archive -Path $output -DestinationPath "sqlite3"
          Write-Host "SQLite3 DLL downloaded and extracted"
      
      - name: Build for Windows
        run: flutter build windows --release --verbose
      
      - name: Copy SQLite3 DLL to build
        run: |
          Copy-Item "sqlite3\sqlite3.dll" "build\windows\x64\runner\Release\"
          Write-Host "SQLite3 DLL copied to build directory"
      
      - name: Verify SQLite3 DLL
        run: |
          if (Test-Path "build\windows\x64\runner\Release\sqlite3.dll") {
            Write-Host "✓ SQLite3 DLL successfully included in build"
            Get-ChildItem "build\windows\x64\runner\Release\" | Where-Object {$_.Name -like "*.dll"} | ForEach-Object {Write-Host "  - $($_.Name)"}
          } else {
            Write-Host "✗ SQLite3 DLL not found in build"
            exit 1
          }
      
      - name: Archive Windows build
        uses: actions/upload-artifact@v4
        with:
          name: price-calculator-windows
          path: build/windows/x64/runner/Release/
          retention-days: 30

  build-linux:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.24.0'
          channel: 'stable'
      
      - name: Install Linux dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y ninja-build libgtk-3-dev libsqlite3-dev pkg-config
      
      - name: Enable Linux desktop
        run: flutter config --enable-linux-desktop
      
      - name: Get dependencies
        run: flutter pub get
      
      - name: Clean build
        run: flutter clean
      
      - name: Generate code (database, etc.)
        run: dart run build_runner build --delete-conflicting-outputs
      
      - name: Build for Linux
        run: flutter build linux --release --verbose
      
      - name: Verify SQLite3 library
        run: |
          if [ -f "build/linux/x64/release/bundle/lib/libsqlite3.so.0" ] || command -v sqlite3 &> /dev/null; then
            echo "✓ SQLite3 library available"
            ldd build/linux/x64/release/bundle/price_calculator || true
          else
            echo "✗ SQLite3 library not found"
            exit 1
          fi
      
      - name: Archive Linux build
        uses: actions/upload-artifact@v4
        with:
          name: price-calculator-linux
          path: build/linux/x64/release/bundle/
          retention-days: 30