import 'package:flutter_test/flutter_test.dart';
import '../lib/services/license_validation_service.dart';

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  group('License Validation Real Test', () {
    test('Test with exact Python client values', () async {
      final service = LicenseValidationService.instance;
      
      // Use the exact values from the working Python client
      const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const customHwid = 'MEHDI';
      
      print('Testing license validation with:');
      print('Serial: $serial');
      print('HWID: $customHwid');
      
      try {
        final result = await service.validateLicense(
          serialNumber: serial,
          customHwid: customHwid,
          enableDebug: true,
          timeout: const Duration(seconds: 10),
        );
        
        print('Result: ${result.isValid}');
        print('Message: ${result.message}');
        
        // Even if it fails, we want to see the detailed debug output
        expect(result, isNotNull);
        
      } catch (e) {
        print('Exception during license validation: $e');
        // Don't fail the test, we want to see the debug output
      }
    });
  });
}
