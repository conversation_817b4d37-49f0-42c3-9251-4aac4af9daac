import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

void main() {
  group('OAEP SHA256 Investigation', () {
    test('Try different OAEP constructions', () {
      const String publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

      final publicKey = CryptoUtils.rsaPublicKeyFromPem(publicKeyPem);
      final testData = Uint8List.fromList(utf8.encode('test'));

      print('Testing different OAEP configurations:');

      // Method 1: Try creating OAEP with explicit digest parameters
      print('\\n=== Method 1: Manual OAEP with SHA256 ===');
      try {
        // Create the digesters manually
        final sha256 = SHA256Digest();
        final mgf1 = MGF1Digest(sha256);
        
        // Try the OAEPEncoding constructor signature analysis
        print('Attempting OAEPEncoding with manual digest parameters...');
        
        // From pointycastle source, OAEPEncoding constructor might take:
        // OAEPEncoding(AsymmetricBlockCipher cipher, [Uint8List? label])
        // OR there might be a way to specify digest
        
        // Let's first check what methods/properties are available
        final basicOAEP = OAEPEncoding(RSAEngine());
        print('OAEPEncoding runtime type: ${basicOAEP.runtimeType}');
        print('OAEPEncoding string: $basicOAEP');
        
        // Try reflection-like approach to see if we can access internals
        // This is more for debugging to understand the library structure
        
      } catch (e) {
        print('Manual OAEP construction failed: $e');
      }

      // Method 2: Check if there's a way to create OAEP with different digest
      print('\\n=== Method 2: Library exploration ===');
      try {
        // Let's see what digest algorithms are available
        final sha1 = SHA1Digest();
        final sha256 = SHA256Digest();
        final sha512 = SHA512Digest();
        
        print('SHA1 algorithm name: ${sha1.algorithmName}');
        print('SHA256 algorithm name: ${sha256.algorithmName}');
        print('SHA512 algorithm name: ${sha512.algorithmName}');
        
        // Check MGF1 variants
        final mgf1_sha1 = MGF1Digest(sha1);
        final mgf1_sha256 = MGF1Digest(sha256);
        
        print('MGF1-SHA1 algorithm: ${mgf1_sha1.algorithmName}');
        print('MGF1-SHA256 algorithm: ${mgf1_sha256.algorithmName}');
        
      } catch (e) {
        print('Digest exploration failed: $e');
      }

      // Method 3: Try different cipher creation approaches
      print('\\n=== Method 3: Alternative cipher approaches ===');
      try {
        // Maybe we can create a different type of cipher entirely
        final rsaEngine = RSAEngine();
        print('RSA Engine algorithm: ${rsaEngine.algorithmName}');
        
        // Try PKCS1 for comparison
        final pkcs1 = PKCS1Encoding(RSAEngine());
        pkcs1.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        final pkcs1Encrypted = pkcs1.process(testData);
        print('PKCS1 encrypted length: ${pkcs1Encrypted.length}');
        
      } catch (e) {
        print('Alternative cipher approach failed: $e');
      }

      // Method 4: Check if OAEPEncoding has any other constructors or methods
      print('\\n=== Method 4: OAEPEncoding deep dive ===');
      try {
        final oaep = OAEPEncoding(RSAEngine());
        print('OAEP toString: $oaep');
        print('OAEP hashCode: ${oaep.hashCode}');
        
        // Try to see if we can change digest after construction
        // (This is unlikely to work but worth trying)
        
        oaep.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        final encrypted = oaep.process(testData);
        print('Default OAEP encrypted: ${base64Encode(encrypted)}');
        
      } catch (e) {
        print('OAEP deep dive failed: $e');
      }
    });
  });
}
