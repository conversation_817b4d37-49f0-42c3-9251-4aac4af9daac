import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/calculator_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/models/calculation.dart';

void main() {
  group('Calculator Save Tests', () {
    late CalculatorProvider calculatorProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      calculatorProvider = CalculatorProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should save calculation with valid data', () async {
      // Set up valid calculation data
      calculatorProvider.updateClientName('Test Client');
      calculatorProvider.updateClientReference('TC-001');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateThickness(2.0);
      calculatorProvider.updateLinearMeters(10.0);
      calculatorProvider.updateCuttingSpeed(100.0);
      calculatorProvider.updateIsDesignProvided(true);
      calculatorProvider.updateDesignReference('design-001.dwg');

      // Save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save was successful
      expect(success, isTrue);

      // Verify calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(1));

      final savedCalculation = calculations.first;
      expect(savedCalculation.clientName, equals('Test Client'));
      expect(savedCalculation.clientReference, equals('TC-001'));
      expect(savedCalculation.material, equals('steel'));
      expect(savedCalculation.gasType, equals('oxygen'));
      expect(savedCalculation.thickness, equals(2.0));
      expect(savedCalculation.linearMeters, equals(10.0));
      expect(savedCalculation.cuttingSpeed, equals(100.0));
      expect(savedCalculation.designProvided, isTrue);
      expect(savedCalculation.designReference, equals('design-001.dwg'));
      expect(savedCalculation.status, equals(CalculationStatus.pending));
      expect(savedCalculation.totalPrice, greaterThan(0));
    });

    test('should not save calculation with missing client name', () async {
      // Set up calculation data without client name
      calculatorProvider.updateClientReference('TC-001');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateLinearMeters(10.0);

      // Try to save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save failed
      expect(success, isFalse);

      // Verify no calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(0));
    });

    test('should not save calculation with missing client reference', () async {
      // Set up calculation data without client reference
      calculatorProvider.updateClientName('Test Client');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateLinearMeters(10.0);

      // Try to save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save failed
      expect(success, isFalse);

      // Verify no calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(0));
    });

    test('should not save calculation with missing material', () async {
      // Set up calculation data without material
      calculatorProvider.updateClientName('Test Client');
      calculatorProvider.updateClientReference('TC-001');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateLinearMeters(10.0);

      // Try to save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save failed
      expect(success, isFalse);

      // Verify no calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(0));
    });

    test('should not save calculation with missing gas type', () async {
      // Set up calculation data without gas type
      calculatorProvider.updateClientName('Test Client');
      calculatorProvider.updateClientReference('TC-001');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateLinearMeters(10.0);

      // Try to save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save failed
      expect(success, isFalse);

      // Verify no calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(0));
    });

    test('should save calculation without design reference when design not provided', () async {
      // Set up valid calculation data without design
      calculatorProvider.updateClientName('Test Client');
      calculatorProvider.updateClientReference('TC-001');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateThickness(2.0);
      calculatorProvider.updateLinearMeters(10.0);
      calculatorProvider.updateCuttingSpeed(100.0);
      calculatorProvider.updateIsDesignProvided(false);

      // Save the calculation
      final success = await calculatorProvider.saveCalculation();

      // Verify save was successful
      expect(success, isTrue);

      // Verify calculation was saved to database
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(1));

      final savedCalculation = calculations.first;
      expect(savedCalculation.designProvided, isFalse);
      expect(savedCalculation.designReference, isNull);
    });

    test('should generate unique IDs for multiple calculations', () async {
      // Save first calculation
      calculatorProvider.updateClientName('Client 1');
      calculatorProvider.updateClientReference('C1-001');
      calculatorProvider.updateMaterial('steel');
      calculatorProvider.updateGasType('oxygen');
      calculatorProvider.updateLinearMeters(5.0);

      await calculatorProvider.saveCalculation();

      // Reset and save second calculation
      await calculatorProvider.resetCalculator();
      calculatorProvider.updateClientName('Client 2');
      calculatorProvider.updateClientReference('C2-001');
      calculatorProvider.updateMaterial('aluminum');
      calculatorProvider.updateGasType('air');
      calculatorProvider.updateLinearMeters(8.0);

      await calculatorProvider.saveCalculation();

      // Verify both calculations were saved with unique IDs
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.length, equals(2));
      expect(calculations[0].id, isNot(equals(calculations[1].id)));
    });
  });
}
