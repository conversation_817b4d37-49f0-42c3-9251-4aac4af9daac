import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/material_service.dart';
import 'package:laser_cutting_calculator/services/gas_service.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Tab Refresh Tests', () {
    late MaterialService materialService;
    late GasService gasService;
    late MaterialProvider materialProvider;
    late GasProvider gasProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      materialService = MaterialService.instance;
      gasService = GasService.instance;
      materialProvider = MaterialProvider();
      gasProvider = GasProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    group('Material Deletion Persistence Tests', () {
      test('should persist material deletion across provider reloads', () async {
        await materialProvider.initialize();

        // Verify initial materials exist
        expect(materialProvider.materialRates.containsKey('acier'), isTrue);
        expect(materialProvider.materialRates.containsKey('inox'), isTrue);

        // Delete a material
        final deleteSuccess = await materialProvider.removeMaterial('acier');
        expect(deleteSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('acier'), isFalse);

        // Create a new provider instance (simulating tab switch)
        final newMaterialProvider = MaterialProvider();
        await newMaterialProvider.initialize();

        // Verify deletion persisted
        expect(newMaterialProvider.materialRates.containsKey('acier'), isFalse);
        expect(newMaterialProvider.materialRates.containsKey('inox'), isTrue);
      });

      test('should persist custom material addition across provider reloads', () async {
        await materialProvider.initialize();

        // Add a custom material
        const materialName = 'Titanium';
        const materialRate = 1200.0;

        final addSuccess = await materialProvider.addMaterial(materialName, materialRate);
        expect(addSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('titanium'), isTrue);

        // Create a new provider instance (simulating tab switch)
        final newMaterialProvider = MaterialProvider();
        await newMaterialProvider.initialize();

        // Verify addition persisted
        expect(newMaterialProvider.materialRates.containsKey('titanium'), isTrue);
        expect(newMaterialProvider.materialRates['titanium'], equals(materialRate));
      });

      test('should handle multiple operations and persist correctly', () async {
        await materialProvider.initialize();

        // Perform multiple operations
        await materialProvider.removeMaterial('cuivre'); // Delete default
        await materialProvider.addMaterial('Carbon Steel', 680.0); // Add custom
        await materialProvider.removeMaterial('aluminum'); // Delete another default

        // Verify changes in current provider
        expect(materialProvider.materialRates.containsKey('cuivre'), isFalse);
        expect(materialProvider.materialRates.containsKey('carbon-steel'), isTrue);
        expect(materialProvider.materialRates.containsKey('aluminum'), isFalse);
        expect(materialProvider.materialRates.containsKey('inox'), isTrue); // Should remain

        // Create new provider instance
        final newMaterialProvider = MaterialProvider();
        await newMaterialProvider.initialize();

        // Verify all changes persisted
        expect(newMaterialProvider.materialRates.containsKey('cuivre'), isFalse);
        expect(newMaterialProvider.materialRates.containsKey('carbon-steel'), isTrue);
        expect(newMaterialProvider.materialRates['carbon-steel'], equals(680.0));
        expect(newMaterialProvider.materialRates.containsKey('aluminum'), isFalse);
        expect(newMaterialProvider.materialRates.containsKey('inox'), isTrue);
      });
    });

    group('Gas Type Deletion Persistence Tests', () {
      test('should persist gas type deletion across provider reloads', () async {
        await gasProvider.initialize();

        // Verify initial gas types exist
        expect(gasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue);
        expect(gasProvider.gasTypes.containsKey('Azote (N₂)'), isTrue);

        // Delete a gas type
        final deleteSuccess = await gasProvider.removeGasType('Oxygène (O₂)');
        expect(deleteSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey('Oxygène (O₂)'), isFalse);

        // Create a new provider instance (simulating tab switch)
        final newGasProvider = GasProvider();
        await newGasProvider.initialize();

        // Verify deletion persisted
        expect(newGasProvider.gasTypes.containsKey('Oxygène (O₂)'), isFalse);
        expect(newGasProvider.gasTypes.containsKey('Azote (N₂)'), isTrue);
      });

      test('should persist custom gas type addition across provider reloads', () async {
        await gasProvider.initialize();

        // Add a custom gas type
        const gasName = 'Argon';

        final addSuccess = await gasProvider.addGasType(gasName);
        expect(addSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey(gasName), isTrue);

        // Create a new provider instance (simulating tab switch)
        final newGasProvider = GasProvider();
        await newGasProvider.initialize();

        // Verify addition persisted
        expect(newGasProvider.gasTypes.containsKey(gasName), isTrue);
        expect(newGasProvider.gasTypes[gasName]?.keys.length, equals(4)); // All thickness ranges
      });

      test('should handle gas type operations and persist correctly', () async {
        await gasProvider.initialize();

        // Perform multiple operations
        await gasProvider.removeGasType('Air Comprimé'); // Delete default
        await gasProvider.addGasType('Helium'); // Add custom
        await gasProvider.removeGasType('Azote (N₂)'); // Delete another default

        // Verify changes in current provider
        expect(gasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(gasProvider.gasTypes.containsKey('Helium'), isTrue);
        expect(gasProvider.gasTypes.containsKey('Azote (N₂)'), isFalse);
        expect(gasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue); // Should remain

        // Create new provider instance
        final newGasProvider = GasProvider();
        await newGasProvider.initialize();

        // Verify all changes persisted
        expect(newGasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(newGasProvider.gasTypes.containsKey('Helium'), isTrue);
        expect(newGasProvider.gasTypes['Helium']?.keys.length, equals(4));
        expect(newGasProvider.gasTypes.containsKey('Azote (N₂)'), isFalse);
        expect(newGasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue);
      });
    });

    group('Cross-Provider Persistence Tests', () {
      test('should persist changes across both material and gas providers', () async {
        await materialProvider.initialize();
        await gasProvider.initialize();

        // Make changes to both
        await materialProvider.removeMaterial('tole_galvanisee');
        await materialProvider.addMaterial('Brass', 750.0);
        await gasProvider.removeGasType('Air Comprimé');
        await gasProvider.addGasType('Carbon Dioxide');

        // Create new provider instances
        final newMaterialProvider = MaterialProvider();
        final newGasProvider = GasProvider();
        await newMaterialProvider.initialize();
        await newGasProvider.initialize();

        // Verify all changes persisted
        expect(newMaterialProvider.materialRates.containsKey('tole_galvanisee'), isFalse);
        expect(newMaterialProvider.materialRates.containsKey('brass'), isTrue);
        expect(newMaterialProvider.materialRates['brass'], equals(750.0));

        expect(newGasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(newGasProvider.gasTypes.containsKey('Carbon Dioxide'), isTrue);
      });

      test('should maintain data integrity after multiple tab switches', () async {
        // Simulate multiple tab switches with operations
        for (int i = 0; i < 3; i++) {
          final materialProv = MaterialProvider();
          final gasProv = GasProvider();
          await materialProv.initialize();
          await gasProv.initialize();

          if (i == 0) {
            // First switch: delete some items
            await materialProv.removeMaterial('acier');
            await gasProv.removeGasType('Oxygène (O₂)');
          } else if (i == 1) {
            // Second switch: add some items
            await materialProv.addMaterial('Test Material $i', 100.0 * i);
            await gasProv.addGasType('Test Gas $i');
          } else {
            // Third switch: verify everything is still there
            expect(materialProv.materialRates.containsKey('acier'), isFalse);
            expect(materialProv.materialRates.containsKey('test-material-1'), isTrue);
            expect(gasProv.gasTypes.containsKey('Oxygène (O₂)'), isFalse);
            expect(gasProv.gasTypes.containsKey('Test Gas 1'), isTrue);
          }
        }
      });
    });

    group('Service Level Persistence Tests', () {
      test('should persist material changes at service level', () async {
        // Delete using service directly
        await materialService.removeMaterial('inox');

        // Add custom material using service
        await materialService.addCustomMaterial('Stainless Steel', 850.0);

        // Verify changes persist when getting all materials
        final materials = await materialService.getAllMaterialRates();
        expect(materials.containsKey('inox'), isFalse);
        expect(materials.containsKey('stainless-steel'), isTrue);
        expect(materials['stainless-steel'], equals(850.0));
      });

      test('should persist gas type changes at service level', () async {
        // Delete using service directly
        await gasService.removeGasType('Azote (N₂)');

        // Add custom gas type using service
        await gasService.addCustomGasType('Xenon');

        // Verify changes persist when getting all gas types
        final gasTypes = await gasService.getAllGasTypes();
        expect(gasTypes.containsKey('Azote (N₂)'), isFalse);
        expect(gasTypes.containsKey('Xenon'), isTrue);
        expect(gasTypes['Xenon']?.keys.length, equals(4));
      });
    });
  });
}
