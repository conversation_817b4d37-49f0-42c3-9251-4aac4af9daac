import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/calculator_provider.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/database/daos.dart';

void main() {
  group('Design Service Price Integration Tests', () {
    late CalculatorProvider calculatorProvider;
    late ConfigurationProvider configurationProvider;
    late AppSettingsDao appSettingsDao;

    setUpAll(() async {
      // Set test mode to use in-memory database
      DatabaseService.setTestMode(true);
      appSettingsDao = AppSettingsDao(DatabaseService.instance.database);
      calculatorProvider = CalculatorProvider();
      configurationProvider = ConfigurationProvider();
    });

    setUp(() async {
      // Clean up any existing test data
      final setting = await appSettingsDao.getSettingByCategoryAndKey('pricing', 'design_service_price');
      if (setting != null) {
        await appSettingsDao.deleteSetting(setting.id);
      }
    });

    test('CalculatorProvider should load design service price from database', () async {
      // Arrange: Set a specific design service price in the database
      const testPrice = 75.0;
      await appSettingsDao.upsertSetting('pricing', 'design_service_price', testPrice.toString(), 'double');

      // Act: Initialize the calculator provider
      await calculatorProvider.initialize();

      // Assert: The calculator should have loaded the price from database
      expect(calculatorProvider.defaultDesignServicePrice, equals(testPrice));
      expect(calculatorProvider.config.designServicePrice, equals(testPrice));
    });

    test('CalculatorProvider should refresh when configuration is updated', () async {
      // Arrange: Initialize with one price
      const initialPrice = 50.0;
      await appSettingsDao.upsertSetting('pricing', 'design_service_price', initialPrice.toString(), 'double');
      await calculatorProvider.initialize();
      
      expect(calculatorProvider.defaultDesignServicePrice, equals(initialPrice));

      // Act: Update the price in configuration
      const newPrice = 100.0;
      await configurationProvider.setDesignServicePrice(newPrice);
      await calculatorProvider.refreshDesignServicePrice();

      // Assert: The calculator should have the new price
      expect(calculatorProvider.defaultDesignServicePrice, equals(newPrice));
      if (!calculatorProvider.config.isDesignServicePriceCustomized) {
        expect(calculatorProvider.config.designServicePrice, equals(newPrice));
      }
    });

    test('CalculatorProvider should not override customized values when refreshing', () async {
      // Arrange: Initialize with default price
      const defaultPrice = 50.0;
      await appSettingsDao.upsertSetting('pricing', 'design_service_price', defaultPrice.toString(), 'double');
      await calculatorProvider.initialize();
      
      // Customize the price
      const customPrice = 150.0;
      calculatorProvider.startDesignServicePriceCustomization();
      calculatorProvider.validateDesignServicePriceCustomization(customPrice);

      // Act: Update the default price in configuration
      const newDefaultPrice = 100.0;
      await configurationProvider.setDesignServicePrice(newDefaultPrice);
      await calculatorProvider.refreshDesignServicePrice();

      // Assert: The customized value should remain unchanged
      expect(calculatorProvider.defaultDesignServicePrice, equals(newDefaultPrice));
      expect(calculatorProvider.config.designServicePrice, equals(customPrice));
      expect(calculatorProvider.config.isDesignServicePriceCustomized, isTrue);
    });

    test('CalculatorProvider should reset to new default when canceling customization', () async {
      // Arrange: Initialize with default price
      const defaultPrice = 50.0;
      await appSettingsDao.upsertSetting('pricing', 'design_service_price', defaultPrice.toString(), 'double');
      await calculatorProvider.initialize();
      
      // Customize the price
      const customPrice = 150.0;
      calculatorProvider.startDesignServicePriceCustomization();
      calculatorProvider.validateDesignServicePriceCustomization(customPrice);

      // Update the default price
      const newDefaultPrice = 100.0;
      await configurationProvider.setDesignServicePrice(newDefaultPrice);
      await calculatorProvider.refreshDesignServicePrice();

      // Act: Cancel customization
      await calculatorProvider.cancelDesignServicePriceCustomization();

      // Assert: Should reset to the current default value
      expect(calculatorProvider.config.designServicePrice, equals(newDefaultPrice));
      expect(calculatorProvider.config.isDesignServicePriceCustomized, isFalse);
    });

    tearDown(() async {
      // Clean up test data
      final setting = await appSettingsDao.getSettingByCategoryAndKey('pricing', 'design_service_price');
      if (setting != null) {
        await appSettingsDao.deleteSetting(setting.id);
      }
    });
  });
}
