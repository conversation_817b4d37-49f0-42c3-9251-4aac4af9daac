import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/material_service.dart';
import 'package:laser_cutting_calculator/services/gas_service.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Deletion Functionality Tests', () {
    late MaterialService materialService;
    late GasService gasService;
    late MaterialProvider materialProvider;
    late GasProvider gasProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      materialService = MaterialService.instance;
      gasService = GasService.instance;
      materialProvider = MaterialProvider();
      gasProvider = GasProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    group('Material Deletion Tests', () {
      test('should identify default materials correctly', () {
        // Default materials should not be custom
        expect(materialService.isCustomMaterial('acier'), isFalse);
        expect(materialService.isCustomMaterial('inox'), isFalse);
        expect(materialService.isCustomMaterial('cuivre'), isFalse);
        expect(materialService.isCustomMaterial('aluminum'), isFalse);
        expect(materialService.isCustomMaterial('tole_galvanisee'), isFalse);

        // Custom materials should be custom
        expect(materialService.isCustomMaterial('titanium'), isTrue);
        expect(materialService.isCustomMaterial('brass'), isTrue);
        expect(materialService.isCustomMaterial('custom_material'), isTrue);
      });

      test('should allow deletion of custom materials only', () async {
        // Add a custom material
        const customMaterialName = 'Titanium';
        const customMaterialRate = 1200.0;

        final addSuccess = await materialService.addCustomMaterial(customMaterialName, customMaterialRate);
        expect(addSuccess, isTrue);

        // Verify it exists
        final materialsBeforeRemoval = await materialService.getAllMaterialRates();
        expect(materialsBeforeRemoval.containsKey('titanium'), isTrue);

        // Remove the custom material
        final removeSuccess = await materialService.removeCustomMaterial('titanium');
        expect(removeSuccess, isTrue);

        // Verify it's removed
        final materialsAfterRemoval = await materialService.getAllMaterialRates();
        expect(materialsAfterRemoval.containsKey('titanium'), isFalse);

        // Verify it's actually deleted from the database (not just marked as 'DELETED')
        final configValue = await databaseService.getConfigValue('material_rate_titanium');
        expect(configValue, isNull);
      });

      test('should allow deletion of any material including defaults', () async {
        // Add a material first (simulating a default material)
        await materialService.addCustomMaterial('acier', 250.0);

        // Verify it exists
        final materialsBefore = await materialService.getAllMaterialRates();
        expect(materialsBefore.containsKey('acier'), isTrue);

        // Remove the material - this should work
        final removeSuccess = await materialService.removeCustomMaterial('acier');
        expect(removeSuccess, isTrue);

        // Verify material is actually removed
        final materialsAfterRemoval = await materialService.getAllMaterialRates();
        expect(materialsAfterRemoval.containsKey('acier'), isFalse);

        // Verify it's actually deleted from the database
        final configValue = await databaseService.getConfigValue('material_rate_acier');
        expect(configValue, isNull);
      });

      test('MaterialProvider should handle deletion correctly', () async {
        await materialProvider.initialize();

        // Add a custom material
        const materialName = 'Carbon Steel';
        const materialRate = 680.0;

        final addSuccess = await materialProvider.addMaterial(materialName, materialRate);
        expect(addSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('carbon-steel'), isTrue);

        // Remove the custom material
        final removeSuccess = await materialProvider.removeMaterial('carbon-steel');
        expect(removeSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('carbon-steel'), isFalse);
      });

      test('should handle custom material operations correctly', () async {
        await materialProvider.initialize();

        // Add a custom material
        await materialProvider.addMaterial('Test Material', 500.0);
        expect(materialProvider.materialRates.containsKey('test-material'), isTrue);
        expect(materialProvider.materialRates['test-material'], equals(500.0));

        // Remove the custom material
        await materialProvider.removeMaterial('test-material');
        expect(materialProvider.materialRates.containsKey('test-material'), isFalse);

        // Verify the material is actually deleted from the database
        final configValue = await databaseService.getConfigValue('material_rate_test-material');
        expect(configValue, isNull);
      });
    });

    group('Gas Type Deletion Tests', () {
      test('should identify default gas types correctly', () {
        // Default gas types should not be custom
        expect(gasService.isCustomGasType('Oxygène (O₂)'), isFalse);
        expect(gasService.isCustomGasType('Azote (N₂)'), isFalse);
        expect(gasService.isCustomGasType('Air Comprimé'), isFalse);

        // Custom gas types should be custom
        expect(gasService.isCustomGasType('Argon'), isTrue);
        expect(gasService.isCustomGasType('Helium'), isTrue);
        expect(gasService.isCustomGasType('Custom Gas'), isTrue);
      });

      test('should allow deletion of custom gas types only', () async {
        // Add a custom gas type
        const customGasName = 'Argon';

        final addSuccess = await gasService.addCustomGasType(customGasName);
        expect(addSuccess, isTrue);

        // Verify it exists
        final gasTypesBeforeRemoval = await gasService.getAllGasTypes();
        expect(gasTypesBeforeRemoval.containsKey(customGasName), isTrue);

        // Remove the custom gas type
        final removeSuccess = await gasService.removeCustomGasType(customGasName);
        expect(removeSuccess, isTrue);

        // Verify it's removed
        final gasTypesAfterRemoval = await gasService.getAllGasTypes();
        expect(gasTypesAfterRemoval.containsKey(customGasName), isFalse);
      });

      test('should allow deletion of any gas type including defaults', () async {
        // Add a gas type first (simulating a default gas type)
        await gasService.addCustomGasType('Oxygène (O₂)');

        // Set some costs to make it a complete gas type
        await gasService.updateGasCost('Oxygène (O₂)', '1-5 mm', 10.0);
        await gasService.updateGasCost('Oxygène (O₂)', '5-10 mm', 15.0);
        await gasService.updateGasCost('Oxygène (O₂)', '10-15 mm', 20.0);
        await gasService.updateGasCost('Oxygène (O₂)', '> 15 mm', 25.0);

        // Verify it exists
        final gasTypesBefore = await gasService.getAllGasTypes();
        expect(gasTypesBefore.containsKey('Oxygène (O₂)'), isTrue);

        // Remove the gas type - this should work
        final removeSuccess = await gasService.removeCustomGasType('Oxygène (O₂)');
        expect(removeSuccess, isTrue);

        // Verify gas type is actually removed
        final gasTypesAfterRemoval = await gasService.getAllGasTypes();
        expect(gasTypesAfterRemoval.containsKey('Oxygène (O₂)'), isFalse);

        // Verify all configuration entries are deleted from the database
        final ranges = ['1_5_mm', '5_10_mm', '10_15_mm', 'gt_15_mm'];
        for (final range in ranges) {
          final configValue = await databaseService.getConfigValue('gas_cost_oxygène_o₂_$range');
          expect(configValue, isNull);
        }
      });

      test('GasProvider should handle deletion correctly', () async {
        await gasProvider.initialize();

        // Add a custom gas type
        const gasName = 'Carbon Dioxide';

        final addSuccess = await gasProvider.addGasType(gasName);
        expect(addSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey(gasName), isTrue);

        // Remove the custom gas type
        final removeSuccess = await gasProvider.removeGasType(gasName);
        expect(removeSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey(gasName), isFalse);
      });

      test('should handle custom gas type operations correctly', () async {
        await gasProvider.initialize();

        // Add a custom gas type
        await gasProvider.addGasType('Test Gas');
        expect(gasProvider.gasTypes.containsKey('Test Gas'), isTrue);

        // Remove the custom gas type
        await gasProvider.removeGasType('Test Gas');
        expect(gasProvider.gasTypes.containsKey('Test Gas'), isFalse);

        // Verify the gas type is actually deleted from the database
        // Gas types have multiple config entries, so we verify the service reports it as gone
        final gasTypesAfterRemoval = await gasService.getAllGasTypes();
        expect(gasTypesAfterRemoval.containsKey('Test Gas'), isFalse);
      });

      test('should maintain thickness ranges after gas type deletion', () async {
        await gasProvider.initialize();

        // Add a custom gas type
        const gasName = 'Test Gas';
        await gasProvider.addGasType(gasName);

        // Verify it has all thickness ranges
        expect(gasProvider.gasTypes[gasName]?.keys.length, equals(4));
        expect(gasProvider.gasTypes[gasName]?.containsKey('1-5 mm'), isTrue);
        expect(gasProvider.gasTypes[gasName]?.containsKey('5-10 mm'), isTrue);
        expect(gasProvider.gasTypes[gasName]?.containsKey('10-15 mm'), isTrue);
        expect(gasProvider.gasTypes[gasName]?.containsKey('> 15 mm'), isTrue);

        // Remove it
        await gasProvider.removeGasType(gasName);

        // Verify it's completely removed
        expect(gasProvider.gasTypes.containsKey(gasName), isFalse);
      });
    });

    group('Cross-Service Deletion Tests', () {
      test('should handle simultaneous material and gas type operations', () async {
        await materialProvider.initialize();
        await gasProvider.initialize();

        // Add custom material and gas type
        await materialProvider.addMaterial('Test Material', 500.0);
        await gasProvider.addGasType('Test Gas');

        // Verify both exist
        expect(materialProvider.materialRates.containsKey('test-material'), isTrue);
        expect(gasProvider.gasTypes.containsKey('Test Gas'), isTrue);

        // Remove both
        await materialProvider.removeMaterial('test-material');
        await gasProvider.removeGasType('Test Gas');

        // Verify both are removed
        expect(materialProvider.materialRates.containsKey('test-material'), isFalse);
        expect(gasProvider.gasTypes.containsKey('Test Gas'), isFalse);

        // Verify they are actually deleted from the database
        final materialConfigValue = await databaseService.getConfigValue('material_rate_test-material');
        expect(materialConfigValue, isNull);

        // Note: Gas types have multiple config entries, so we just verify the service reports them as gone
      });
    });
  });
}
