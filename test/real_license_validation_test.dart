import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Real License Validation Test', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      SharedPreferences.setMockInitialValues({});
    });

    test('test with actual serial number from error message', () async {
      // Based on the error pattern, try common license formats
      const testSerials = [
        'EXAUY-J1VPW-WN71A-AKRBJ',  // From the existing test
        'O4T1K-RW2I9-HDAZY-K98BQ',  // Generated by the system 
        'TEST-12345-ABCDE-67890',   // Generic test format
      ];
      
      print('\n=== TESTING MULTIPLE SERIAL NUMBERS ===');
      
      for (final serialNumber in testSerials) {
        print('\n--- Testing Serial: $serialNumber ---');
        
        try {
          final result = await licenseService.validateLicense(
            serialNumber: serialNumber,
            enableDebug: true,
            timeout: const Duration(seconds: 10),
          );

          print('Result: ${result.isValid}');
          print('Code: ${result.responseCode}');
          print('Message: ${result.message}');
          print('HTTP Code: ${result.httpCode}');
          
          if (result.serverResponse != null) {
            print('Server Response Details:');
            result.serverResponse!.forEach((key, value) {
              print('  $key: $value');
            });
          }
          
        } catch (e) {
          print('Exception during validation: $e');
        }
        
        print(''); // Empty line for separation
      }
    });

    test('test with local license server status', () async {
      print('\n=== CHECKING LICENSE SERVER CONNECTIVITY ===');
      
      // Try to test basic connectivity first
      try {
        const serialNumber = 'TEST-CONNECTIVITY-CHECK';
        
        final result = await licenseService.validateLicense(
          serialNumber: serialNumber,
          enableDebug: false,
          timeout: const Duration(seconds: 5),
        );
        
        print('Server connectivity test:');
        print('- Response received: ${result.message}');
        print('- Response code: ${result.responseCode}');
        print('- HTTP code: ${result.httpCode ?? "No HTTP code"}');
        
        // If we get any response (even error), the server is reachable
        if (result.responseCode != 'NO_CONNECTION' && result.responseCode != 'TIMEOUT') {
          print('- Server Status: REACHABLE ✓');
        } else {
          print('- Server Status: UNREACHABLE ✗');
        }
        
      } catch (e) {
        print('Connectivity test failed: $e');
      }
    });

    test('analyze RSA encryption format', () async {
      print('\n=== RSA ENCRYPTION ANALYSIS ===');
      
      const testPayloads = [
        'TEST-SERIAL:ABCD1234',
        'EXAUY-J1VPW-WN71A-AKRBJ:1234567890ABCDEF',
        'SHORT:12345678',
        'VERY-LONG-SERIAL-NUMBER-FOR-TESTING:VERY-LONG-HARDWARE-ID-FOR-TESTING-12345',
      ];
      
      for (final payload in testPayloads) {
        print('\nPayload: $payload (${payload.length} chars)');
        
        try {
          final encrypted = await licenseService.testEncryption(payload);
          print('Encrypted length: ${encrypted.length} chars');
          print('First 64 chars: ${encrypted.substring(0, math.min(64, encrypted.length))}');
          
          // Check if all encrypted payloads have the same length (they should for RSA)
          print('Encryption successful: ✓');
        } catch (e) {
          print('Encryption failed: $e');
        }
      }
    });
  });
}
