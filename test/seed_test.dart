import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/database/database_seeder.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/services/seed_service.dart';

void main() {
  group('Database Seeding Tests', () {
    setUpAll(() async {
      // Set test mode for database
      DatabaseService.setTestMode(true);
    });

    tearDownAll(() async {
      // Reset test mode
      DatabaseService.setTestMode(false);
    });

    test('should seed database with initial data', () async {
      // Arrange
      final seedService = SeedService.instance;
      final dbService = DatabaseService.instance;

      // Act
      await seedService.seedDatabase(force: true);

      // Assert
      final clients = await dbService.getAllClients();
      final calculations = await dbService.getAllCalculations();
      final seedInfo = await seedService.getSeedInfo();

      expect(clients.length, equals(5));
      expect(calculations.length, equals(25));
      expect(seedInfo['seeded'], equals('true'));
      expect(seedInfo['seed_date'], isNotNull);
    });

    test('should not re-seed if already seeded', () async {
      // Arrange
      final seedService = SeedService.instance;
      
      // Seed once
      await seedService.seedDatabase(force: true);
      final firstSeedDate = (await seedService.getSeedInfo())['seed_date'];

      // Wait a moment
      await Future.delayed(const Duration(milliseconds: 100));

      // Act - try to seed again without force
      await seedService.seedDatabase(force: false);
      final secondSeedDate = (await seedService.getSeedInfo())['seed_date'];

      // Assert - seed date should be the same
      expect(secondSeedDate, equals(firstSeedDate));
    });

    test('should re-seed when forced', () async {
      // Arrange
      final seedService = SeedService.instance;
      
      // Seed once
      await seedService.seedDatabase(force: true);
      final firstSeedDate = (await seedService.getSeedInfo())['seed_date'];

      // Wait a moment
      await Future.delayed(const Duration(milliseconds: 100));

      // Act - force re-seed
      await seedService.seedDatabase(force: true);
      final secondSeedDate = (await seedService.getSeedInfo())['seed_date'];

      // Assert - seed date should be different
      expect(secondSeedDate, isNot(equals(firstSeedDate)));
    });

    test('should check if seeding is needed correctly', () async {
      // Arrange
      final seedService = SeedService.instance;
      
      // Clear any existing seed state
      await seedService.seedDatabase(force: true);

      // Act & Assert
      expect(await DatabaseSeeder.needsSeeding(), isFalse);

      // Clear seed state
      await seedService.resetToInitialState();
      
      // Should need seeding again
      expect(await DatabaseSeeder.needsSeeding(), isFalse); // Will be false because reset seeds again
    });

    test('should provide seed information', () async {
      // Arrange
      final seedService = SeedService.instance;
      
      // Act
      await seedService.seedDatabase(force: true);
      final seedInfo = await DatabaseSeeder.getSeedInfo();

      // Assert
      expect(seedInfo['seeded'], equals('true'));
      expect(seedInfo['seed_date'], isNotNull);
      expect(seedInfo['app_version'], equals('1.0.0'));
    });

    test('should verify database statistics after seeding', () async {
      // Arrange
      final seedService = SeedService.instance;
      final dbService = DatabaseService.instance;

      // Act
      await seedService.seedDatabase(force: true);
      final stats = await dbService.getStatistics();

      // Assert
      expect(stats['totalClients'], equals(5));
      expect(stats['totalCalculations'], equals(25));
      expect(stats['totalRevenue'], greaterThan(0));
      expect(stats['averageOrderValue'], greaterThan(0));
    });
  });
}
