import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/migration_service.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Migration Service Tests', () {
    late MigrationService migrationService;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      migrationService = MigrationService();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      // Clean up database after each test
      await databaseService.clearAllData();
      // Reset database service to get fresh instance for next test
      DatabaseService.setTestMode(true);
    });

    test('should run migrations and seed initial data', () async {
      // Ensure database is empty
      await databaseService.clearAllData();
      
      // Run migrations
      await migrationService.runMigrations();
      
      // Check if initial data was seeded
      final hasData = await migrationService.hasInitialData();
      expect(hasData, isTrue);
      
      // Verify clients were seeded
      final clients = await databaseService.getAllClients();
      expect(clients.isNotEmpty, isTrue);
      
      // Verify calculations were seeded
      final calculations = await databaseService.getAllCalculations();
      expect(calculations.isNotEmpty, isTrue);
    });

    test('should not seed data if already exists', () async {
      // Run migrations first time
      await migrationService.runMigrations();
      final initialClientCount = (await databaseService.getAllClients()).length;
      
      // Run migrations again
      await migrationService.runMigrations();
      final finalClientCount = (await databaseService.getAllClients()).length;
      
      // Should not duplicate data
      expect(finalClientCount, equals(initialClientCount));
    });

    test('should clear all data', () async {
      // Seed some data
      await migrationService.runMigrations();
      expect(await migrationService.hasInitialData(), isTrue);
      
      // Clear all data
      await migrationService.clearAllData();
      expect(await migrationService.hasInitialData(), isFalse);
      
      // Verify database is empty
      final clients = await databaseService.getAllClients();
      final calculations = await databaseService.getAllCalculations();
      expect(clients.isEmpty, isTrue);
      expect(calculations.isEmpty, isTrue);
    });
  });
}
