import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

void main() {
  group('OAEP Padding Investigation', () {
    const String testPublicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

    test('Check OAEPEncoding constructors and default behavior', () {
      // Parse the key using basic_utils (same as service)
      final publicKey = CryptoUtils.rsaPublicKeyFromPem(testPublicKeyPem);
      print('RSA Public Key parsed successfully');
      print('Key size: ${publicKey.modulus!.bitLength} bits');
      
      // Test the payload format that matches Python client
      final testPayload = json.encode({
        'serial': 'O4T1K-RW2I9-HDAZY-K98BQ',
        'hwid': 'MEHDI',
      });
      print('Test payload: $testPayload');
      
      // Test default OAEP encoding
      final payloadBytes = Uint8List.fromList(utf8.encode(testPayload));
      final cipher = OAEPEncoding(RSAEngine());
      cipher.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
      
      final encrypted = cipher.process(payloadBytes);
      final base64Encrypted = base64Encode(encrypted);
      
      print('Encrypted length: ${encrypted.length} bytes');
      print('Base64 length: ${base64Encrypted.length} characters');
      print('Base64 encrypted: $base64Encrypted');
      
      // Test if we can get information about the OAEP digest
      print('\\nOAEPEncoding analysis:');
      print('Type: ${cipher.runtimeType}');
      
      // Check if encryption is consistent
      final cipher2 = OAEPEncoding(RSAEngine());
      cipher2.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
      final encrypted2 = cipher2.process(payloadBytes);
      final base64Encrypted2 = base64Encode(encrypted2);
      
      print('\\nConsistency check:');
      print('First encryption:  $base64Encrypted');
      print('Second encryption: $base64Encrypted2');
      print('Are they identical? ${base64Encrypted == base64Encrypted2}');
      
      // RSA with OAEP should produce different results each time due to random padding
      expect(base64Encrypted, isNot(equals(base64Encrypted2)));
      expect(encrypted.length, equals(256)); // 2048-bit key = 256 bytes
    });

    test('Check if OAEPEncoding accepts digest parameter', () {
      final publicKey = CryptoUtils.rsaPublicKeyFromPem(testPublicKeyPem);
      final testData = Uint8List.fromList(utf8.encode('test'));
      
      // Test constructor options
      print('Testing OAEPEncoding constructor options:');
      
      // Basic constructor (what we're currently using)
      try {
        final basic = OAEPEncoding(RSAEngine());
        basic.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        print('✓ Basic OAEPEncoding(RSAEngine()) works');
        
        final encrypted = basic.process(testData);
        print('  Encrypted ${testData.length} bytes -> ${encrypted.length} bytes');
      } catch (e) {
        print('✗ Basic constructor failed: $e');
      }
      
      // Try with label parameter
      try {
        final withLabel = OAEPEncoding(RSAEngine(), null);
        withLabel.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        print('✓ OAEPEncoding(RSAEngine(), null) works');
      } catch (e) {
        print('✗ Constructor with label failed: $e');
      }
      
      // Try with empty label
      try {
        final withEmptyLabel = OAEPEncoding(RSAEngine(), Uint8List(0));
        withEmptyLabel.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        print('✓ OAEPEncoding(RSAEngine(), Uint8List(0)) works');
      } catch (e) {
        print('✗ Constructor with empty label failed: $e');
      }
    });
  });
}
