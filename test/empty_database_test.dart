import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/services/material_service.dart';
import 'package:laser_cutting_calculator/services/gas_service.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';

void main() {
  group('Empty Database Tests', () {
    late DatabaseService databaseService;
    late MaterialService materialService;
    late GasService gasService;
    late ConfigurationProvider configProvider;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      databaseService = DatabaseService.instance;
      materialService = MaterialService.instance;
      gasService = GasService.instance;
      configProvider = ConfigurationProvider();
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    group('Database Starts Empty', () {
      test('should have no materials in empty database', () async {
        final materials = await materialService.getAllMaterialRates();
        expect(materials.isEmpty, isTrue);
        
        final labels = await materialService.getAllMaterialLabels();
        expect(labels.isEmpty, isTrue);
        
        print('✅ Database starts with no materials');
      });

      test('should have no gas types in empty database', () async {
        final gasTypes = await gasService.getAllGasTypes();
        expect(gasTypes.isEmpty, isTrue);
        
        final labels = await gasService.getAllGasLabels();
        expect(labels.isEmpty, isTrue);
        
        print('✅ Database starts with no gas types');
      });

      test('should have no configuration values in empty database', () async {
        final setupFee = await configProvider.getDesignServicePrice();
        expect(setupFee, equals(0.0));
        
        final companyName = await configProvider.getCompanyName();
        expect(companyName, isNull);
        
        final defaultMaterial = await configProvider.getDefaultMaterial();
        expect(defaultMaterial, isNull);
        
        final defaultGasType = await configProvider.getDefaultGasType();
        expect(defaultGasType, isNull);
        
        print('✅ Database starts with no configuration values');
      });

      test('should have no clients in empty database', () async {
        final clients = await databaseService.getAllClients();
        expect(clients.isEmpty, isTrue);
        
        print('✅ Database starts with no clients');
      });

      test('should have no calculations in empty database', () async {
        final calculations = await databaseService.getAllCalculations();
        expect(calculations.isEmpty, isTrue);
        
        print('✅ Database starts with no calculations');
      });
    });

    group('Providers Start Empty', () {
      test('MaterialProvider should start empty', () async {
        final materialProvider = MaterialProvider();
        await materialProvider.initialize();
        
        expect(materialProvider.materialRates.isEmpty, isTrue);
        expect(materialProvider.materialLabels.isEmpty, isTrue);
        
        print('✅ MaterialProvider starts empty');
      });

      test('GasProvider should start empty', () async {
        final gasProvider = GasProvider();
        await gasProvider.initialize();
        
        expect(gasProvider.gasTypes.isEmpty, isTrue);
        expect(gasProvider.gasLabels.isEmpty, isTrue);
        
        print('✅ GasProvider starts empty');
      });
    });

    group('User Can Add Data to Empty Database', () {
      test('should allow adding materials to empty database', () async {
        final materialProvider = MaterialProvider();
        await materialProvider.initialize();
        
        // Verify empty
        expect(materialProvider.materialRates.isEmpty, isTrue);
        
        // Add a material
        final addSuccess = await materialProvider.addMaterial('Steel', 500.0);
        expect(addSuccess, isTrue);
        
        // Verify added
        expect(materialProvider.materialRates.containsKey('steel'), isTrue);
        expect(materialProvider.materialRates['steel'], equals(500.0));
        expect(materialProvider.materialRates.length, equals(1));
        
        print('✅ Can add materials to empty database');
      });

      test('should allow adding gas types to empty database', () async {
        final gasProvider = GasProvider();
        await gasProvider.initialize();
        
        // Verify empty
        expect(gasProvider.gasTypes.isEmpty, isTrue);
        
        // Add a gas type
        final addSuccess = await gasProvider.addGasType('Oxygen');
        expect(addSuccess, isTrue);
        
        // Verify added
        expect(gasProvider.gasTypes.containsKey('Oxygen'), isTrue);
        expect(gasProvider.gasTypes['Oxygen']?.keys.length, equals(4)); // All thickness ranges
        expect(gasProvider.gasTypes.length, equals(1));
        
        print('✅ Can add gas types to empty database');
      });

      test('should allow setting configuration values in empty database', () async {
        // Set configuration values
        await configProvider.setDesignServicePrice(75.0);
        await configProvider.setCompanyName('My Laser Company');
        await configProvider.setDefaultMaterial('steel');
        await configProvider.setDefaultGasType('oxygen');
        
        // Verify values are set
        final setupFee = await configProvider.getDesignServicePrice();
        expect(setupFee, equals(75.0));
        
        final companyName = await configProvider.getCompanyName();
        expect(companyName, equals('My Laser Company'));
        
        final defaultMaterial = await configProvider.getDefaultMaterial();
        expect(defaultMaterial, equals('steel'));
        
        final defaultGasType = await configProvider.getDefaultGasType();
        expect(defaultGasType, equals('oxygen'));
        
        print('✅ Can set configuration values in empty database');
      });
    });

    group('Database Persistence Tests', () {
      test('should persist user data across provider instances', () async {
        // Add data with first provider instance
        final materialProvider1 = MaterialProvider();
        await materialProvider1.initialize();
        await materialProvider1.addMaterial('Aluminum', 350.0);
        
        final gasProvider1 = GasProvider();
        await gasProvider1.initialize();
        await gasProvider1.addGasType('Nitrogen');
        
        // Create new provider instances (simulating app restart)
        final materialProvider2 = MaterialProvider();
        await materialProvider2.initialize();
        
        final gasProvider2 = GasProvider();
        await gasProvider2.initialize();
        
        // Verify data persisted
        expect(materialProvider2.materialRates.containsKey('aluminum'), isTrue);
        expect(materialProvider2.materialRates['aluminum'], equals(350.0));
        
        expect(gasProvider2.gasTypes.containsKey('Nitrogen'), isTrue);
        expect(gasProvider2.gasTypes['Nitrogen']?.keys.length, equals(4));
        
        print('✅ User data persists across provider instances');
      });

      test('should maintain empty state when no data is added', () async {
        // Create multiple provider instances without adding data
        for (int i = 0; i < 3; i++) {
          final materialProvider = MaterialProvider();
          await materialProvider.initialize();
          expect(materialProvider.materialRates.isEmpty, isTrue);
          
          final gasProvider = GasProvider();
          await gasProvider.initialize();
          expect(gasProvider.gasTypes.isEmpty, isTrue);
        }
        
        print('✅ Database maintains empty state when no data is added');
      });
    });

    group('No Hard-coded Values Tests', () {
      test('should not load any hard-coded materials', () async {
        final materials = await materialService.getAllMaterialRates();
        
        // Should not have any of the old hard-coded materials
        expect(materials.containsKey('acier'), isFalse);
        expect(materials.containsKey('inox'), isFalse);
        expect(materials.containsKey('cuivre'), isFalse);
        expect(materials.containsKey('aluminum'), isFalse);
        expect(materials.containsKey('tole_galvanisee'), isFalse);
        
        print('✅ No hard-coded materials loaded');
      });

      test('should not load any hard-coded gas types', () async {
        final gasTypes = await gasService.getAllGasTypes();
        
        // Should not have any of the old hard-coded gas types
        expect(gasTypes.containsKey('Oxygène (O₂)'), isFalse);
        expect(gasTypes.containsKey('Azote (N₂)'), isFalse);
        expect(gasTypes.containsKey('Air Comprimé'), isFalse);
        
        print('✅ No hard-coded gas types loaded');
      });

      test('should not have any hard-coded configuration values', () async {
        // All configuration values should be 0 or null when not set
        expect(await configProvider.getDesignServicePrice(), equals(0.0));
        expect(await configProvider.getPricePerMeter(), equals(0.0));
        expect(await configProvider.getMinimumOrderValue(), equals(0.0));
        expect(await configProvider.getTaxRate(), equals(0.0));
        expect(await configProvider.getDefaultThickness(), equals(0.0));
        expect(await configProvider.getDefaultCuttingSpeed(), equals(0.0));
        expect(await configProvider.getDefaultLinearMeters(), equals(0.0));
        
        expect(await configProvider.getCompanyName(), isNull);
        expect(await configProvider.getCompanyAddress(), isNull);
        expect(await configProvider.getCompanyPhone(), isNull);
        expect(await configProvider.getCompanyEmail(), isNull);
        expect(await configProvider.getDefaultMaterial(), isNull);
        expect(await configProvider.getDefaultGasType(), isNull);
        
        print('✅ No hard-coded configuration values');
      });
    });

    group('Clean Slate Tests', () {
      test('should provide a completely clean slate for users', () async {
        // Verify everything is empty
        final materials = await materialService.getAllMaterialRates();
        final gasTypes = await gasService.getAllGasTypes();
        final clients = await databaseService.getAllClients();
        final calculations = await databaseService.getAllCalculations();
        
        expect(materials.isEmpty, isTrue);
        expect(gasTypes.isEmpty, isTrue);
        expect(clients.isEmpty, isTrue);
        expect(calculations.isEmpty, isTrue);
        
        // Verify configuration is empty
        expect(await configProvider.getDesignServicePrice(), equals(0.0));
        expect(await configProvider.getCompanyName(), isNull);
        
        print('✅ Database provides a completely clean slate');
      });

      test('should allow users to build their own data from scratch', () async {
        // Start with empty database
        final materialProvider = MaterialProvider();
        final gasProvider = GasProvider();
        await materialProvider.initialize();
        await gasProvider.initialize();
        
        expect(materialProvider.materialRates.isEmpty, isTrue);
        expect(gasProvider.gasTypes.isEmpty, isTrue);
        
        // Build custom data
        await materialProvider.addMaterial('Custom Steel', 600.0);
        await materialProvider.addMaterial('Custom Aluminum', 400.0);
        await gasProvider.addGasType('Custom Oxygen');
        await gasProvider.addGasType('Custom Nitrogen');
        await configProvider.setDesignServicePrice(100.0);
        await configProvider.setCompanyName('Custom Laser Co');
        
        // Verify custom data
        expect(materialProvider.materialRates.length, equals(2));
        expect(gasProvider.gasTypes.length, equals(2));
        expect(await configProvider.getDesignServicePrice(), equals(100.0));
        expect(await configProvider.getCompanyName(), equals('Custom Laser Co'));
        
        print('✅ Users can build their own data from scratch');
      });
    });
  });
}
