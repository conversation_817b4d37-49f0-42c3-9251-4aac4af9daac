import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Simple Tab Refresh Tests', () {
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('Material deletion should persist when switching tabs', () async {
      // Simulate being on configuration tab
      final configMaterialProvider = MaterialProvider();
      await configMaterialProvider.initialize();

      // Verify initial state
      expect(configMaterialProvider.materialRates.containsKey('acier'), isTrue);
      expect(configMaterialProvider.materialRates.containsKey('inox'), isTrue);

      // Delete a material (user action in configuration tab)
      final deleteSuccess = await configMaterialProvider.removeMaterial('acier');
      expect(deleteSuccess, isTrue);
      expect(configMaterialProvider.materialRates.containsKey('acier'), isFalse);

      // Simulate switching to calculator tab and back to configuration
      // This creates a new provider instance (like the app does)
      final newConfigMaterialProvider = MaterialProvider();
      await newConfigMaterialProvider.initialize();

      // The deleted material should NOT reappear
      expect(newConfigMaterialProvider.materialRates.containsKey('acier'), isFalse);
      expect(newConfigMaterialProvider.materialRates.containsKey('inox'), isTrue);

      print('✅ Material deletion persisted across tab switch');
    });

    test('Gas type deletion should persist when switching tabs', () async {
      // Simulate being on configuration tab
      final configGasProvider = GasProvider();
      await configGasProvider.initialize();

      // Verify initial state
      expect(configGasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue);
      expect(configGasProvider.gasTypes.containsKey('Azote (N₂)'), isTrue);

      // Delete a gas type (user action in configuration tab)
      final deleteSuccess = await configGasProvider.removeGasType('Oxygène (O₂)');
      expect(deleteSuccess, isTrue);
      expect(configGasProvider.gasTypes.containsKey('Oxygène (O₂)'), isFalse);

      // Simulate switching to calculator tab and back to configuration
      // This creates a new provider instance (like the app does)
      final newConfigGasProvider = GasProvider();
      await newConfigGasProvider.initialize();

      // The deleted gas type should NOT reappear
      expect(newConfigGasProvider.gasTypes.containsKey('Oxygène (O₂)'), isFalse);
      expect(newConfigGasProvider.gasTypes.containsKey('Azote (N₂)'), isTrue);

      print('✅ Gas type deletion persisted across tab switch');
    });

    test('Custom additions should persist when switching tabs', () async {
      // Add custom material
      final materialProvider = MaterialProvider();
      await materialProvider.initialize();
      
      await materialProvider.addMaterial('Custom Steel', 650.0);
      expect(materialProvider.materialRates.containsKey('custom-steel'), isTrue);

      // Add custom gas type
      final gasProvider = GasProvider();
      await gasProvider.initialize();
      
      await gasProvider.addGasType('Custom Gas');
      expect(gasProvider.gasTypes.containsKey('Custom Gas'), isTrue);

      // Simulate tab switch - create new providers
      final newMaterialProvider = MaterialProvider();
      final newGasProvider = GasProvider();
      await newMaterialProvider.initialize();
      await newGasProvider.initialize();

      // Custom additions should persist
      expect(newMaterialProvider.materialRates.containsKey('custom-steel'), isTrue);
      expect(newMaterialProvider.materialRates['custom-steel'], equals(650.0));
      expect(newGasProvider.gasTypes.containsKey('Custom Gas'), isTrue);

      print('✅ Custom additions persisted across tab switch');
    });

    test('Mixed operations should all persist', () async {
      // Perform various operations
      final materialProvider = MaterialProvider();
      final gasProvider = GasProvider();
      await materialProvider.initialize();
      await gasProvider.initialize();

      // Delete some defaults
      await materialProvider.removeMaterial('cuivre');
      await gasProvider.removeGasType('Air Comprimé');

      // Add some customs
      await materialProvider.addMaterial('Titanium', 1200.0);
      await gasProvider.addGasType('Argon');

      // Verify current state
      expect(materialProvider.materialRates.containsKey('cuivre'), isFalse);
      expect(materialProvider.materialRates.containsKey('titanium'), isTrue);
      expect(gasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
      expect(gasProvider.gasTypes.containsKey('Argon'), isTrue);

      // Simulate multiple tab switches
      for (int i = 0; i < 3; i++) {
        final newMaterialProvider = MaterialProvider();
        final newGasProvider = GasProvider();
        await newMaterialProvider.initialize();
        await newGasProvider.initialize();

        // All changes should persist
        expect(newMaterialProvider.materialRates.containsKey('cuivre'), isFalse);
        expect(newMaterialProvider.materialRates.containsKey('titanium'), isTrue);
        expect(newMaterialProvider.materialRates['titanium'], equals(1200.0));
        expect(newGasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(newGasProvider.gasTypes.containsKey('Argon'), isTrue);
      }

      print('✅ All mixed operations persisted across multiple tab switches');
    });

    test('Calculator should see updated data after configuration changes', () async {
      // Simulate configuration tab operations
      final configMaterialProvider = MaterialProvider();
      final configGasProvider = GasProvider();
      await configMaterialProvider.initialize();
      await configGasProvider.initialize();

      // Make changes in configuration
      await configMaterialProvider.removeMaterial('aluminum');
      await configMaterialProvider.addMaterial('Bronze', 720.0);
      await configGasProvider.removeGasType('Azote (N₂)');
      await configGasProvider.addGasType('Helium');

      // Simulate switching to calculator tab (new provider instances)
      final calcMaterialProvider = MaterialProvider();
      final calcGasProvider = GasProvider();
      await calcMaterialProvider.initialize();
      await calcGasProvider.initialize();

      // Calculator should see the updated data
      expect(calcMaterialProvider.materialRates.containsKey('aluminum'), isFalse);
      expect(calcMaterialProvider.materialRates.containsKey('bronze'), isTrue);
      expect(calcMaterialProvider.materialRates['bronze'], equals(720.0));
      expect(calcGasProvider.gasTypes.containsKey('Azote (N₂)'), isFalse);
      expect(calcGasProvider.gasTypes.containsKey('Helium'), isTrue);

      print('✅ Calculator sees updated data after configuration changes');
    });
  });
}
