import 'dart:math' as math;
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

void main() {
  group('Debug Server Communication', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      SharedPreferences.setMockInitialValues({});
    });

    test('manual server communication test', () async {
      print('\n=== MANUAL SERVER COMMUNICATION DEBUG ===');
      
      // Test different endpoints that might exist
      const possibleEndpoints = [
        'http://localhost:8000/api/v1/validate',
        'http://localhost:8000/validate',
        'http://localhost:8000/api/validate',
        'http://localhost:8000/license/validate',
        'http://localhost:8000/',
      ];
      
      for (final endpoint in possibleEndpoints) {
        print('\n--- Testing endpoint: $endpoint ---');
        
        try {
          // Test simple GET request to see if endpoint exists
          final getResponse = await http.get(
            Uri.parse(endpoint),
            headers: {'Accept': 'application/json'},
          ).timeout(const Duration(seconds: 5));
          
          print('GET Response:');
          print('- Status: ${getResponse.statusCode}');
          print('- Body: ${getResponse.body.isEmpty ? "(empty)" : getResponse.body}');
          print('- Headers: ${getResponse.headers}');
          
        } catch (e) {
          print('GET request failed: $e');
        }
        
        try {
          // Test POST request with minimal payload
          final postResponse = await http.post(
            Uri.parse(endpoint),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({
              'apiKey': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
              'payload': 'test-payload',
            }),
          ).timeout(const Duration(seconds: 5));
          
          print('POST Response:');
          print('- Status: ${postResponse.statusCode}');
          print('- Body: ${postResponse.body.isEmpty ? "(empty)" : postResponse.body}');
          print('- Headers: ${postResponse.headers}');
          
        } catch (e) {
          print('POST request failed: $e');
        }
      }
    });

    test('test with different request formats', () async {
      print('\n=== TESTING DIFFERENT REQUEST FORMATS ===');
      
      const endpoint = 'http://localhost:8000/api/v1/validate';
      const testSerial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      
      final hwid = await licenseService.generateHardwareId();
      final payload = '$testSerial:$hwid';
      final encryptedPayload = await licenseService.testEncryption(payload);
      
      // Test different request formats that the server might expect
      final requestFormats = [
        // Format 1: Our current format
        {
          'apiKey': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
          'payload': encryptedPayload,
        },
        // Format 2: With product parameter (since server mentions get_private_key(product))
        {
          'apiKey': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
          'payload': encryptedPayload,
          'product': 'laser_cutting_calculator',
        },
        // Format 3: With separate serial and hwid
        {
          'apiKey': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
          'payload': encryptedPayload,
          'serial': testSerial,
          'hwid': hwid,
        },
        // Format 4: Different parameter names
        {
          'api_key': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
          'encrypted_payload': encryptedPayload,
        },
        // Format 5: Just the essential data
        {
          'payload': encryptedPayload,
        },
      ];
      
      for (int i = 0; i < requestFormats.length; i++) {
        print('\n--- Testing format ${i + 1} ---');
        print('Request: ${jsonEncode(requestFormats[i])}');
        
        try {
          final response = await http.post(
            Uri.parse(endpoint),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestFormats[i]),
          ).timeout(const Duration(seconds: 10));
          
          print('Response:');
          print('- Status: ${response.statusCode}');
          print('- Body: ${response.body.isEmpty ? "(empty)" : response.body}');
          
          if (response.statusCode != 400 || response.body.isNotEmpty) {
            print('🎉 This format got a different response!');
          }
          
        } catch (e) {
          print('Request failed: $e');
        }
      }
    });

    test('test server health and capabilities', () async {
      print('\n=== SERVER HEALTH CHECK ===');
      
      const baseUrl = 'http://localhost:8000';
      
      // Test common health check endpoints
      final healthEndpoints = [
        '$baseUrl/health',
        '$baseUrl/status',
        '$baseUrl/ping',
        '$baseUrl/api/health',
        '$baseUrl/api/status',
        '$baseUrl/',
      ];
      
      for (final endpoint in healthEndpoints) {
        try {
          final response = await http.get(
            Uri.parse(endpoint),
            headers: {'Accept': 'application/json'},
          ).timeout(const Duration(seconds: 3));
          
          print('$endpoint: ${response.statusCode} - ${response.body.isEmpty ? "(empty)" : response.body.substring(0, math.min(100, response.body.length))}');
          
        } catch (e) {
          print('$endpoint: Failed ($e)');
        }
      }
      
      print('\n=== SERVER METHODS TEST ===');
      // Test what HTTP methods the main endpoint accepts
      const mainEndpoint = 'http://localhost:8000/api/v1/validate';
      
      for (final method in ['GET', 'POST', 'PUT', 'PATCH', 'OPTIONS']) {
        try {
          late http.Response response;
          
          switch (method) {
            case 'GET':
              response = await http.get(Uri.parse(mainEndpoint));
              break;
            case 'POST':
              response = await http.post(Uri.parse(mainEndpoint));
              break;
            case 'PUT':
              response = await http.put(Uri.parse(mainEndpoint));
              break;
            case 'PATCH':
              response = await http.patch(Uri.parse(mainEndpoint));
              break;
            case 'OPTIONS':
              response = await http.head(Uri.parse(mainEndpoint)); // Using head as proxy for options
              break;
          }
          
          print('$method: ${response.statusCode}');
          
        } catch (e) {
          print('$method: Failed');
        }
      }
    });
  });
}

/*
DEBUGGING GUIDE:

The server decryption function shows:
```python
def decrypt_data(payload, product):
    private_key = get_private_key(product)
    # ... decryption logic
```

This suggests:
1. The server might need a 'product' parameter
2. Different products might use different private keys
3. The API endpoint might be product-specific

If the tests above show different responses for certain formats or endpoints,
that will help us identify what the server actually expects.

Common issues to check:
1. Missing 'product' parameter
2. Wrong API endpoint
3. Server expecting different parameter names
4. Server not fully started or configured
5. Firewall or network issues
*/
