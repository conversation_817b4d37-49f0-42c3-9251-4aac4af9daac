import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('License Validation Service Tests', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      // Clear shared preferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should return failure result for invalid server response', () async {
      const serialNumber = 'EXAUY-J1VPW-WN71A-AKRBJ';
      const invalidServerUrl = 'http://invalid-server.local/api/v1/validate';

      final result = await licenseService.validateLicense(
        serialNumber: serialNumber,
        customServerUrl: invalidServerUrl,
        timeout: const Duration(seconds: 5),
      );

      expect(result.isValid, isFalse);
      expect(result.responseCode, anyOf(['NO_CONNECTION', contains('TIMEOUT'), contains('HTTP_ERROR')]));
      expect(result.message, isNotEmpty);
    });

    test('should cache validation result', () async {
      final credentials = await licenseService.getStoredCredentials();
      expect(credentials, isNull); // Initially no credentials

      final isValid = await licenseService.isLicenseValid();
      expect(isValid, isFalse); // Initially no valid license
    });

    test('should clear validation cache', () async {
      await licenseService.clearValidationCache();
      
      final isValid = await licenseService.isLicenseValid();
      expect(isValid, isFalse);
    });

    test('should validate with stored credentials when none exist', () async {
      final result = await licenseService.validateWithStoredCredentials();
      
      expect(result.isValid, isFalse);
      expect(result.responseCode, 'NO_STORED_CREDENTIALS');
      expect(result.message, contains('No license credentials found'));
    });

    test('should handle timeout gracefully', () async {
      const serialNumber = 'EXAUY-J1VPW-WN71A-AKRBJ';
      
      final result = await licenseService.validateLicense(
        serialNumber: serialNumber,
        customServerUrl: 'http://httpbin.org/delay/10', // This will timeout
        timeout: const Duration(seconds: 2),
      );

      expect(result.isValid, isFalse);
      expect(result.responseCode, anyOf(['TIMEOUT', 'NO_CONNECTION', contains('ERROR')]));
    });
  });

  group('License Validation Result Tests', () {
    test('should create valid license result', () {
      final timestamp = DateTime.now();
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: timestamp,
      );

      expect(result.isValid, isTrue);
      expect(result.responseCode, 'SUCCESS');
      expect(result.message, 'License validated successfully');
      expect(result.toString(), contains('LicenseValidationResult'));
    });

    test('should create invalid license result', () {
      final timestamp = DateTime.now();
      final result = LicenseValidationResult(
        isValid: false,
        responseCode: 'INVALID_LICENSE',
        message: 'Invalid license key',
        timestamp: timestamp,
      );

      expect(result.isValid, isFalse);
      expect(result.responseCode, 'INVALID_LICENSE');
      expect(result.message, 'Invalid license key');
    });
  });

  group('Stored License Credentials Tests', () {
    test('should store license credentials', () {
      const credentials = StoredLicenseCredentials(
        serialNumber: 'EXAUY-J1VPW-WN71A-AKRBJ',
      );

      expect(credentials.serialNumber, 'EXAUY-J1VPW-WN71A-AKRBJ');
    });

    test('should handle empty credentials', () {
      const credentials = StoredLicenseCredentials(
        serialNumber: '',
      );

      expect(credentials.serialNumber, isEmpty);
    });
  });
}
