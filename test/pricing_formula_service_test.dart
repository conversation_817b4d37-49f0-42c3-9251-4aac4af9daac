import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/pricing_formula_service.dart';

void main() {
  group('PricingFormulaService Tests', () {
    late PricingFormulaService pricingService;

    setUp(() {
      pricingService = PricingFormulaService.instance;
    });

    group('calculateTotalPrice', () {
      test('should handle basic calculation with custom values', () async {
        // Arrange
        const material = 'test_material';
        const cuttingDurationHours = 2.0;
        const gasType = 'test_gas';
        const thickness = 3.0;
        const isDesignFeesIncluded = true;
        const customDesignCost = 100.0;
        const customSetupFees = 50.0;

        // Act - Using custom values since database may not be configured
        final result = await pricingService.calculateTotalPrice(
          material: material,
          cuttingDurationHours: cuttingDurationHours,
          gasType: gasType,
          thickness: thickness,
          isDesignFeesIncluded: isDesignFeesIncluded,
          customDesignCost: customDesignCost,
          customSetupFees: customSetupFees,
        );

        // Assert - Should use custom values when provided
        expect(result.setupFees, equals(50.0));
        expect(result.designCost, equals(100.0));
        expect(result.cuttingDurationHours, equals(2.0));
        expect(result.material, equals(material));
        expect(result.gasType, equals(gasType));
        expect(result.thickness, equals(thickness));
        expect(result.isDesignFeesIncluded, equals(true));
      });

      test('should return zero design cost when design fees are not included', () async {
        // Arrange
        const material = 'test_material';
        const cuttingDurationHours = 1.0;
        const gasType = 'test_gas';
        const thickness = 3.0;
        const isDesignFeesIncluded = false;

        // Act
        final result = await pricingService.calculateTotalPrice(
          material: material,
          cuttingDurationHours: cuttingDurationHours,
          gasType: gasType,
          thickness: thickness,
          isDesignFeesIncluded: isDesignFeesIncluded,
        );

        // Assert
        expect(result.designCost, equals(0.0));
        expect(result.isDesignFeesIncluded, equals(false));
      });

      test('should handle zero cutting duration', () async {
        // Arrange
        const material = 'test_material';
        const cuttingDurationHours = 0.0;
        const gasType = 'test_gas';
        const thickness = 3.0;
        const isDesignFeesIncluded = false;

        // Act
        final result = await pricingService.calculateTotalPrice(
          material: material,
          cuttingDurationHours: cuttingDurationHours,
          gasType: gasType,
          thickness: thickness,
          isDesignFeesIncluded: isDesignFeesIncluded,
        );

        // Assert - With zero duration, machine and gas costs should be zero
        expect(result.cuttingDurationHours, equals(0.0));
        expect(result.designCost, equals(0.0));
      });
    });

    group('thickness range determination', () {
      test('should correctly determine thickness ranges', () {
        // Test public method
        expect(pricingService.getThicknessRange(2.5), equals('1-5 mm'));
        expect(pricingService.getThicknessRange(7.0), equals('5-10 mm'));
        expect(pricingService.getThicknessRange(12.0), equals('10-15 mm'));
        expect(pricingService.getThicknessRange(20.0), equals('> 15 mm'));
      });
    });

    group('default material rates', () {
      test('should return correct default material rates', () {
        // Act
        final defaultRates = pricingService.getDefaultMaterialRates();

        // Assert
        expect(defaultRates['acier'], equals(600.0));
        expect(defaultRates['inox'], equals(900.0));
        expect(defaultRates['cuivre'], equals(0.0));
        expect(defaultRates['aluminum'], equals(0.0));
      });
    });

    group('PricingBreakdown', () {
      test('should generate correct calculation steps', () {
        // Arrange
        const breakdown = PricingBreakdown(
          machineCost: 1200.0,
          gasCost: 100.0,
          setupFees: 50.0,
          designCost: 100.0,
          totalPrice: 1450.0,
          material: 'acier',
          gasType: 'Oxygène (O₂)',
          thickness: 3.0,
          cuttingDurationHours: 2.0,
          isDesignFeesIncluded: true,
        );

        // Act
        final steps = breakdown.getCalculationSteps();

        // Assert
        expect(steps, isNotEmpty);
        expect(steps.first, equals('=== CALCUL DU PRIX TOTAL ==='));
        expect(steps.any((step) => step.contains('1200.00 DH')), isTrue);
        expect(steps.any((step) => step.contains('1450.00 DH')), isTrue);
      });

      test('should convert to and from JSON correctly', () {
        // Arrange
        const originalBreakdown = PricingBreakdown(
          machineCost: 1200.0,
          gasCost: 100.0,
          setupFees: 50.0,
          designCost: 100.0,
          totalPrice: 1450.0,
          material: 'acier',
          gasType: 'Oxygène (O₂)',
          thickness: 3.0,
          cuttingDurationHours: 2.0,
          isDesignFeesIncluded: true,
        );

        // Act
        final json = originalBreakdown.toJson();
        final reconstructedBreakdown = PricingBreakdown.fromJson(json);

        // Assert
        expect(reconstructedBreakdown.machineCost, equals(originalBreakdown.machineCost));
        expect(reconstructedBreakdown.gasCost, equals(originalBreakdown.gasCost));
        expect(reconstructedBreakdown.setupFees, equals(originalBreakdown.setupFees));
        expect(reconstructedBreakdown.designCost, equals(originalBreakdown.designCost));
        expect(reconstructedBreakdown.totalPrice, equals(originalBreakdown.totalPrice));
        expect(reconstructedBreakdown.material, equals(originalBreakdown.material));
        expect(reconstructedBreakdown.gasType, equals(originalBreakdown.gasType));
        expect(reconstructedBreakdown.thickness, equals(originalBreakdown.thickness));
        expect(reconstructedBreakdown.cuttingDurationHours, equals(originalBreakdown.cuttingDurationHours));
        expect(reconstructedBreakdown.isDesignFeesIncluded, equals(originalBreakdown.isDesignFeesIncluded));
      });
    });

    group('edge cases', () {
      test('should handle negative values gracefully', () async {
        // Arrange
        const material = 'test_material';
        const cuttingDurationHours = 1.0;
        const gasType = 'test_gas';
        const thickness = 3.0;
        const isDesignFeesIncluded = true;
        const customDesignCost = -50.0; // Negative value
        const customSetupFees = -25.0; // Negative value

        // Act
        final result = await pricingService.calculateTotalPrice(
          material: material,
          cuttingDurationHours: cuttingDurationHours,
          gasType: gasType,
          thickness: thickness,
          isDesignFeesIncluded: isDesignFeesIncluded,
          customDesignCost: customDesignCost,
          customSetupFees: customSetupFees,
        );

        // Assert - Should handle negative values (business logic decision)
        expect(result.designCost, equals(-50.0));
        expect(result.setupFees, equals(-25.0));
      });
    });
  });
}
