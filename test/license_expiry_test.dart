import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';

void main() {
  group('License Expiry Date Tests', () {
    test('should create LicenseValidationResult with expiry date', () {
      final expiryDate = DateTime.now().add(const Duration(days: 100));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: DateTime.now(),
        expiryDate: expiryDate,
      );

      expect(result.expiryDate, equals(expiryDate));
      expect(result.isExpired, isFalse);
      expect(result.daysUntilExpiry, isNotNull);
    });

    test('should detect expired license', () {
      final expiredDate = DateTime.now().subtract(const Duration(days: 1));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: DateTime.now(),
        expiryDate: expiredDate,
      );

      expect(result.isExpired, isTrue);
      expect(result.daysUntilExpiry, isNegative);
      expect(result.isExpiringSoon, isFalse);
    });

    test('should detect license expiring soon', () {
      final now = DateTime.now();
      final soonDate = DateTime(now.year, now.month, now.day).add(const Duration(days: 15));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: now,
        expiryDate: soonDate,
      );

      expect(result.isExpired, isFalse);
      expect(result.isExpiringSoon, isTrue);
      expect(result.daysUntilExpiry, equals(15));
    });

    test('should handle license with no expiry date', () {
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: DateTime.now(),
        expiryDate: null,
      );

      expect(result.expiryDate, isNull);
      expect(result.isExpired, isFalse);
      expect(result.isExpiringSoon, isFalse);
      expect(result.daysUntilExpiry, isNull);
    });

    test('should handle license with distant expiry date', () {
      final now = DateTime.now();
      final distantDate = DateTime(now.year, now.month, now.day).add(const Duration(days: 365));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: now,
        expiryDate: distantDate,
      );

      expect(result.isExpired, isFalse);
      expect(result.isExpiringSoon, isFalse);
      expect(result.daysUntilExpiry, equals(365));
    });

    test('should parse expiry date from server response', () {
      // This test simulates the server response parsing
      final mockResponseData = {
        'Code': 'SUCCESS',
        'Message': 'License validated successfully',
        'ExpiryDate': '2024-12-31T23:59:59Z',
      };

      // We can't directly test the private method, but we can test the result
      // This would be tested through integration tests with actual server responses
      expect(mockResponseData['ExpiryDate'], isNotNull);
      expect(mockResponseData['ExpiryDate'], contains('2024-12-31'));
    });

    test('should handle different date formats in server response', () {
      final testDates = [
        '2024-12-31T23:59:59Z',      // ISO 8601 with timezone
        '2024-12-31T23:59:59',       // ISO 8601 without timezone
        '2024-12-31',                // Date only
      ];

      for (final dateString in testDates) {
        expect(dateString, isNotEmpty);
        expect(dateString, contains('2024-12-31'));
      }
    });
  });

  group('License Expiry Edge Cases', () {
    test('should handle expiry exactly at 30 days', () {
      final now = DateTime.now();
      final exactlyThirtyDays = DateTime(now.year, now.month, now.day).add(const Duration(days: 30));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: now,
        expiryDate: exactlyThirtyDays,
      );

      expect(result.isExpiringSoon, isTrue);
      expect(result.daysUntilExpiry, equals(30));
    });

    test('should handle expiry exactly at 31 days', () {
      final now = DateTime.now();
      final thirtyOneDays = DateTime(now.year, now.month, now.day).add(const Duration(days: 31));
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: now,
        expiryDate: thirtyOneDays,
      );

      expect(result.isExpiringSoon, isFalse);
      expect(result.daysUntilExpiry, equals(31));
    });

    test('should handle expiry today', () {
      final today = DateTime.now();
      final endOfToday = DateTime(today.year, today.month, today.day, 23, 59, 59);
      final result = LicenseValidationResult(
        isValid: true,
        responseCode: 'SUCCESS',
        message: 'License validated successfully',
        timestamp: today,
        expiryDate: endOfToday,
      );

      expect(result.daysUntilExpiry, equals(0));
      expect(result.isExpiringSoon, isTrue);
    });
  });
}
