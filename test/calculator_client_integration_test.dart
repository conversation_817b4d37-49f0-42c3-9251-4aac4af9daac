import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/calculator_provider.dart';
import 'package:laser_cutting_calculator/providers/client_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/models/job_config.dart';

void main() {
  group('Calculator Client Integration Tests', () {
    late CalculatorProvider calculatorProvider;
    late ClientProvider clientProvider;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      calculatorProvider = CalculatorProvider();
      clientProvider = ClientProvider();
    });

    tearDown(() async {
      await DatabaseService.instance.clearAllData();
    });

    test('should create new client when saving calculation with new client', () async {
      // Setup calculation configuration
      final config = JobConfig(
        clientName: 'Test Client',
        clientReference: 'TEST-001',
        material: 'acier',
        thickness: 5.0,
        gasType: 'Oxygène (O₂)',
        linearMeters: 10.0,
        cuttingSpeed: 2.0,
        includeDesignFees: true,
        isNewClient: true,
        designReference: 'DESIGN-001',
        setupFees: 0.0,
        designServicePrice: 0.0,
        cuttingDurationHours: 2.0,
        isDesignServicePriceCustomized: false,
      );

      // Update calculator with configuration
      calculatorProvider.updateConfig(config);

      // Calculate price (this should populate pricing breakdown)
      await Future.delayed(const Duration(milliseconds: 100)); // Allow calculation to complete

      // Verify we have a valid calculation
      expect(calculatorProvider.totalPrice, greaterThan(0));
      expect(calculatorProvider.pricingBreakdown, isNotNull);

      // Save calculation
      final saveResult = await calculatorProvider.saveCalculation();
      expect(saveResult, isTrue);

      // Load clients and verify new client was created
      await clientProvider.loadClients();
      
      expect(clientProvider.clients.length, equals(1));
      
      final client = clientProvider.clients.first;
      expect(client.name, equals('Test Client'));
      expect(client.reference, equals('TEST-001'));
      expect(client.projects, equals(1));
      expect(client.totalSpent, equals(calculatorProvider.totalPrice));
    });

    test('should update existing client when saving calculation with existing client', () async {
      // First, create a client manually
      await clientProvider.loadClients();
      
      // Setup first calculation
      final config1 = JobConfig(
        clientName: 'Existing Client',
        clientReference: 'EXIST-001',
        material: 'acier',
        thickness: 3.0,
        gasType: 'Oxygène (O₂)',
        linearMeters: 5.0,
        cuttingSpeed: 2.0,
        includeDesignFees: true,
        isNewClient: true,
        designReference: 'DESIGN-001',
        setupFees: 0.0,
        designServicePrice: 0.0,
        cuttingDurationHours: 1.0,
        isDesignServicePriceCustomized: false,
      );

      calculatorProvider.updateConfig(config1);
      await Future.delayed(const Duration(milliseconds: 100));
      
      final firstPrice = calculatorProvider.totalPrice;
      expect(firstPrice, greaterThan(0));
      
      // Save first calculation
      await calculatorProvider.saveCalculation();
      
      // Verify client was created
      await clientProvider.loadClients();
      expect(clientProvider.clients.length, equals(1));
      
      final clientAfterFirst = clientProvider.clients.first;
      expect(clientAfterFirst.projects, equals(1));
      expect(clientAfterFirst.totalSpent, equals(firstPrice));

      // Setup second calculation for same client
      final config2 = JobConfig(
        clientName: 'Existing Client',
        clientReference: 'EXIST-001',
        material: 'acier',
        thickness: 8.0,
        gasType: 'Oxygène (O₂)',
        linearMeters: 8.0,
        cuttingSpeed: 2.0,
        includeDesignFees: false,
        isNewClient: false,
        designReference: '',
        setupFees: 0.0,
        designServicePrice: 100.0,
        cuttingDurationHours: 2.0,
        isDesignServicePriceCustomized: false,
      );

      calculatorProvider.updateConfig(config2);
      await Future.delayed(const Duration(milliseconds: 100));
      
      final secondPrice = calculatorProvider.totalPrice;
      expect(secondPrice, greaterThan(0));
      
      // Save second calculation
      await calculatorProvider.saveCalculation();
      
      // Verify client was updated, not duplicated
      await clientProvider.loadClients();
      expect(clientProvider.clients.length, equals(1));
      
      final clientAfterSecond = clientProvider.clients.first;
      expect(clientAfterSecond.projects, equals(2));
      expect(clientAfterSecond.totalSpent, equals(firstPrice + secondPrice));
    });

    test('should handle calculation save failure gracefully', () async {
      // Setup invalid configuration (missing required fields)
      final invalidConfig = JobConfig(
        clientName: '', // Empty name should cause validation failure
        clientReference: '',
        material: 'acier',
        thickness: 5.0,
        gasType: 'Oxygène (O₂)',
        linearMeters: 10.0,
        cuttingSpeed: 2.0,
        includeDesignFees: true,
        isNewClient: true,
        designReference: '',
        setupFees: 0.0,
        designServicePrice: 0.0,
        cuttingDurationHours: 2.0,
        isDesignServicePriceCustomized: false,
      );

      calculatorProvider.updateConfig(invalidConfig);

      // Try to save invalid calculation
      final saveResult = await calculatorProvider.saveCalculation();
      expect(saveResult, isFalse);

      // Verify no client was created
      await clientProvider.loadClients();
      expect(clientProvider.clients.length, equals(0));
    });
  });
}
