import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Debug License Validation', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      // Clear shared preferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('debug license validation with detailed output', () async {
      // Using the serial number from the error message context
      const serialNumber = 'O4T1K-RW2I9-HDAZY-K98BQ';
      
      print('\n=== DEBUG LICENSE VALIDATION ===');
      print('Testing with serial number: $serialNumber');
      
      final result = await licenseService.validateLicense(
        serialNumber: serialNumber,
        timeout: const Duration(seconds: 10),
        enableDebug: true,
      );

      print('\n=== VALIDATION RESULT ===');
      print('Is Valid: ${result.isValid}');
      print('Response Code: ${result.responseCode}');
      print('Message: ${result.message}');
      print('HTTP Code: ${result.httpCode ?? 'N/A'}');
      print('Timestamp: ${result.timestamp}');
      
      if (result.serverResponse != null) {
        print('\n=== SERVER RESPONSE ===');
        result.serverResponse!.forEach((key, value) {
          print('$key: $value');
        });
      }

      // Test should not fail - we just want to see the debug output
      expect(result, isNotNull);
    });

    test('test RSA encryption compatibility', () async {
      const testPayload = 'TEST-SERIAL:ABCD1234';
      
      print('\n=== RSA ENCRYPTION TEST ===');
      print('Test payload: $testPayload');
      
      try {
        final service = LicenseValidationService.instance;
        
        // Test hardware ID generation
        final hwid = await service.generateHardwareId();
        print('Generated Hardware ID: $hwid');
        print('HWID length: ${hwid.length}');
        
        // Test RSA encryption
        final encryptedPayload = await service.testEncryption(testPayload);
        print('Encrypted payload: ${encryptedPayload.substring(0, math.min(100, encryptedPayload.length))}...');
        print('Encrypted length: ${encryptedPayload.length}');
        
        expect(hwid, isNotEmpty);
        expect(hwid.length, greaterThan(0));
        expect(encryptedPayload, isNotEmpty);
        expect(encryptedPayload, isNot(startsWith('ERROR:')));
      } catch (e) {
        print('Error during encryption test: $e');
        // Don't fail the test, just report the error
      }
    });
  });
}

// Remove the extension as we made methods public
