import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/python_equivalent_auth.dart';

void main() {
  group('Python Equivalent Authentication', () {
    test('test authentication function - exact Python equivalent', () async {
      print('\n=== PYTHON EQUIVALENT AUTHENTICATION TEST ===');
      
      // Parse the public key (equivalent to Python's serialization.load_pem_public_key)
      final publicKey = PythonEquivalentAuth.parsePublicKeyFromPem(
        PythonEquivalentAuth.publicKeyPem,
      );
      
      // Test with exact same values as Python
      const apiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
      const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const hwid = 'MEHDI';
      
      print('Testing with:');
      print('- API Key: $apiKey');
      print('- Serial: $serial');
      print('- HWID: $hwid');
      print('- Payload: $serial:$hwid');
      
      // Call the authentication function (equivalent to Python's authentication())
      final result = await PythonEquivalentAuth.authentication(
        publicKey,
        apiKey,
        serial,
        hwid,
      );
      
      print('Authentication result: $result');
      
      // The test doesn't assert success/failure since it depends on server availability
      // This is just to demonstrate the equivalent implementation
      expect(result, isA<bool>());
    });

    test('run main function - equivalent to Python if __name__ == "__main__"', () async {
      print('\n=== RUNNING MAIN FUNCTION ===');
      
      // This is equivalent to running the Python script directly
      await PythonEquivalentAuth.main();
      
      // Just verify it completes without throwing
      expect(true, isTrue);
    });

    test('compare payload generation with Python', () async {
      print('\n=== PAYLOAD COMPARISON ===');
      
      const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const hwid = 'MEHDI';
      const expectedPayload = '$serial:$hwid';
      
      print('Expected payload (Python): "$expectedPayload"');
      print('Payload length: ${expectedPayload.length} characters');
      
      // This matches Python: plaintexts = bytes(serial + ':' + hwid, 'utf-8')
      expect(expectedPayload, equals('O4T1K-RW2I9-HDAZY-K98BQ:MEHDI'));
    });
  });
}
