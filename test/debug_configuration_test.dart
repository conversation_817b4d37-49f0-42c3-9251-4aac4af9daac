import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Debug Configuration Tests', () {
    late ConfigurationProvider configProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      configProvider = ConfigurationProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('debug: step by step material addition', () async {
      print('=== Starting material addition test ===');
      
      // Step 1: Add a new material
      const materialKey = 'material_rate_titanium';
      const materialRate = '1200.0';
      
      print('Step 1: Adding material with key: $materialKey, rate: $materialRate');
      await configProvider.setConfigValue(materialKey, materialRate);
      print('Step 1: Material added to database');
      
      // Step 2: Retrieve the material
      print('Step 2: Retrieving material from database');
      final retrievedRate = await configProvider.getConfigValue(materialKey);
      print('Step 2: Retrieved rate: $retrievedRate');
      
      expect(retrievedRate, equals(materialRate));
      print('=== Material addition test completed successfully ===');
    });

    test('debug: step by step gas cost update', () async {
      print('=== Starting gas cost update test ===');
      
      // Step 1: Add a gas cost
      const gasKey = 'gas_cost_oxygen_1_5_mm';
      const gasCost = '55.0';
      
      print('Step 1: Adding gas cost with key: $gasKey, cost: $gasCost');
      await configProvider.setConfigValue(gasKey, gasCost);
      print('Step 1: Gas cost added to database');
      
      // Step 2: Retrieve the gas cost
      print('Step 2: Retrieving gas cost from database');
      final retrievedCost = await configProvider.getConfigValue(gasKey);
      print('Step 2: Retrieved cost: $retrievedCost');
      
      expect(retrievedCost, equals(gasCost));
      print('=== Gas cost update test completed successfully ===');
    });

    test('debug: simulate configuration screen workflow', () async {
      print('=== Starting configuration screen workflow simulation ===');
      
      // Simulate adding a new material like the configuration screen does
      const newMaterialName = 'Titanium';
      const newMaterialRate = '1200.0';
      
      print('Step 1: Simulating material addition');
      print('Material name: $newMaterialName');
      print('Material rate: $newMaterialRate');
      
      // Create key like the configuration screen does
      final key = newMaterialName.toLowerCase().replaceAll(' ', '-');
      print('Generated key: $key');
      
      // Save to database like _addMaterial does
      await configProvider.setConfigValue('material_rate_$key', newMaterialRate);
      print('Step 1: Saved to database with key: material_rate_$key');
      
      // Retrieve to verify
      final retrievedValue = await configProvider.getConfigValue('material_rate_$key');
      print('Step 1: Retrieved value: $retrievedValue');
      
      expect(retrievedValue, equals(newMaterialRate));
      
      // Simulate updating the material rate
      const updatedRate = '1300.0';
      print('Step 2: Simulating material rate update to: $updatedRate');
      
      await configProvider.setConfigValue('material_rate_$key', updatedRate);
      print('Step 2: Updated in database');
      
      final updatedValue = await configProvider.getConfigValue('material_rate_$key');
      print('Step 2: Retrieved updated value: $updatedValue');
      
      expect(updatedValue, equals(updatedRate));
      print('=== Configuration screen workflow simulation completed successfully ===');
    });

    test('debug: simulate gas type addition workflow', () async {
      print('=== Starting gas type addition workflow simulation ===');
      
      const gasName = 'Argon (Ar)';
      print('Gas name: $gasName');
      
      // Create key like the configuration screen does
      final gasKey = gasName.toLowerCase().replaceAll(' ', '_').replaceAll('(', '').replaceAll(')', '').replaceAll('₂', '2');
      print('Generated gas key: $gasKey');
      
      // Add all thickness ranges like _addGasType does
      final ranges = {
        'gas_cost_${gasKey}_1_5_mm': '60.0',
        'gas_cost_${gasKey}_5_10_mm': '90.0',
        'gas_cost_${gasKey}_10_15_mm': '120.0',
        'gas_cost_${gasKey}_gt_15_mm': '180.0',
      };
      
      print('Adding gas ranges:');
      for (final entry in ranges.entries) {
        print('  ${entry.key}: ${entry.value}');
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      print('Verifying gas ranges:');
      for (final entry in ranges.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        print('  ${entry.key}: $retrievedValue');
        expect(retrievedValue, equals(entry.value));
      }
      
      // Simulate updating a gas cost
      const updatedCost = '65.0';
      final rangeToUpdate = 'gas_cost_${gasKey}_1_5_mm';
      print('Updating $rangeToUpdate to $updatedCost');
      
      await configProvider.setConfigValue(rangeToUpdate, updatedCost);
      final updatedValue = await configProvider.getConfigValue(rangeToUpdate);
      print('Updated value: $updatedValue');
      
      expect(updatedValue, equals(updatedCost));
      print('=== Gas type addition workflow simulation completed successfully ===');
    });

    test('debug: check if configuration persists', () async {
      print('=== Starting configuration persistence test ===');
      
      // Add some configuration
      await configProvider.setConfigValue('material_rate_test', '500.0');
      await configProvider.setConfigValue('gas_cost_test_1_5_mm', '40.0');
      
      // Create new provider instance to simulate app restart
      final newProvider = ConfigurationProvider();
      
      // Check if data persists
      final materialValue = await newProvider.getConfigValue('material_rate_test');
      final gasValue = await newProvider.getConfigValue('gas_cost_test_1_5_mm');
      
      print('Material value after restart: $materialValue');
      print('Gas value after restart: $gasValue');
      
      expect(materialValue, equals('500.0'));
      expect(gasValue, equals('40.0'));
      
      print('=== Configuration persistence test completed successfully ===');
    });
  });
}
