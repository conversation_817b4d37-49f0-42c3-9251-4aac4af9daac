import 'package:flutter_test/flutter_test.dart';
import 'package:drift/drift.dart' hide isNull, isNotNull;
import 'package:laser_cutting_calculator/database/database.dart';
import 'package:laser_cutting_calculator/database/daos.dart';
import 'package:laser_cutting_calculator/database/tables.dart';
import 'package:laser_cutting_calculator/database/usage_examples.dart';

void main() {
  group('Database Normalization Tests', () {
    late AppDatabase database;
    late MaterialsDao materialsDao;
    late GasTypesDao gasTypesDao;
    late ThicknessRangesDao thicknessRangesDao;
    late GasCostsDao gasCostsDao;
    late AppSettingsDao appSettingsDao;
    late DatabaseUsageExamples examples;

    setUp(() async {
      // Create test database
      database = AppDatabase.forTesting();
      
      // Initialize DAOs
      materialsDao = MaterialsDao(database);
      gasTypesDao = GasTypesDao(database);
      thicknessRangesDao = ThicknessRangesDao(database);
      gasCostsDao = GasCostsDao(database);
      appSettingsDao = AppSettingsDao(database);
      examples = DatabaseUsageExamples(database);
    });

    tearDown(() async {
      await database.close();
    });

    test('should create normalized tables on fresh installation', () async {
      // Verify tables exist and are empty initially
      final materials = await materialsDao.getAllMaterials();
      final gasTypes = await gasTypesDao.getAllGasTypes();
      final thicknessRanges = await thicknessRangesDao.getAllThicknessRanges();
      final gasCosts = await gasCostsDao.getAllGasCostsWithDetails();
      final settings = await appSettingsDao.getAllSettings();

      // Should have seeded data from onCreate
      expect(materials.isNotEmpty, isTrue);
      expect(gasTypes.isNotEmpty, isTrue);
      expect(thicknessRanges.isNotEmpty, isTrue);
      expect(gasCosts.isNotEmpty, isTrue);
      expect(settings.isNotEmpty, isTrue);

      print('✅ Normalized tables created and seeded successfully');
    });

    test('should have correct material data', () async {
      final materials = await materialsDao.getAllMaterials();
      
      // Check that default materials exist
      final materialNames = materials.map((m) => m.name).toSet();
      expect(materialNames.contains('acier'), isTrue);
      expect(materialNames.contains('inox'), isTrue);
      expect(materialNames.contains('cuivre'), isTrue);
      expect(materialNames.contains('aluminum'), isTrue);
      expect(materialNames.contains('tole_galvanisee'), isTrue);

      // Check default material
      final defaultMaterial = await materialsDao.getDefaultMaterial();
      expect(defaultMaterial, isNotNull);
      expect(defaultMaterial!.name, equals('acier'));

      // Check material rates
      final rates = await materialsDao.getMaterialRates();
      expect(rates['acier'], equals(25.0));
      expect(rates['inox'], equals(35.0));

      print('✅ Material data is correct');
    });

    test('should have correct gas types and costs', () async {
      final gasTypes = await gasTypesDao.getAllGasTypes();
      
      // Check that default gas types exist
      final gasTypeNames = gasTypes.map((g) => g.name).toSet();
      expect(gasTypeNames.contains('oxygene_o2'), isTrue);
      expect(gasTypeNames.contains('azote_n2'), isTrue);
      expect(gasTypeNames.contains('air_comprime'), isTrue);

      // Check default gas type
      final defaultGasType = await gasTypesDao.getDefaultGasType();
      expect(defaultGasType, isNotNull);
      expect(defaultGasType!.name, equals('oxygene_o2'));

      // Check gas costs
      final oxygenCost = await gasCostsDao.getGasCostForThickness('oxygene_o2', 3.0);
      expect(oxygenCost, equals(8.0)); // Should use 1-5mm range

      final nitrogenCost = await gasCostsDao.getGasCostForThickness('azote_n2', 7.0);
      expect(nitrogenCost, equals(9.0)); // Should use 5-10mm range

      print('✅ Gas types and costs are correct');
    });

    test('should have correct thickness ranges', () async {
      final ranges = await thicknessRangesDao.getAllThicknessRanges();
      
      expect(ranges.length, equals(4));
      
      // Check specific ranges
      final range1_5 = ranges.firstWhere((r) => r.name == '1_5_mm');
      expect(range1_5.minThickness, equals(1.0));
      expect(range1_5.maxThickness, equals(5.0));
      expect(range1_5.displayName, equals('1-5 mm'));

      final rangeGt15 = ranges.firstWhere((r) => r.name == 'gt_15_mm');
      expect(rangeGt15.minThickness, equals(15.0));
      expect(rangeGt15.maxThickness, isNull); // Open-ended range

      // Test thickness range finding
      final rangeFor3mm = await thicknessRangesDao.getThicknessRangeForValue(3.0);
      expect(rangeFor3mm?.name, equals('1_5_mm'));

      final rangeFor20mm = await thicknessRangesDao.getThicknessRangeForValue(20.0);
      expect(rangeFor20mm?.name, equals('gt_15_mm'));

      print('✅ Thickness ranges are correct');
    });

    test('should have correct app settings', () async {
      // Test pricing settings
      final designPrice = await appSettingsDao.getDesignServicePrice();
      expect(designPrice, equals(50.0));

      final pricePerMeter = await appSettingsDao.getPricePerMeter();
      expect(pricePerMeter, equals(15.0));

      final taxRate = await appSettingsDao.getTaxRate();
      expect(taxRate, equals(20.0));

      // Test app configuration
      final currency = await appSettingsDao.getCurrency();
      expect(currency, equals('EUR'));

      final appVersion = await appSettingsDao.getAppVersion();
      expect(appVersion, equals('1.0.0'));

      print('✅ App settings are correct');
    });

    test('should calculate cutting costs correctly', () async {
      final costs = await examples.calculateCuttingCost(
        materialName: 'acier',
        gasTypeName: 'oxygene_o2',
        thickness: 5.0,
        linearMeters: 10.0,
        cuttingSpeed: 100.0,
        designProvided: true,
      );

      expect(costs['material_cost'], isNotNull);
      expect(costs['gas_cost'], isNotNull);
      expect(costs['base_cost'], isNotNull);
      expect(costs['design_cost'], equals(50.0)); // Design service price
      expect(costs['subtotal'], isNotNull);
      expect(costs['tax_amount'], isNotNull);
      expect(costs['total'], isNotNull);

      // Verify calculation logic
      final expectedBaseCost = 15.0 * 10.0; // price_per_meter * linear_meters
      expect(costs['base_cost'], equals(expectedBaseCost));

      final expectedGasCost = 12.0 * 10.0; // gas cost for 5mm thickness (5-10mm range) * linear_meters
      expect(costs['gas_cost'], equals(expectedGasCost));

      print('✅ Cost calculation is correct');
      print('Cost breakdown: $costs');
    });

    test('should support CRUD operations', () async {
      // Test adding a new material
      final newMaterialId = await materialsDao.insertMaterial(MaterialsCompanion(
        name: const Value('test_material'),
        displayName: const Value('Test Material'),
        rate: const Value(40.0),
        isDefault: const Value(false),
      ));

      expect(newMaterialId, greaterThan(0));

      // Test retrieving the new material
      final newMaterial = await materialsDao.getMaterialById(newMaterialId);
      expect(newMaterial, isNotNull);
      expect(newMaterial!.name, equals('test_material'));
      expect(newMaterial.rate, equals(40.0));

      // Test updating the material
      final updateSuccess = await materialsDao.updateMaterial(
        newMaterialId,
        const MaterialsCompanion(rate: Value(45.0)),
      );
      expect(updateSuccess, isTrue);

      final updatedMaterial = await materialsDao.getMaterialById(newMaterialId);
      expect(updatedMaterial!.rate, equals(45.0));

      // Test deleting the material
      final deleteCount = await materialsDao.deleteMaterial(newMaterialId);
      expect(deleteCount, equals(1));

      final deletedMaterial = await materialsDao.getMaterialById(newMaterialId);
      expect(deletedMaterial, isNull);

      print('✅ CRUD operations work correctly');
    });

    test('should support bulk operations', () async {
      // Test bulk gas cost update
      await examples.bulkUpdateGasCosts('oxygene_o2', {
        '1_5_mm': 10.0,
        '5_10_mm': 15.0,
        '10_15_mm': 20.0,
        'gt_15_mm': 30.0,
      });

      // Verify updates
      final cost1_5 = await gasCostsDao.getGasCostForThickness('oxygene_o2', 3.0);
      expect(cost1_5, equals(10.0));

      final cost5_10 = await gasCostsDao.getGasCostForThickness('oxygene_o2', 7.0);
      expect(cost5_10, equals(15.0));

      print('✅ Bulk operations work correctly');
    });

    test('should generate configuration summary', () async {
      final summary = await examples.getConfigurationSummary();

      expect(summary['materials'], isNotNull);
      expect(summary['gas_types'], isNotNull);
      expect(summary['gas_costs'], isNotNull);
      expect(summary['pricing_settings'], isNotNull);
      expect(summary['app_configuration'], isNotNull);

      final materials = summary['materials'] as List;
      expect(materials.isNotEmpty, isTrue);

      final gasTypes = summary['gas_types'] as List;
      expect(gasTypes.isNotEmpty, isTrue);

      print('✅ Configuration summary generated correctly');
      print('Summary: ${summary.keys}');
    });
  });
}
