import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Configuration Add Material Tests', () {
    late ConfigurationProvider configProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      configProvider = ConfigurationProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should save and retrieve new material rates', () async {
      // Add a new material
      const materialKey = 'material_rate_titanium';
      const materialRate = '1200.0';
      
      await configProvider.setConfigValue(materialKey, materialRate);
      final retrievedRate = await configProvider.getConfigValue(materialKey);
      
      expect(retrievedRate, equals(materialRate));
    });

    test('should save and retrieve multiple new materials', () async {
      final newMaterials = {
        'material_rate_titanium': '1200.0',
        'material_rate_brass': '850.0',
        'material_rate_carbon_steel': '650.0',
        'material_rate_stainless_steel_316': '950.0',
      };
      
      // Save all new materials
      for (final entry in newMaterials.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Retrieve and verify all new materials
      for (final entry in newMaterials.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        expect(retrievedValue, equals(entry.value));
      }
    });

    test('should save and retrieve new gas types with all thickness ranges', () async {
      const gasType = 'argon';
      final gasRanges = {
        'gas_cost_argon_1_5_mm': '60.0',
        'gas_cost_argon_5_10_mm': '90.0',
        'gas_cost_argon_10_15_mm': '120.0',
        'gas_cost_argon_gt_15_mm': '180.0',
      };
      
      // Save all gas ranges
      for (final entry in gasRanges.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Retrieve and verify all gas ranges
      for (final entry in gasRanges.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        expect(retrievedValue, equals(entry.value));
      }
    });

    test('should handle material removal by setting empty value', () async {
      const materialKey = 'material_rate_test_material';
      const materialRate = '500.0';
      
      // Add material
      await configProvider.setConfigValue(materialKey, materialRate);
      final addedValue = await configProvider.getConfigValue(materialKey);
      expect(addedValue, equals(materialRate));
      
      // Remove material (set empty value)
      await configProvider.setConfigValue(materialKey, '');
      final removedValue = await configProvider.getConfigValue(materialKey);
      expect(removedValue, equals(''));
    });

    test('should handle gas type removal by setting empty values', () async {
      const gasType = 'test_gas';
      final gasRanges = {
        'gas_cost_test_gas_1_5_mm': '40.0',
        'gas_cost_test_gas_5_10_mm': '60.0',
        'gas_cost_test_gas_10_15_mm': '80.0',
        'gas_cost_test_gas_gt_15_mm': '120.0',
      };
      
      // Add gas type
      for (final entry in gasRanges.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Verify gas type was added
      for (final entry in gasRanges.entries) {
        final addedValue = await configProvider.getConfigValue(entry.key);
        expect(addedValue, equals(entry.value));
      }
      
      // Remove gas type (set empty values)
      for (final key in gasRanges.keys) {
        await configProvider.setConfigValue(key, '');
      }
      
      // Verify gas type was removed
      for (final key in gasRanges.keys) {
        final removedValue = await configProvider.getConfigValue(key);
        expect(removedValue, equals(''));
      }
    });

    test('should update existing material rates', () async {
      const materialKey = 'material_rate_aluminum';
      const initialRate = '700.0';
      const updatedRate = '750.0';
      
      // Set initial rate
      await configProvider.setConfigValue(materialKey, initialRate);
      final firstValue = await configProvider.getConfigValue(materialKey);
      expect(firstValue, equals(initialRate));
      
      // Update rate
      await configProvider.setConfigValue(materialKey, updatedRate);
      final secondValue = await configProvider.getConfigValue(materialKey);
      expect(secondValue, equals(updatedRate));
    });

    test('should update existing gas costs', () async {
      const gasKey = 'gas_cost_nitrogen_5_10_mm';
      const initialCost = '120.0';
      const updatedCost = '130.0';
      
      // Set initial cost
      await configProvider.setConfigValue(gasKey, initialCost);
      final firstValue = await configProvider.getConfigValue(gasKey);
      expect(firstValue, equals(initialCost));
      
      // Update cost
      await configProvider.setConfigValue(gasKey, updatedCost);
      final secondValue = await configProvider.getConfigValue(gasKey);
      expect(secondValue, equals(updatedCost));
    });

    test('should handle special characters in gas type names', () async {
      // Test with special characters like parentheses and subscripts
      const gasTypeName = 'Argon (Ar)';
      const normalizedKey = 'argon_ar'; // Should be normalized
      
      final gasRanges = {
        'gas_cost_${normalizedKey}_1_5_mm': '65.0',
        'gas_cost_${normalizedKey}_5_10_mm': '95.0',
        'gas_cost_${normalizedKey}_10_15_mm': '125.0',
        'gas_cost_${normalizedKey}_gt_15_mm': '185.0',
      };
      
      // Save all gas ranges
      for (final entry in gasRanges.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Retrieve and verify all gas ranges
      for (final entry in gasRanges.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        expect(retrievedValue, equals(entry.value));
      }
    });

    test('should persist configuration across provider instances', () async {
      const materialKey = 'material_rate_persistent_test';
      const materialRate = '999.0';
      
      // Save with first provider instance
      await configProvider.setConfigValue(materialKey, materialRate);
      
      // Create new provider instance
      final newConfigProvider = ConfigurationProvider();
      
      // Retrieve with new provider instance
      final retrievedValue = await newConfigProvider.getConfigValue(materialKey);
      expect(retrievedValue, equals(materialRate));
    });
  });
}
