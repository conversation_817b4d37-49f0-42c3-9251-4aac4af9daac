import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:flutter_test/flutter_test.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

class LicenseAuthenticator {
  static Future<bool> authenticate({
    required String publicKeyPem,
    required String apiKey,
    required String serial,
    required String hwid,
  }) async {
    try {
      // Step 1: Construct plaintext = "serial:hwid"
      final plaintext = '$serial:$hwid';
      final plaintextBytes = utf8.encode(plaintext);

      print('Debug: Plaintext = "$plaintext"');
      print('Debug: Plaintext bytes = $plaintextBytes');

      // Step 2: Parse PEM public key
      final rsaPublicKey = _parseRsaPublicKeyFromPem(publicKeyPem);

      // Step 3: Encrypt with RSA-OAEP-SHA256
      // NOTE: Let's see what OAEP options are available in pointycastle
      final encryptedBytes = await _encryptWithOAEPSHA256(plaintextBytes, rsaPublicKey);

      // Step 4: Base64 encode the encrypted payload
      final encodedPayload = base64Encode(encryptedBytes);

      print('Debug: Encrypted payload = $encodedPayload');
      print('Debug: Encrypted length = ${encryptedBytes.length} bytes');

      // Step 5: Send POST request
      final url = Uri.parse('http://localhost:8000/api/v1/validate');
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'apiKey': apiKey,
          'payload': encodedPayload,
        }),
      ).timeout(const Duration(seconds: 10));

      print('Debug: HTTP Status = ${response.statusCode}');
      print('Debug: HTTP Response = ${response.body}');

      // Step 6: Parse response
      if (response.statusCode != 200) {
        print('Error: HTTP ${response.statusCode}');
        return false;
      }

      final responseBody = jsonDecode(response.body);
      final String? responseCode = responseBody['Code'] as String?;

      if (responseCode == null) {
        print('Error: Missing "Code" in response');
        return false;
      }

      final successCodes = ['OKAY', 'SUCCESS'];
      if (successCodes.contains(responseCode)) {
        print('Authentication: $responseCode');
        return true;
      } else {
        print('Error: $responseCode');
        return false;
      }
    } catch (e) {
      print('Exception during authentication: $e');
      return false;
    }
  }

  // Helper: Encrypt with OAEP SHA256 (attempt different approaches)
  static Future<Uint8List> _encryptWithOAEPSHA256(Uint8List data, RSAPublicKey publicKey) async {
    print('Debug: Attempting OAEP SHA256 encryption...');
    
    // Approach 1: Try to use OAEP with explicit SHA256
    try {
      // The classes you mentioned might not exist, let's try alternatives
      // For now, use the standard OAEP with default settings
      final cipher = OAEPEncoding(RSAEngine());
      cipher.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
      
      final encrypted = cipher.process(data);
      print('Debug: OAEP encryption successful with ${encrypted.length} bytes');
      print('Debug: Note - this might be using SHA1 instead of SHA256');
      return encrypted;
      
    } catch (e) {
      print('Debug: OAEP encryption failed: $e');
      rethrow;
    }
  }

  // Helper: Parse PEM RSA Public Key using basic_utils
  static RSAPublicKey _parseRsaPublicKeyFromPem(String pem) {
    try {
      // Use basic_utils to parse the RSA public key
      final rsaPublicKey = CryptoUtils.rsaPublicKeyFromPem(pem);
      print('Debug: RSA key parsed - modulus bits: ${rsaPublicKey.modulus!.bitLength}');
      return rsaPublicKey;
    } catch (e) {
      print('Debug: RSA key parsing failed: $e');
      rethrow;
    }
  }
}

void main() {
  group('New RSA Approach Test', () {
    test('test new license authenticator approach', () async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      const pubKey = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

      const apiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
      const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const hwid = 'MEHDI';

      print('=== Testing New RSA Approach ===');
      print('Serial: $serial');
      print('HWID: $hwid');

      try {
        final isAuthenticated = await LicenseAuthenticator.authenticate(
          publicKeyPem: pubKey,
          apiKey: apiKey,
          serial: serial,
          hwid: hwid,
        );

        print('Final result: Authenticated = $isAuthenticated');
        
        // Don't fail the test if authentication fails - we want to see the debug output
        expect(isAuthenticated, isA<bool>());
        
      } catch (e) {
        print('Test caught exception: $e');
        // Print exception but don't fail test so we can see debug output
        expect(e, isNotNull);
      }
    });
  });
}
