import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  group('License Validation HTTP Test', () {
    test('Test HTTP request format with current encryption', () async {
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // Mock the encryption and HTTP call manually to avoid shared_preferences
      const serial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const hwid = 'MEHDI';
      
      print('Testing HTTP request format with:');
      print('Serial: $serial');
      print('HWID: $hwid');
      
      try {
        // We'll manually call the HTTP endpoint to see what the server responds
        final url = Uri.parse('http://localhost:8000/api/v1/validate');
        const apiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
        
        // First test - try without encryption to see server response
        final payload = {
          'serial': serial,
          'hwid': hwid,
        };
        
        final response = await http.post(
          url,
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': apiKey,
          },
          body: json.encode({
            'data': base64Encode(utf8.encode(json.encode(payload))), // Plain base64, not RSA encrypted
          }),
        ).timeout(const Duration(seconds: 5));
        
        print('HTTP Response Status: ${response.statusCode}');
        print('HTTP Response Headers: ${response.headers}');
        print('HTTP Response Body: ${response.body}');
        
        // Now let's see what happens with the response
        if (response.statusCode == 400 || response.statusCode == 401) {
          print('\\nServer rejected the request. This suggests either:');
          print('1. The data must be RSA encrypted (not just base64 encoded)');
          print('2. The RSA encryption is using the wrong padding/algorithm');
          print('3. The API key or request format is incorrect');
        }
        
      } catch (e) {
        print('HTTP request failed: $e');
        
        if (e.toString().contains('Connection refused') || e.toString().contains('Failed host lookup')) {
          print('\\nServer appears to be down or not accessible');
          print('Make sure the Python license server is running on localhost:8000');
        }
      }
    });
  });
}
