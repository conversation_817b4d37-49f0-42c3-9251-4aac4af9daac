import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/main.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Widget Tests', () {
    late DatabaseService databaseService;

    setUpAll(() {
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      // Clean up database after each test
      await databaseService.clearAllData();
    });

    testWidgets('App should initialize and show main screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(MyApp(databaseService: databaseService));

      // Wait for initialization to complete
      await tester.pumpAndSettle();

      // Verify that the app title is displayed
      expect(find.text('Calculateur de Coût Découpe Laser'), findsOneWidget);

      // Verify that the sidebar is present
      expect(find.text('Calculateur'), findsOneWidget);
      expect(find.text('Clients'), findsOneWidget);
      expect(find.text('Historique'), findsOneWidget);
      expect(find.text('Configuration'), findsOneWidget);
    });

    testWidgets('Should navigate between screens', (WidgetTester tester) async {
      await tester.pumpWidget(MyApp(databaseService: databaseService));
      await tester.pumpAndSettle();

      // Tap on Clients in sidebar
      await tester.tap(find.text('Clients'));
      await tester.pumpAndSettle();

      // Should show client management screen
      expect(find.text('Gestion des Clients'), findsOneWidget);

      // Tap on History in sidebar
      await tester.tap(find.text('Historique'));
      await tester.pumpAndSettle();

      // Should show calculation history screen
      expect(find.text('Historique des Calculs'), findsOneWidget);
    });

    testWidgets('Should show loading screen during initialization', (WidgetTester tester) async {
      await tester.pumpWidget(MyApp(databaseService: databaseService));

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Initializing application...'), findsOneWidget);

      // Wait for initialization to complete
      await tester.pumpAndSettle();

      // Loading should be gone
      expect(find.byType(CircularProgressIndicator), findsNothing);
      expect(find.text('Initializing application...'), findsNothing);
    });
  });
}
