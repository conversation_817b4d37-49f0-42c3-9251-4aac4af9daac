import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:laser_cutting_calculator/screens/configuration_screen.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Configuration UI Integration Tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    tearDown(() async {
      final databaseService = DatabaseService.instance;
      await databaseService.clearAllData();
    });

    testWidgets('should display and update material rates correctly', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      // Build the configuration screen
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      // Wait for loading to complete
      await tester.pumpAndSettle();

      // Find material rate text fields
      final acierField = find.widgetWithText(TextFormField, '600.0');
      expect(acierField, findsOneWidget);

      // Test updating a material rate
      await tester.enterText(acierField, '650.0');
      await tester.pump();

      // Verify the value was updated in the provider
      final savedValue = await configProvider.getConfigValue('material_rate_acier');
      expect(savedValue, equals('650.0'));
    });

    testWidgets('should add new materials correctly', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the new material name field
      final nameField = find.widgetWithText(TextField, '').first;
      await tester.enterText(nameField, 'Titanium');
      await tester.pump();

      // Find the new material rate field
      final rateField = find.widgetWithText(TextField, '').last;
      await tester.enterText(rateField, '1200.0');
      await tester.pump();

      // Find and tap the add button
      final addButton = find.byIcon(Icons.add).first;
      await tester.tap(addButton);
      await tester.pumpAndSettle();

      // Verify the material was added to the database
      final savedValue = await configProvider.getConfigValue('material_rate_titanium');
      expect(savedValue, equals('1200.0'));

      // Verify success message is shown
      expect(find.text('Matériau "Titanium" ajouté avec succès!'), findsOneWidget);
    });

    testWidgets('should display and update gas costs correctly', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find gas cost text fields (there should be multiple)
      final gasCostFields = find.widgetWithText(TextFormField, '50.0');
      expect(gasCostFields, findsWidgets);

      // Test updating a gas cost
      await tester.enterText(gasCostFields.first, '55.0');
      await tester.pump();

      // Verify the value was updated in the provider
      final savedValue = await configProvider.getConfigValue('gas_cost_oxygene_o2_1_5_mm');
      expect(savedValue, equals('55.0'));
    });

    testWidgets('should add new gas types correctly', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the new gas name field (should be at the bottom)
      final gasNameField = find.widgetWithText(TextField, '').last;
      await tester.enterText(gasNameField, 'Argon');
      await tester.pump();

      // Find and tap the add gas button
      final addGasButton = find.byIcon(Icons.add).last;
      await tester.tap(addGasButton);
      await tester.pumpAndSettle();

      // Verify the gas type was added to the database
      final savedValue1 = await configProvider.getConfigValue('gas_cost_argon_1_5_mm');
      final savedValue2 = await configProvider.getConfigValue('gas_cost_argon_5_10_mm');
      final savedValue3 = await configProvider.getConfigValue('gas_cost_argon_10_15_mm');
      final savedValue4 = await configProvider.getConfigValue('gas_cost_argon_gt_15_mm');
      
      expect(savedValue1, equals('0.0'));
      expect(savedValue2, equals('0.0'));
      expect(savedValue3, equals('0.0'));
      expect(savedValue4, equals('0.0'));

      // Verify success message is shown
      expect(find.text('Type de gaz "Argon" ajouté avec succès!'), findsOneWidget);
    });

    testWidgets('should update setup fee correctly', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the setup fee field
      final setupFeeField = find.widgetWithText(TextFormField, '50.0').first;
      expect(setupFeeField, findsOneWidget);

      // Update the setup fee
      await tester.enterText(setupFeeField, '75.0');
      await tester.pump();

      // Verify the value was updated in the provider
      final savedValue = await configProvider.getDesignServicePrice();
      expect(savedValue, equals(75.0));
    });

    testWidgets('should save all configuration when save button is pressed', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Update some values
      final acierField = find.widgetWithText(TextFormField, '600.0');
      await tester.enterText(acierField, '625.0');
      await tester.pump();

      // Find and tap the save button
      final saveButton = find.widgetWithText(ElevatedButton, 'Sauvegarder');
      expect(saveButton, findsOneWidget);
      
      await tester.tap(saveButton);
      await tester.pumpAndSettle();

      // Verify success message is shown
      expect(find.text('Configuration sauvegardée avec succès!'), findsOneWidget);

      // Verify the configuration was saved
      final savedValue = await configProvider.getConfigValue('material_rate_acier');
      expect(savedValue, equals('625.0'));
    });

    testWidgets('should persist configuration across widget rebuilds', (WidgetTester tester) async {
      final configProvider = ConfigurationProvider();
      
      // First, save some configuration
      await configProvider.setConfigValue('material_rate_test', '999.0');
      await configProvider.setDesignServicePrice(88.0);
      
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<ConfigurationProvider>(
            create: (_) => configProvider,
            child: const ConfigurationScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify the saved values are displayed
      expect(find.text('88.0'), findsOneWidget); // Setup fee should be loaded
      
      // The test material won't be displayed since it's not in the default materials,
      // but we can verify it's in the database
      final savedValue = await configProvider.getConfigValue('material_rate_test');
      expect(savedValue, equals('999.0'));
    });
  });
}
