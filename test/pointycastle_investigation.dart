import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

void main() {
  group('PointyCastle RSA Investigation', () {
    test('Investigate RSA cipher options', () {
      const String publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

      final publicKey = CryptoUtils.rsaPublicKeyFromPem(publicKeyPem);
      
      print('=== Available Digest Algorithms ===');
      try {
        final sha1 = SHA1Digest();
        final sha256 = SHA256Digest();
        final md5 = MD5Digest();
        
        print('SHA1: ${sha1.algorithmName} (digest length: ${sha1.digestSize})');
        print('SHA256: ${sha256.algorithmName} (digest length: ${sha256.digestSize})');
        print('MD5: ${md5.algorithmName} (digest length: ${md5.digestSize})');
      } catch (e) {
        print('Error exploring digests: $e');
      }

      print('\\n=== RSA Engine Info ===');
      try {
        final rsa = RSAEngine();
        print('Algorithm: ${rsa.algorithmName}');
        print('Input block size: ${rsa.inputBlockSize}');
        print('Output block size: ${rsa.outputBlockSize}');
      } catch (e) {
        print('Error exploring RSA engine: $e');
      }

      print('\\n=== OAEPEncoding Constructor Analysis ===');
      try {
        // Try basic constructor
        final oaep1 = OAEPEncoding(RSAEngine());
        print('✓ OAEPEncoding(RSAEngine()) works');
        
        // Try with null label
        final oaep2 = OAEPEncoding(RSAEngine(), null);
        print('✓ OAEPEncoding(RSAEngine(), null) works');
        
        // Try with empty label
        final oaep3 = OAEPEncoding(RSAEngine(), Uint8List(0));
        print('✓ OAEPEncoding(RSAEngine(), Uint8List(0)) works');
        
        // Try with custom label
        final oaep4 = OAEPEncoding(RSAEngine(), Uint8List.fromList(utf8.encode('CUSTOM_LABEL')));
        print('✓ OAEPEncoding(RSAEngine(), custom_label) works');
        
        print('\\nOAEPEncoding appears to only take (cipher, label) parameters');
        
      } catch (e) {
        print('OAEP constructor analysis failed: $e');
      }

      print('\\n=== Checking for MGF and other OAEP components ===');
      try {
        // Let's see what's available in the MGF space
        // In cryptography, MGF1 is the standard mask generation function
        // Maybe it's named differently in pointycastle
        
        print('Looking for MGF-related classes...');
        
        // Try to find if there are any MGF classes by trying different names
        final digests = [
          'MGF1',
          'MGF1Digest', 
          'MaskGenerationFunction1',
          'MGF',
        ];
        
        for (final name in digests) {
          print('Checking for class: $name - not found in imports');
        }
        
      } catch (e) {
        print('MGF exploration failed: $e');
      }

      print('\\n=== Encryption Test with Current OAEP ===');
      try {
        final testPayload = json.encode({
          'serial': 'O4T1K-RW2I9-HDAZY-K98BQ',
          'hwid': 'MEHDI',
        });
        
        final payloadBytes = Uint8List.fromList(utf8.encode(testPayload));
        
        // Try default OAEP
        final cipher = OAEPEncoding(RSAEngine());
        cipher.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        final encrypted = cipher.process(payloadBytes);
        
        print('Payload: $testPayload');
        print('Payload bytes: ${payloadBytes.length}');
        print('Encrypted bytes: ${encrypted.length}');
        print('Encrypted (base64): ${base64Encode(encrypted)}');
        
        // The real question: does this produce SHA-1 or SHA-256 OAEP?
        print('\\nIMPORTANT: This uses the default OAEP settings in pointycastle');
        print('Need to determine if this is SHA-1 or SHA-256 compatible');
        
      } catch (e) {
        print('Encryption test failed: $e');
      }
    });
  });
}
