import 'dart:math' as math;
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('License Validation with Real Server', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      SharedPreferences.setMockInitialValues({});
    });

    test('test with working Python client equivalent', () async {
      /*
      TODO: Fill in these values from your working Python server:
      
      From your Python code:
      - PUB_KEY = """""" (put your actual RSA public key here)
      - API_KEY = '' (put your actual API key here)
      - SERIAL = '' (put a valid license serial number here)
      - HWID = '' (put the hardware ID that works with this serial)
      
      These values should match exactly what works in your Python client.
      */
      
      // TEMPORARY TEST VALUES - REPLACE WITH YOUR ACTUAL VALUES
      const testSerial = 'O4T1K-RW2I9-HDAZY-K98BQ';
      const expectedApiKey = 'a080015b-b827-48f8-a96d-dc3ccc650bc8';
      const expectedPublicKey = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';
      
      print('\n=== TESTING WITH ACTUAL SERVER ===');
      print('⚠️  WARNING: You need to update the test values above!');
      print('');
      print('Current test values:');
      print('- Serial: $testSerial');
      print('- API Key: $expectedApiKey');
      print('- Public Key: ${expectedPublicKey.replaceAll('\n', '\\n')}');
      print('');
      
      // Generate hardware ID like our Dart implementation does
      final dartHwid = await licenseService.generateHardwareId();
      print('Dart-generated HWID: $dartHwid');
      
      // Create the payload exactly like Python does: serial + ':' + hwid
      final payload = '$testSerial:$dartHwid';
      print('Payload to encrypt: $payload');
      
      // Test encryption
      try {
        final encryptedPayload = await licenseService.testEncryption(payload);
        print('Encryption successful: ${encryptedPayload.length} chars');
        print('First 64 chars: ${encryptedPayload.substring(0, math.min(64, encryptedPayload.length))}');
      } catch (e) {
        print('❌ Encryption failed: $e');
        return;
      }
      
      print('\n=== MAKING LICENSE REQUEST ===');
      
      final result = await licenseService.validateLicense(
        serialNumber: testSerial,
        enableDebug: true,
        timeout: const Duration(seconds: 15),
      );

      print('\n=== VALIDATION RESULT ===');
      print('✅ Request completed');
      print('Is Valid: ${result.isValid}');
      print('Response Code: ${result.responseCode}');
      print('Message: ${result.message}');
      print('HTTP Code: ${result.httpCode ?? 'N/A'}');
      
      if (result.serverResponse != null) {
        print('\n=== FULL SERVER RESPONSE ===');
        result.serverResponse!.forEach((key, value) {
          print('$key: $value');
        });
      }
      
      // Don't fail the test - we're just debugging
      expect(result, isNotNull);
    });

    test('compare request format with Python client', () async {
      print('\n=== REQUEST FORMAT COMPARISON ===');
      print('');
      print('Python client sends:');
      print('{');
      print('  "apiKey": "<your-api-key>",');
      print('  "payload": "<base64-encrypted-data>"');
      print('}');
      print('');
      print('Our Dart client sends:');
      print('{');
      print('  "apiKey": "a080015b-b827-48f8-a96d-dc3ccc650bc8",');
      print('  "payload": "<base64-encrypted-data>"');
      print('}');
      print('');
      print('✅ Request formats match!');
      print('');
      print('Next steps:');
      print('1. Update the API key in license_validation_service.dart');
      print('2. Update the public key in license_validation_service.dart');
      print('3. Test with a known working serial number');
    });
  });
}

/*
INSTRUCTIONS TO FIX THE 401 ERROR:

1. Open lib/services/license_validation_service.dart
2. Replace the _apiKey value with your actual API key
3. Replace the _publicKeyPem value with your actual RSA public key
4. Update the test above with a known working serial number

The values should match exactly what you use in your working Python client.

Current hardcoded values in the service:
- API Key: 'a080015b-b827-48f8-a96d-dc3ccc650bc8'  
- Public Key: The RSA key starting with 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...'

These need to be replaced with your actual server values.
*/
