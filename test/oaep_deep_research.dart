import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:pointycastle/export.dart';
import 'package:basic_utils/basic_utils.dart';

void main() {
  group('OAEP with Custom Digest Test', () {
    test('Investigate OAEP constructor parameters', () {
      const String publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2PQEde9Bw5+slzsLATLS
JmzmsBJwpyDGOCeKhU1k8lqsBJT3KwqfIxk3E9KeEC9bsvH0fV83X7hNYuEiOwq9
eB3vsfmTsWyDq0ml8yjvWvim9rcC97Wz7TE/fNRQjdGTwpG2CHtey6kJwwyr5EKN
n1M5OWftUUp4SQ3u1f4Wcpt1VNGuPL6VUD9bcwocb2vDGjlEwFfD5P8m565pnLsM
X9yN2DfemBNpgrAH18dAEmYsUMy4JYL92rNZ+pEQjfVg9x6lYnV0Q4PBqmLMiDuU
gg9NnFeN5hFksBi3AX/wBjFx9uyit+j+uuTkpASyuJH2jv7av2Y4rgEK8Tb422w1
IwIDAQAB
-----END PUBLIC KEY-----''';

      final publicKey = CryptoUtils.rsaPublicKeyFromPem(publicKeyPem);
      final testData = Uint8List.fromList(utf8.encode('O4T1K-RW2I9-HDAZY-K98BQ:MEHDI'));

      print('=== OAEP Constructor Deep Investigation ===');

      // Let's try to manually construct OAEP with specific parameters
      // Based on pointycastle source code exploration
      
      try {
        print('\\n--- Approach 1: Check if OAEPEncoding accepts more parameters ---');
        
        // Let's try the constructor with reflection or different parameter counts
        // Maybe there's a hidden constructor we haven't found
        
        // Try creating with different engines
        final rsaEngine = RSAEngine();
        print('RSA Engine created: ${rsaEngine.algorithmName}');
        
        // Standard OAEP
        final oaep1 = OAEPEncoding(rsaEngine);
        print('Standard OAEP created');
        
        // OAEP with empty label
        final oaep2 = OAEPEncoding(rsaEngine, Uint8List(0));
        print('OAEP with empty label created');
        
        // Let's see if we can access internal digest somehow
        oaep1.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        final result1 = oaep1.process(testData);
        print('Standard OAEP result: ${base64Encode(result1)}');
        
      } catch (e) {
        print('Approach 1 failed: $e');
      }

      try {
        print('\\n--- Approach 2: Try to find OAEP digest parameters ---');
        
        // Let's check if there are any ways to set digest parameters
        // Maybe through some configuration or factory method
        
        final sha1 = SHA1Digest();
        final sha256 = SHA256Digest();
        
        print('SHA1 available: ${sha1.algorithmName}');
        print('SHA256 available: ${sha256.algorithmName}');
        
        // Check if there are any OAEP-related classes we missed
        print('Looking for OAEP-related classes...');
        
        // Try to see if OAEPEncoding has any methods to change digest
        final oaep = OAEPEncoding(RSAEngine());
        print('OAEP created, checking methods...');
        print('OAEP toString: $oaep');
        print('OAEP runtimeType: ${oaep.runtimeType}');
        
      } catch (e) {
        print('Approach 2 failed: $e');
      }

      try {
        print('\\n--- Approach 3: Check pointycastle version and alternatives ---');
        
        // Maybe we need to check what version we have and if there are alternatives
        print('Current pointycastle classes available:');
        
        // Try to create various digest and padding combinations
        final digests = [
          SHA1Digest(),
          SHA256Digest(),
          SHA512Digest(),
          MD5Digest(),
        ];
        
        for (final digest in digests) {
          print('- ${digest.algorithmName}: digest size ${digest.digestSize}');
        }
        
        // Check if there are other RSA padding options
        print('\\nTrying PKCS1 for comparison:');
        final pkcs1 = PKCS1Encoding(RSAEngine());
        pkcs1.init(true, PublicKeyParameter<RSAPublicKey>(publicKey));
        final pkcs1Result = pkcs1.process(testData);
        print('PKCS1 result length: ${pkcs1Result.length}');
        
      } catch (e) {
        print('Approach 3 failed: $e');
      }
    });

    test('Research alternative crypto libraries', () {
      print('\\n=== Alternative Crypto Library Research ===');
      
      // Let's think about alternatives to pointycastle
      print('Current issue: pointycastle OAEPEncoding seems to use SHA-1 by default');
      print('Python server expects: OAEP with SHA-256');
      print('\\nPossible solutions:');
      print('1. Find a way to use SHA-256 in pointycastle OAEP');
      print('2. Use a different Dart crypto library');
      print('3. Implement OAEP manually');
      print('4. Change the Python server to accept SHA-1 OAEP');
      print('Let\'s explore option 1 first...');
      
      // Maybe pointycastle has newer versions or different APIs
      print('\\nCurrent pointycastle: 4.0.0');
      print('Research needed: Check if newer versions support custom OAEP digests');
    });
  });
}
