import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../lib/main.dart';
import '../lib/providers/license_provider.dart';
import '../lib/services/license_validation_service.dart';
import '../lib/services/database_service.dart';
import '../lib/database/database.dart';

/// Test suite for license verification on app launch
void main() {
  group('License Launch Validation Tests', () {
    late AppDatabase database;
    late LicenseValidationService licenseService;
    late LicenseProvider licenseProvider;

    setUp(() async {
      // Initialize test database
      database = AppDatabase.forTesting();
      licenseService = LicenseValidationService.getInstance(database);
      licenseProvider = LicenseProvider(database);

      // Clear SharedPreferences for clean test state
      SharedPreferences.setMockInitialValues({});
    });

    tearDown(() async {
      await database.close();
    });

    testWidgets('App initialization triggers license validation', (WidgetTester tester) async {
      // Set up a mock license for testing
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(0); // Every launch

      // Build the app
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider.value(value: licenseProvider),
          ],
          child: MaterialApp(
            home: const AppInitializer(),
          ),
        ),
      );

      // Verify that initialization screen is shown
      expect(find.text('Validating license...'), findsOneWidget);
      
      // Wait for initialization to complete
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Verify that license validation was attempted
      // (This will show the license guard screen since no valid license is configured)
      expect(find.byType(Scaffold), findsOneWidget);
    });

    test('Launch validation configuration - force validation enabled', () async {
      // Test enabling force validation
      await licenseService.setForceValidationOnLaunch(true);
      final isForced = await licenseService.shouldForceValidationOnLaunch();
      expect(isForced, true);

      // Test that validation should occur on this launch
      final shouldValidate = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidate, true);
    });

    test('Launch validation configuration - force validation disabled', () async {
      // Test disabling force validation
      await licenseService.setForceValidationOnLaunch(false);
      final isForced = await licenseService.shouldForceValidationOnLaunch();
      expect(isForced, false);

      // Test that validation should not occur on this launch
      final shouldValidate = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidate, false);
    });

    test('Launch validation frequency - every launch', () async {
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(0);
      
      final frequency = await licenseService.getLaunchValidationFrequency();
      expect(frequency, 0);

      final shouldValidate = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidate, true);
    });

    test('Launch validation frequency - daily', () async {
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(24);
      
      final frequency = await licenseService.getLaunchValidationFrequency();
      expect(frequency, 24);

      // First launch should validate
      final shouldValidateFirst = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidateFirst, true);

      // Record that we validated
      await licenseService.recordLaunchValidation();

      // Immediate second check should not validate (within 24 hours)
      final shouldValidateSecond = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidateSecond, false);
    });

    test('License provider launch validation methods', () async {
      // Test configuration methods
      await licenseProvider.setForceValidationOnLaunch(true);
      final isForced = await licenseProvider.isValidationForcedOnLaunch();
      expect(isForced, true);

      await licenseProvider.setLaunchValidationFrequency(12);
      final frequency = await licenseProvider.getLaunchValidationFrequency();
      expect(frequency, 12);
    });

    test('Validation bypassing cache works correctly', () async {
      // This test verifies that the bypass cache method exists and can be called
      // Note: Actual network validation would require a test server
      
      final result = await licenseService.validateOnAppLaunch(enableDebug: true);
      
      // Should return invalid result since no credentials are stored
      expect(result.isValid, false);
      expect(result.responseCode, 'NO_STORED_CREDENTIALS');
      expect(result.message, contains('No license credentials found'));
    });

    test('Smart validation method chooses correct approach', () async {
      // Test when force validation is enabled
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(0);

      final result = await licenseService.validateForAppLaunch(enableDebug: true);
      
      // Should attempt validation (and fail due to no credentials)
      expect(result.isValid, false);
      expect(result.responseCode, 'NO_STORED_CREDENTIALS');

      // Test when force validation is disabled
      await licenseService.setForceValidationOnLaunch(false);
      
      final result2 = await licenseService.validateForAppLaunch(enableDebug: true);
      
      // Should use cached validation (and fail due to no credentials)
      expect(result2.isValid, false);
      expect(result2.responseCode, 'NO_STORED_CREDENTIALS');
    });

    test('Launch validation recording works', () async {
      // Record a launch validation
      await licenseService.recordLaunchValidation();
      
      // Set frequency to 1 hour
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(1);
      
      // Should not validate again immediately
      final shouldValidate = await licenseService.shouldValidateOnThisLaunch();
      expect(shouldValidate, false);
    });

    testWidgets('License provider initialization performs launch validation', (WidgetTester tester) async {
      bool initializationCalled = false;
      
      // Create a custom license provider to track initialization
      final testProvider = LicenseProvider(database);
      
      // Override the initialize method behavior by calling it directly
      await testProvider.initialize();
      initializationCalled = true;
      
      expect(initializationCalled, true);
      // The provider should have attempted validation (even if it failed due to no credentials)
      expect(testProvider.lastValidationResult, isNotNull);
    });
  });

  group('License Launch Validation Error Handling', () {
    late AppDatabase database;
    late LicenseValidationService licenseService;

    setUp(() async {
      database = AppDatabase.forTesting();
      licenseService = LicenseValidationService.getInstance(database);
      SharedPreferences.setMockInitialValues({});
    });

    tearDown(() async {
      await database.close();
    });

    test('Handles missing credentials gracefully', () async {
      final result = await licenseService.validateOnAppLaunch();
      
      expect(result.isValid, false);
      expect(result.responseCode, 'NO_STORED_CREDENTIALS');
      expect(result.message, contains('No license credentials found'));
    });

    test('Handles network timeout gracefully', () async {
      // This test would require mocking HTTP requests
      // For now, we test that the method exists and handles the no-credentials case
      
      final result = await licenseService.validateOnAppLaunch(
        timeout: const Duration(milliseconds: 1), // Very short timeout
      );
      
      expect(result.isValid, false);
      expect(result.responseCode, 'NO_STORED_CREDENTIALS');
    });

    test('Configuration persistence works', () async {
      // Test that configuration survives service recreation
      await licenseService.setForceValidationOnLaunch(true);
      await licenseService.setLaunchValidationFrequency(24);
      
      // Create new service instance (simulating app restart)
      final newService = LicenseValidationService.getInstance(database);
      
      final isForced = await newService.shouldForceValidationOnLaunch();
      final frequency = await newService.getLaunchValidationFrequency();
      
      expect(isForced, true);
      expect(frequency, 24);
    });
  });
}
