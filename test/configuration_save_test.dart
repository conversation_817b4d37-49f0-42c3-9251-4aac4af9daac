import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/configuration_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Configuration Save Tests', () {
    late ConfigurationProvider configProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      configProvider = ConfigurationProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should save and retrieve design service price', () async {
      const testPrice = 75.0;
      
      await configProvider.setDesignServicePrice(testPrice);
      final retrievedPrice = await configProvider.getDesignServicePrice();
      
      expect(retrievedPrice, equals(testPrice));
    });

    test('should save and retrieve custom configuration values', () async {
      const testKey = 'material_rate_steel';
      const testValue = '650.0';
      
      await configProvider.setConfigValue(testKey, testValue);
      final retrievedValue = await configProvider.getConfigValue(testKey);
      
      expect(retrievedValue, equals(testValue));
    });

    test('should save and retrieve multiple material rates', () async {
      final materialRates = {
        'material_rate_acier': '600.0',
        'material_rate_inox': '900.0',
        'material_rate_cuivre': '800.0',
        'material_rate_aluminium': '700.0',
      };
      
      // Save all material rates
      for (final entry in materialRates.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Retrieve and verify all material rates
      for (final entry in materialRates.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        expect(retrievedValue, equals(entry.value));
      }
    });

    test('should save and retrieve gas costs', () async {
      final gasCosts = {
        'gas_cost_oxygene_o2_1_5_mm': '50.0',
        'gas_cost_oxygene_o2_5_10_mm': '75.0',
        'gas_cost_oxygene_o2_10_15_mm': '100.0',
        'gas_cost_oxygene_o2_gt_15_mm': '150.0',
        'gas_cost_azote_n2_1_5_mm': '80.0',
        'gas_cost_azote_n2_5_10_mm': '120.0',
        'gas_cost_azote_n2_10_15_mm': '160.0',
        'gas_cost_azote_n2_gt_15_mm': '200.0',
      };
      
      // Save all gas costs
      for (final entry in gasCosts.entries) {
        await configProvider.setConfigValue(entry.key, entry.value);
      }
      
      // Retrieve and verify all gas costs
      for (final entry in gasCosts.entries) {
        final retrievedValue = await configProvider.getConfigValue(entry.key);
        expect(retrievedValue, equals(entry.value));
      }
    });

    test('should handle non-existent configuration keys', () async {
      final retrievedValue = await configProvider.getConfigValue('non_existent_key');
      expect(retrievedValue, isNull);
    });

    test('should update existing configuration values', () async {
      const testKey = 'test_config';
      const initialValue = '100.0';
      const updatedValue = '200.0';
      
      // Save initial value
      await configProvider.setConfigValue(testKey, initialValue);
      final firstRetrieval = await configProvider.getConfigValue(testKey);
      expect(firstRetrieval, equals(initialValue));
      
      // Update value
      await configProvider.setConfigValue(testKey, updatedValue);
      final secondRetrieval = await configProvider.getConfigValue(testKey);
      expect(secondRetrieval, equals(updatedValue));
    });

    test('should save and retrieve company information', () async {
      const companyName = 'Test Laser Company';
      const companyAddress = '123 Test Street';
      const companyPhone = '+***********-789';
      const companyEmail = '<EMAIL>';
      
      await configProvider.setCompanyName(companyName);
      await configProvider.setCompanyAddress(companyAddress);
      await configProvider.setCompanyPhone(companyPhone);
      await configProvider.setCompanyEmail(companyEmail);
      
      final retrievedName = await configProvider.getCompanyName();
      final retrievedAddress = await configProvider.getCompanyAddress();
      final retrievedPhone = await configProvider.getCompanyPhone();
      final retrievedEmail = await configProvider.getCompanyEmail();
      
      expect(retrievedName, equals(companyName));
      expect(retrievedAddress, equals(companyAddress));
      expect(retrievedPhone, equals(companyPhone));
      expect(retrievedEmail, equals(companyEmail));
    });

    test('should save and retrieve pricing configuration', () async {
      const pricePerMeter = 15.0;
      const minimumOrderValue = 75.0;
      const taxRate = 0.20;
      
      await configProvider.setPricePerMeter(pricePerMeter);
      await configProvider.setMinimumOrderValue(minimumOrderValue);
      await configProvider.setTaxRate(taxRate);
      
      final retrievedPricePerMeter = await configProvider.getPricePerMeter();
      final retrievedMinimumOrder = await configProvider.getMinimumOrderValue();
      final retrievedTaxRate = await configProvider.getTaxRate();
      
      expect(retrievedPricePerMeter, equals(pricePerMeter));
      expect(retrievedMinimumOrder, equals(minimumOrderValue));
      expect(retrievedTaxRate, equals(taxRate));
    });

    test('should save and retrieve default cutting parameters', () async {
      const defaultGasType = 'nitrogen';
      const defaultMaterial = 'aluminum';
      const defaultThickness = 3.0;
      const defaultCuttingSpeed = 150.0;
      const defaultLinearMeters = 15.0;
      
      await configProvider.setDefaultGasType(defaultGasType);
      await configProvider.setDefaultMaterial(defaultMaterial);
      await configProvider.setDefaultThickness(defaultThickness);
      await configProvider.setDefaultCuttingSpeed(defaultCuttingSpeed);
      await configProvider.setDefaultLinearMeters(defaultLinearMeters);
      
      final retrievedGasType = await configProvider.getDefaultGasType();
      final retrievedMaterial = await configProvider.getDefaultMaterial();
      final retrievedThickness = await configProvider.getDefaultThickness();
      final retrievedCuttingSpeed = await configProvider.getDefaultCuttingSpeed();
      final retrievedLinearMeters = await configProvider.getDefaultLinearMeters();
      
      expect(retrievedGasType, equals(defaultGasType));
      expect(retrievedMaterial, equals(defaultMaterial));
      expect(retrievedThickness, equals(defaultThickness));
      expect(retrievedCuttingSpeed, equals(defaultCuttingSpeed));
      expect(retrievedLinearMeters, equals(defaultLinearMeters));
    });
  });
}
