import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/providers/client_provider.dart';
import 'package:laser_cutting_calculator/models/client.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';

void main() {
  group('Client Expenses Calculation Tests', () {
    late ClientProvider clientProvider;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      clientProvider = ClientProvider();
    });

    tearDown(() async {
      await DatabaseService.instance.clearAllData();
    });

    test('should calculate total expenses correctly', () async {
      // Create test clients with different expenses
      final client1 = Client(
        id: '1',
        name: 'Client 1',
        reference: 'REF001',
        projects: 2,
        totalSpent: 1500.0,
        createdAt: DateTime.now(),
      );

      final client2 = Client(
        id: '2',
        name: 'Client 2',
        reference: 'REF002',
        projects: 1,
        totalSpent: 750.0,
        createdAt: DateTime.now(),
      );

      final client3 = Client(
        id: '3',
        name: 'Client 3',
        reference: 'REF003',
        projects: 3,
        totalSpent: 2250.0,
        createdAt: DateTime.now(),
      );

      // Add clients
      await clientProvider.addClient(client1);
      await clientProvider.addClient(client2);
      await clientProvider.addClient(client3);

      // Test total expenses calculation
      expect(clientProvider.totalExpenses, equals(4500.0));
    });

    test('should calculate average expense per client correctly', () async {
      // Create test clients
      final client1 = Client(
        id: '1',
        name: 'Client 1',
        reference: 'REF001',
        projects: 1,
        totalSpent: 1000.0,
        createdAt: DateTime.now(),
      );

      final client2 = Client(
        id: '2',
        name: 'Client 2',
        reference: 'REF002',
        projects: 1,
        totalSpent: 2000.0,
        createdAt: DateTime.now(),
      );

      // Add clients
      await clientProvider.addClient(client1);
      await clientProvider.addClient(client2);

      // Test average calculation
      expect(clientProvider.averageExpensePerClient, equals(1500.0));
    });

    test('should identify top spending client correctly', () async {
      // Create test clients with different spending amounts
      final client1 = Client(
        id: '1',
        name: 'Low Spender',
        reference: 'REF001',
        projects: 1,
        totalSpent: 500.0,
        createdAt: DateTime.now(),
      );

      final client2 = Client(
        id: '2',
        name: 'High Spender',
        reference: 'REF002',
        projects: 1,
        totalSpent: 3000.0,
        createdAt: DateTime.now(),
      );

      final client3 = Client(
        id: '3',
        name: 'Medium Spender',
        reference: 'REF003',
        projects: 1,
        totalSpent: 1500.0,
        createdAt: DateTime.now(),
      );

      // Add clients
      await clientProvider.addClient(client1);
      await clientProvider.addClient(client2);
      await clientProvider.addClient(client3);

      // Test top spending client identification
      final topClient = clientProvider.topSpendingClient;
      expect(topClient, isNotNull);
      expect(topClient!.name, equals('High Spender'));
      expect(topClient.totalSpent, equals(3000.0));
    });

    test('should calculate total projects correctly', () async {
      // Create test clients with different project counts
      final client1 = Client(
        id: '1',
        name: 'Client 1',
        reference: 'REF001',
        projects: 5,
        totalSpent: 1000.0,
        createdAt: DateTime.now(),
      );

      final client2 = Client(
        id: '2',
        name: 'Client 2',
        reference: 'REF002',
        projects: 3,
        totalSpent: 500.0,
        createdAt: DateTime.now(),
      );

      // Add clients
      await clientProvider.addClient(client1);
      await clientProvider.addClient(client2);

      // Test total projects calculation
      expect(clientProvider.totalProjects, equals(8));
    });

    test('should handle empty client list correctly', () {
      // Test with no clients
      expect(clientProvider.totalExpenses, equals(0.0));
      expect(clientProvider.averageExpensePerClient, equals(0.0));
      expect(clientProvider.topSpendingClient, isNull);
      expect(clientProvider.totalProjects, equals(0));
    });

    test('should provide complete client statistics', () async {
      // Create a test client
      final client = Client(
        id: '1',
        name: 'Test Client',
        reference: 'REF001',
        projects: 2,
        totalSpent: 1000.0,
        createdAt: DateTime.now(),
      );

      await clientProvider.addClient(client);

      // Test client statistics
      final stats = clientProvider.clientStatistics;
      expect(stats['totalClients'], equals(1));
      expect(stats['totalExpenses'], equals(1000.0));
      expect(stats['averageExpensePerClient'], equals(1000.0));
      expect(stats['totalProjects'], equals(2));
      expect((stats['topSpendingClient'] as Client).id, equals(client.id));
    });

    test('should update statistics when client expenses change', () async {
      // Create a test client
      final client = Client(
        id: '1',
        name: 'Test Client',
        reference: 'REF001',
        projects: 1,
        totalSpent: 500.0,
        createdAt: DateTime.now(),
      );

      await clientProvider.addClient(client);

      // Initial state
      expect(clientProvider.totalExpenses, equals(500.0));

      // Update client's total spent (this adds to existing amount)
      await clientProvider.updateClientTotalSpent(client.id, 1000.0);

      // Verify statistics are updated (500.0 + 1000.0 = 1500.0)
      expect(clientProvider.totalExpenses, equals(1500.0));
    });
  });
}
