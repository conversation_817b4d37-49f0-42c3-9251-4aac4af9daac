import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/material_service.dart';
import 'package:laser_cutting_calculator/services/gas_service.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';


void main() {
  group('Universal Deletion Tests', () {
    late MaterialService materialService;
    late GasService gasService;
    late MaterialProvider materialProvider;
    late GasProvider gasProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      materialService = MaterialService.instance;
      gasService = GasService.instance;
      materialProvider = MaterialProvider();
      gasProvider = GasProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    group('Material Universal Deletion Tests', () {
      test('should allow deletion of default materials', () async {
        // Get initial materials
        final initialMaterials = await materialService.getAllMaterialRates();
        expect(initialMaterials.containsKey('acier'), isTrue);
        expect(initialMaterials.containsKey('inox'), isTrue);

        // Delete a default material
        final deleteSuccess = await materialService.removeMaterial('acier');
        expect(deleteSuccess, isTrue);

        // Verify it's removed
        final materialsAfterDeletion = await materialService.getAllMaterialRates();
        expect(materialsAfterDeletion.containsKey('acier'), isFalse);
        expect(materialsAfterDeletion.containsKey('inox'), isTrue); // Other materials should remain
      });

      test('should allow deletion of custom materials', () async {
        // Add a custom material
        const customMaterialName = 'Titanium';
        const customMaterialRate = 1200.0;

        final addSuccess = await materialService.addCustomMaterial(customMaterialName, customMaterialRate);
        expect(addSuccess, isTrue);

        // Verify it exists
        final materialsBeforeRemoval = await materialService.getAllMaterialRates();
        expect(materialsBeforeRemoval.containsKey('titanium'), isTrue);

        // Delete the custom material
        final deleteSuccess = await materialService.removeMaterial('titanium');
        expect(deleteSuccess, isTrue);

        // Verify it's removed
        final materialsAfterRemoval = await materialService.getAllMaterialRates();
        expect(materialsAfterRemoval.containsKey('titanium'), isFalse);
      });

      test('MaterialProvider should allow deletion of any material', () async {
        await materialProvider.initialize();

        // Test deleting a default material
        expect(materialProvider.materialRates.containsKey('inox'), isTrue);
        
        final deleteDefaultSuccess = await materialProvider.removeMaterial('inox');
        expect(deleteDefaultSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('inox'), isFalse);

        // Add and delete a custom material
        await materialProvider.addMaterial('Custom Steel', 750.0);
        expect(materialProvider.materialRates.containsKey('custom-steel'), isTrue);

        final deleteCustomSuccess = await materialProvider.removeMaterial('custom-steel');
        expect(deleteCustomSuccess, isTrue);
        expect(materialProvider.materialRates.containsKey('custom-steel'), isFalse);
      });

      test('should persist material deletions across service instances', () async {
        // Delete a default material
        await materialService.removeMaterial('cuivre');

        // Create new service instance
        final newMaterialService = MaterialService();

        // Verify deletion persisted
        final materials = await newMaterialService.getAllMaterialRates();
        expect(materials.containsKey('cuivre'), isFalse);
        expect(materials.containsKey('acier'), isTrue); // Other materials should remain
      });
    });

    group('Gas Type Universal Deletion Tests', () {
      test('should allow deletion of default gas types', () async {
        // Get initial gas types
        final initialGasTypes = await gasService.getAllGasTypes();
        expect(initialGasTypes.containsKey('Oxygène (O₂)'), isTrue);
        expect(initialGasTypes.containsKey('Azote (N₂)'), isTrue);

        // Delete a default gas type
        final deleteSuccess = await gasService.removeGasType('Oxygène (O₂)');
        expect(deleteSuccess, isTrue);

        // Verify it's removed
        final gasTypesAfterDeletion = await gasService.getAllGasTypes();
        expect(gasTypesAfterDeletion.containsKey('Oxygène (O₂)'), isFalse);
        expect(gasTypesAfterDeletion.containsKey('Azote (N₂)'), isTrue); // Other gas types should remain
      });

      test('should allow deletion of custom gas types', () async {
        // Add a custom gas type
        const customGasName = 'Argon';

        final addSuccess = await gasService.addCustomGasType(customGasName);
        expect(addSuccess, isTrue);

        // Verify it exists
        final gasTypesBeforeRemoval = await gasService.getAllGasTypes();
        expect(gasTypesBeforeRemoval.containsKey(customGasName), isTrue);

        // Delete the custom gas type
        final deleteSuccess = await gasService.removeGasType(customGasName);
        expect(deleteSuccess, isTrue);

        // Verify it's removed
        final gasTypesAfterRemoval = await gasService.getAllGasTypes();
        expect(gasTypesAfterRemoval.containsKey(customGasName), isFalse);
      });

      test('GasProvider should allow deletion of any gas type', () async {
        await gasProvider.initialize();

        // Test deleting a default gas type
        expect(gasProvider.gasTypes.containsKey('Azote (N₂)'), isTrue);
        
        final deleteDefaultSuccess = await gasProvider.removeGasType('Azote (N₂)');
        expect(deleteDefaultSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey('Azote (N₂)'), isFalse);

        // Add and delete a custom gas type
        await gasProvider.addGasType('Helium');
        expect(gasProvider.gasTypes.containsKey('Helium'), isTrue);

        final deleteCustomSuccess = await gasProvider.removeGasType('Helium');
        expect(deleteCustomSuccess, isTrue);
        expect(gasProvider.gasTypes.containsKey('Helium'), isFalse);
      });

      test('should persist gas type deletions across service instances', () async {
        // Delete a default gas type
        await gasService.removeGasType('Air Comprimé');

        // Create new service instance
        final newGasService = GasService();

        // Verify deletion persisted
        final gasTypes = await newGasService.getAllGasTypes();
        expect(gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(gasTypes.containsKey('Oxygène (O₂)'), isTrue); // Other gas types should remain
      });

      test('should remove all thickness ranges when deleting gas type', () async {
        // Add a custom gas type
        await gasService.addCustomGasType('Test Gas');
        
        // Verify it has all thickness ranges
        final gasTypesBeforeRemoval = await gasService.getAllGasTypes();
        expect(gasTypesBeforeRemoval['Test Gas']?.keys.length, equals(4));

        // Delete the gas type
        await gasService.removeGasType('Test Gas');

        // Verify it's completely removed (no partial thickness ranges left)
        final gasTypesAfterRemoval = await gasService.getAllGasTypes();
        expect(gasTypesAfterRemoval.containsKey('Test Gas'), isFalse);
      });
    });

    group('Cross-Service Universal Deletion Tests', () {
      test('should handle deletion of both materials and gas types', () async {
        await materialProvider.initialize();
        await gasProvider.initialize();

        // Delete default materials and gas types
        await materialProvider.removeMaterial('aluminum');
        await gasProvider.removeGasType('Air Comprimé');

        // Add and delete custom items
        await materialProvider.addMaterial('Custom Material', 500.0);
        await gasProvider.addGasType('Custom Gas');

        await materialProvider.removeMaterial('custom-material');
        await gasProvider.removeGasType('Custom Gas');

        // Verify all deletions worked
        expect(materialProvider.materialRates.containsKey('aluminum'), isFalse);
        expect(materialProvider.materialRates.containsKey('custom-material'), isFalse);
        expect(gasProvider.gasTypes.containsKey('Air Comprimé'), isFalse);
        expect(gasProvider.gasTypes.containsKey('Custom Gas'), isFalse);

        // Verify some defaults remain
        expect(materialProvider.materialRates.containsKey('acier'), isTrue);
        expect(gasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue);
      });

      test('should allow complete cleanup of all materials and gas types', () async {
        await materialProvider.initialize();
        await gasProvider.initialize();

        // Delete all default materials
        final allMaterialKeys = materialProvider.materialRates.keys.toList();
        for (final key in allMaterialKeys) {
          await materialProvider.removeMaterial(key);
        }

        // Delete all default gas types
        final allGasTypeKeys = gasProvider.gasTypes.keys.toList();
        for (final key in allGasTypeKeys) {
          await gasProvider.removeGasType(key);
        }

        // Verify everything is deleted
        expect(materialProvider.materialRates.isEmpty, isTrue);
        expect(gasProvider.gasTypes.isEmpty, isTrue);
      });

      test('should allow rebuilding after complete deletion', () async {
        await materialProvider.initialize();
        await gasProvider.initialize();

        // Delete everything
        final allMaterialKeys = materialProvider.materialRates.keys.toList();
        for (final key in allMaterialKeys) {
          await materialProvider.removeMaterial(key);
        }

        final allGasTypeKeys = gasProvider.gasTypes.keys.toList();
        for (final key in allGasTypeKeys) {
          await gasProvider.removeGasType(key);
        }

        // Add new custom items
        await materialProvider.addMaterial('New Steel', 600.0);
        await gasProvider.addGasType('New Gas');

        // Verify new items exist
        expect(materialProvider.materialRates.containsKey('new-steel'), isTrue);
        expect(materialProvider.materialRates['new-steel'], equals(600.0));
        expect(gasProvider.gasTypes.containsKey('New Gas'), isTrue);
        expect(gasProvider.gasTypes['New Gas']?.keys.length, equals(4)); // All thickness ranges
      });
    });
  });
}
