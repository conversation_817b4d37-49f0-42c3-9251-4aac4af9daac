import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/license_validation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('RSA Encryption Comparison', () {
    late LicenseValidationService licenseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      licenseService = LicenseValidationService.instance;
      SharedPreferences.setMockInitialValues({});
    });

    test('compare Dart vs Python encryption', () async {
      print('\n=== RSA ENCRYPTION COMPARISON ===');
      
      // Exact payload from your working Python client
      const payload = 'O4T1K-RW2I9-HDAZY-K98BQ:MEHDI';
      
      print('Testing payload: "$payload"');
      print('Payload bytes: ${utf8.encode(payload)}');
      print('Payload length: ${payload.length} characters');
      
      // Test encryption multiple times to see if it's deterministic
      print('\n--- Multiple encryption attempts ---');
      for (int i = 1; i <= 3; i++) {
        final encrypted = await licenseService.testEncryption(payload);
        print('Attempt $i:');
        print('  Length: ${encrypted.length}');
        print('  First 32 chars: ${encrypted.substring(0, 32)}');
        print('  Last 32 chars: ${encrypted.substring(encrypted.length - 32)}');
        
        // Decode and check raw bytes length
        final rawBytes = base64Decode(encrypted);
        print('  Raw bytes length: ${rawBytes.length}');
      }
      
      print('\n--- Expected Python output ---');
      print('Your Python client should generate a base64 string of 344 characters');
      print('The raw encrypted bytes should be 256 bytes (2048-bit RSA)');
      
      print('\n=== DEBUGGING RSA PADDING ===');
      print('Our Dart implementation uses:');
      print('- OAEPEncoding(RSAEngine())');
      print('- Default parameters (should use SHA1 for hash and MGF1)');
      print('');
      print('Python uses:');
      print('- padding.OAEP(mgf=MGF1(SHA256), algorithm=SHA256, label=None)');
      print('');
      print('❌ MISMATCH FOUND: Python uses SHA256, Dart likely uses SHA1!');
    });

    test('test with raw HTTP request like Python', () async {
      print('\n=== RAW HTTP REQUEST TEST ===');
      
      // Let's see what our exact request looks like
      const payload = 'O4T1K-RW2I9-HDAZY-K98BQ:MEHDI';
      final encryptedPayload = await licenseService.testEncryption(payload);
      
      final requestBody = {
        'apiKey': 'a080015b-b827-48f8-a96d-dc3ccc650bc8',
        'payload': encryptedPayload,
      };
      
      print('Our Dart request body:');
      print(jsonEncode(requestBody));
      print('');
      print('Python equivalent would be:');
      print('requests.post("http://localhost:8000/api/v1/validate", json={');
      print('    "apiKey": "a080015b-b827-48f8-a96d-dc3ccc650bc8",');
      print('    "payload": "<encrypted-data>"');
      print('})');
      
      expect(requestBody['apiKey'], 'a080015b-b827-48f8-a96d-dc3ccc650bc8');
      expect(requestBody['payload'], isNotEmpty);
    });
  });
}
