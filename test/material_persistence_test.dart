import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/material_service.dart';
import 'package:laser_cutting_calculator/providers/material_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/constants/app_constants.dart';

void main() {
  group('Material Persistence Tests', () {
    late MaterialService materialService;
    late MaterialProvider materialProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      materialService = MaterialService.instance;
      materialProvider = MaterialProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should load default materials', () async {
      final materialRates = await materialService.getAllMaterialRates();
      final materialLabels = await materialService.getAllMaterialLabels();

      // Should include all default materials
      for (final key in AppConstants.materialRates.keys) {
        expect(materialRates.containsKey(key), isTrue);
        expect(materialLabels.containsKey(key), isTrue);
      }

      // Should have correct default values
      expect(materialRates['acier'], equals(AppConstants.materialRates['acier']));
      expect(materialLabels['acier'], equals(AppConstants.materialLabels['acier']));
    });

    test('should add and persist custom materials', () async {
      const customMaterialName = 'Titanium';
      const customMaterialRate = 1200.0;

      // Add custom material
      final success = await materialService.addCustomMaterial(customMaterialName, customMaterialRate);
      expect(success, isTrue);

      // Verify it's saved in database
      final allMaterials = await materialService.getAllMaterialRates();
      expect(allMaterials.containsKey('titanium'), isTrue);
      expect(allMaterials['titanium'], equals(customMaterialRate));

      // Verify labels are generated correctly
      final allLabels = await materialService.getAllMaterialLabels();
      expect(allLabels.containsKey('titanium'), isTrue);
      expect(allLabels['titanium'], equals('Titanium'));
    });

    test('should persist custom materials across service instances', () async {
      const customMaterialName = 'Brass';
      const customMaterialRate = 850.0;

      // Add custom material with first instance
      await materialService.addCustomMaterial(customMaterialName, customMaterialRate);

      // Create new service instance
      final newMaterialService = MaterialService();

      // Verify custom material is still available
      final allMaterials = await newMaterialService.getAllMaterialRates();
      expect(allMaterials.containsKey('brass'), isTrue);
      expect(allMaterials['brass'], equals(customMaterialRate));
    });

    test('should update material rates', () async {
      const materialKey = 'acier';
      const newRate = 650.0;

      // Update material rate
      final success = await materialService.updateMaterialRate(materialKey, newRate);
      expect(success, isTrue);

      // Verify rate was updated
      final updatedRate = await materialService.getMaterialRate(materialKey);
      expect(updatedRate, equals(newRate));

      // Verify it's in the full materials list
      final allMaterials = await materialService.getAllMaterialRates();
      expect(allMaterials[materialKey], equals(newRate));
    });

    test('should remove custom materials', () async {
      const customMaterialName = 'Test Material';
      const customMaterialRate = 500.0;

      // Add custom material
      await materialService.addCustomMaterial(customMaterialName, customMaterialRate);

      // Verify it exists
      final materialsBeforeRemoval = await materialService.getAllMaterialRates();
      expect(materialsBeforeRemoval.containsKey('test-material'), isTrue);

      // Remove custom material
      final success = await materialService.removeCustomMaterial('test-material');
      expect(success, isTrue);

      // Verify it's removed
      final materialsAfterRemoval = await materialService.getAllMaterialRates();
      expect(materialsAfterRemoval.containsKey('test-material'), isFalse);
    });

    test('should identify custom vs default materials', () {
      // Default materials should not be custom
      expect(materialService.isCustomMaterial('acier'), isFalse);
      expect(materialService.isCustomMaterial('inox'), isFalse);

      // Custom materials should be custom
      expect(materialService.isCustomMaterial('titanium'), isTrue);
      expect(materialService.isCustomMaterial('brass'), isTrue);
    });

    test('MaterialProvider should initialize correctly', () async {
      // Add some custom materials first
      await materialService.addCustomMaterial('Titanium', 1200.0);
      await materialService.addCustomMaterial('Brass', 850.0);

      // Initialize provider
      await materialProvider.initialize();

      expect(materialProvider.isLoading, isFalse);
      expect(materialProvider.error, isNull);

      // Should include default materials
      expect(materialProvider.materialRates.containsKey('acier'), isTrue);
      expect(materialProvider.materialLabels.containsKey('acier'), isTrue);

      // Should include custom materials
      expect(materialProvider.materialRates.containsKey('titanium'), isTrue);
      expect(materialProvider.materialRates.containsKey('brass'), isTrue);
      expect(materialProvider.materialLabels.containsKey('titanium'), isTrue);
      expect(materialProvider.materialLabels.containsKey('brass'), isTrue);
    });

    test('MaterialProvider should add materials correctly', () async {
      await materialProvider.initialize();

      const materialName = 'Carbon Steel';
      const materialRate = 680.0;

      final success = await materialProvider.addMaterial(materialName, materialRate);
      expect(success, isTrue);

      // Should be available in provider
      expect(materialProvider.materialRates.containsKey('carbon-steel'), isTrue);
      expect(materialProvider.materialRates['carbon-steel'], equals(materialRate));
      expect(materialProvider.materialLabels.containsKey('carbon-steel'), isTrue);
    });

    test('MaterialProvider should remove materials correctly', () async {
      await materialProvider.initialize();

      // Add a material first
      await materialProvider.addMaterial('Test Material', 500.0);
      expect(materialProvider.materialRates.containsKey('test-material'), isTrue);

      // Remove it
      final success = await materialProvider.removeMaterial('test-material');
      expect(success, isTrue);

      // Should be removed from provider
      expect(materialProvider.materialRates.containsKey('test-material'), isFalse);
      expect(materialProvider.materialLabels.containsKey('test-material'), isFalse);
    });

    test('MaterialProvider should update rates correctly', () async {
      await materialProvider.initialize();

      const materialKey = 'acier';
      const newRate = 625.0;

      final success = await materialProvider.updateMaterialRate(materialKey, newRate);
      expect(success, isTrue);

      // Should be updated in provider
      expect(materialProvider.materialRates[materialKey], equals(newRate));
    });

    test('should handle complex material names correctly', () async {
      final testCases = {
        'Stainless Steel 316': 'stainless-steel-316',
        'Carbon Fiber': 'carbon-fiber',
        'High Grade Aluminum': 'high-grade-aluminum',
        'Titanium Alloy': 'titanium-alloy',
      };

      for (final entry in testCases.entries) {
        final success = await materialService.addCustomMaterial(entry.key, 1000.0);
        expect(success, isTrue);

        final allMaterials = await materialService.getAllMaterialRates();
        expect(allMaterials.containsKey(entry.value), isTrue);

        final allLabels = await materialService.getAllMaterialLabels();
        expect(allLabels.containsKey(entry.value), isTrue);
        expect(allLabels[entry.value], equals(entry.key));
      }
    });
  });
}
