import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/services/gas_service.dart';
import 'package:laser_cutting_calculator/providers/gas_provider.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/constants/app_constants.dart';

void main() {
  group('Gas Persistence Tests', () {
    late GasService gasService;
    late GasProvider gasProvider;
    late DatabaseService databaseService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      gasService = GasService.instance;
      gasProvider = GasProvider();
      databaseService = DatabaseService.instance;
    });

    tearDown(() async {
      await databaseService.clearAllData();
    });

    test('should load default gas types', () async {
      final gasTypes = await gasService.getAllGasTypes();
      final gasLabels = await gasService.getAllGasLabels();

      // Should include all default gas types
      for (final key in AppConstants.gasTypes.keys) {
        expect(gasTypes.containsKey(key), isTrue);
        expect(gasLabels.containsKey(key), isTrue);
      }

      // Should have correct default values
      expect(gasTypes['Oxygène (O₂)']?['1-5 mm'], equals(AppConstants.gasTypes['Oxygène (O₂)']?['1-5 mm']));
      expect(gasLabels['oxygen'], equals(AppConstants.gasLabels['oxygen']));
    });

    test('should add and persist custom gas types', () async {
      const customGasName = 'Argon';

      // Add custom gas type
      final success = await gasService.addCustomGasType(customGasName);
      expect(success, isTrue);

      // Verify it's saved in database
      final allGasTypes = await gasService.getAllGasTypes();
      expect(allGasTypes.containsKey(customGasName), isTrue);

      // Should have all thickness ranges with default 0.0 values
      expect(allGasTypes[customGasName]?['1-5 mm'], equals(0.0));
      expect(allGasTypes[customGasName]?['5-10 mm'], equals(0.0));
      expect(allGasTypes[customGasName]?['10-15 mm'], equals(0.0));
      expect(allGasTypes[customGasName]?['> 15 mm'], equals(0.0));

      // Verify labels are generated correctly
      final allLabels = await gasService.getAllGasLabels();
      expect(allLabels.containsKey(customGasName), isTrue);
      expect(allLabels[customGasName], equals(customGasName));
    });

    test('should persist custom gas types across service instances', () async {
      const customGasName = 'Helium';

      // Add custom gas type with first instance
      await gasService.addCustomGasType(customGasName);

      // Create new service instance
      final newGasService = GasService();

      // Verify custom gas type is still available
      final allGasTypes = await newGasService.getAllGasTypes();
      expect(allGasTypes.containsKey(customGasName), isTrue);
      expect(allGasTypes[customGasName]?['1-5 mm'], equals(0.0));
    });

    test('should update gas costs for specific thickness ranges', () async {
      const gasName = 'Oxygène (O₂)';
      const thicknessRange = '1-5 mm';
      const newCost = 60.0;

      // Update gas cost
      final success = await gasService.updateGasCost(gasName, thicknessRange, newCost);
      expect(success, isTrue);

      // Verify cost was updated
      final updatedCost = await gasService.getGasCost(gasName, thicknessRange);
      expect(updatedCost, equals(newCost));

      // Verify it's in the full gas types list
      final allGasTypes = await gasService.getAllGasTypes();
      expect(allGasTypes[gasName]?[thicknessRange], equals(newCost));
    });

    test('should remove custom gas types', () async {
      const customGasName = 'Test Gas';

      // Add custom gas type
      await gasService.addCustomGasType(customGasName);

      // Verify it exists
      final gasTypesBeforeRemoval = await gasService.getAllGasTypes();
      expect(gasTypesBeforeRemoval.containsKey(customGasName), isTrue);

      // Remove custom gas type
      final success = await gasService.removeCustomGasType(customGasName);
      expect(success, isTrue);

      // Verify it's removed
      final gasTypesAfterRemoval = await gasService.getAllGasTypes();
      expect(gasTypesAfterRemoval.containsKey(customGasName), isFalse);
    });

    test('should identify custom vs default gas types', () {
      // Default gas types should not be custom
      expect(gasService.isCustomGasType('Oxygène (O₂)'), isFalse);
      expect(gasService.isCustomGasType('Azote (N₂)'), isFalse);

      // Custom gas types should be custom
      expect(gasService.isCustomGasType('Argon'), isTrue);
      expect(gasService.isCustomGasType('Helium'), isTrue);
    });

    test('GasProvider should initialize correctly', () async {
      // Add some custom gas types first
      await gasService.addCustomGasType('Argon');
      await gasService.addCustomGasType('Helium');

      // Initialize provider
      await gasProvider.initialize();

      expect(gasProvider.isLoading, isFalse);
      expect(gasProvider.error, isNull);

      // Should include default gas types
      expect(gasProvider.gasTypes.containsKey('Oxygène (O₂)'), isTrue);
      expect(gasProvider.gasLabels.containsKey('oxygen'), isTrue);

      // Should include custom gas types
      expect(gasProvider.gasTypes.containsKey('Argon'), isTrue);
      expect(gasProvider.gasTypes.containsKey('Helium'), isTrue);
      expect(gasProvider.gasLabels.containsKey('Argon'), isTrue);
      expect(gasProvider.gasLabels.containsKey('Helium'), isTrue);
    });

    test('GasProvider should add gas types correctly', () async {
      await gasProvider.initialize();

      const gasName = 'Carbon Dioxide';

      final success = await gasProvider.addGasType(gasName);
      expect(success, isTrue);

      // Should be available in provider
      expect(gasProvider.gasTypes.containsKey(gasName), isTrue);
      expect(gasProvider.gasLabels.containsKey(gasName), isTrue);

      // Should have all thickness ranges
      expect(gasProvider.gasTypes[gasName]?['1-5 mm'], equals(0.0));
      expect(gasProvider.gasTypes[gasName]?['5-10 mm'], equals(0.0));
      expect(gasProvider.gasTypes[gasName]?['10-15 mm'], equals(0.0));
      expect(gasProvider.gasTypes[gasName]?['> 15 mm'], equals(0.0));
    });

    test('GasProvider should remove gas types correctly', () async {
      await gasProvider.initialize();

      // Add a gas type first
      await gasProvider.addGasType('Test Gas');
      expect(gasProvider.gasTypes.containsKey('Test Gas'), isTrue);

      // Remove it
      final success = await gasProvider.removeGasType('Test Gas');
      expect(success, isTrue);

      // Should be removed from provider
      expect(gasProvider.gasTypes.containsKey('Test Gas'), isFalse);
      expect(gasProvider.gasLabels.containsKey('Test Gas'), isFalse);
    });

    test('GasProvider should update gas costs correctly', () async {
      await gasProvider.initialize();

      const gasName = 'Oxygène (O₂)';
      const thicknessRange = '1-5 mm';
      const newCost = 65.0;

      final success = await gasProvider.updateGasCost(gasName, thicknessRange, newCost);
      expect(success, isTrue);

      // Should be updated in provider
      expect(gasProvider.gasTypes[gasName]?[thicknessRange], equals(newCost));
    });

    test('should handle complex gas type names correctly', () async {
      final testCases = {
        'Argon (Ar)': 'Argon (Ar)',
        'Carbon Dioxide (CO₂)': 'Carbon Dioxide (CO₂)',
        'Mixed Gas 80/20': 'Mixed Gas 80/20',
        'High Purity Nitrogen': 'High Purity Nitrogen',
      };

      for (final entry in testCases.entries) {
        final success = await gasService.addCustomGasType(entry.key);
        expect(success, isTrue);

        final allGasTypes = await gasService.getAllGasTypes();
        expect(allGasTypes.containsKey(entry.key), isTrue);

        final allLabels = await gasService.getAllGasLabels();
        expect(allLabels.containsKey(entry.key), isTrue);
        expect(allLabels[entry.key], equals(entry.value));
      }
    });

    test('should update costs for all thickness ranges', () async {
      const gasName = 'Argon';
      await gasService.addCustomGasType(gasName);

      final costs = {
        '1-5 mm': 70.0,
        '5-10 mm': 100.0,
        '10-15 mm': 130.0,
        '> 15 mm': 180.0,
      };

      // Update all costs
      for (final entry in costs.entries) {
        final success = await gasService.updateGasCost(gasName, entry.key, entry.value);
        expect(success, isTrue);
      }

      // Verify all costs were updated
      final allGasTypes = await gasService.getAllGasTypes();
      for (final entry in costs.entries) {
        expect(allGasTypes[gasName]?[entry.key], equals(entry.value));
      }
    });
  });
}
