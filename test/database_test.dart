import 'package:flutter_test/flutter_test.dart';
import 'package:laser_cutting_calculator/models/client.dart';
import 'package:laser_cutting_calculator/models/calculation.dart';
import 'package:laser_cutting_calculator/services/database_service.dart';
import 'package:laser_cutting_calculator/repositories/client_repository.dart';
import 'package:laser_cutting_calculator/repositories/calculation_history_repository.dart';
import 'package:laser_cutting_calculator/repositories/configuration_repository.dart';

void main() {
  group('Database Integration Tests', () {
    late DatabaseService databaseService;
    late ClientRepository clientRepository;
    late CalculationHistoryRepository calculationRepository;
    late ConfigurationRepository configRepository;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      DatabaseService.setTestMode(true);
    });

    setUp(() {
      databaseService = DatabaseService.instance;
      clientRepository = ClientRepository();
      calculationRepository = CalculationHistoryRepository();
      configRepository = ConfigurationRepository();
    });

    tearDown(() async {
      // Clean up database after each test
      await databaseService.clearAllData();
    });

    group('Client Repository Tests', () {
      test('should add and retrieve client', () async {
        final client = Client(
          id: 'test-1',
          name: 'Test Client',
          reference: 'TC-001',
          company: 'Test Company',
          email: '<EMAIL>',
          phone: '+1234567890',
          projects: 0,
          totalSpent: 0.0,
          createdAt: DateTime.now(),
        );

        await clientRepository.addClient(client);
        final retrievedClient = await clientRepository.getClientById('test-1');

        expect(retrievedClient, isNotNull);
        expect(retrievedClient!.name, equals('Test Client'));
        expect(retrievedClient.reference, equals('TC-001'));
        expect(retrievedClient.company, equals('Test Company'));
      });

      test('should update client', () async {
        final client = Client(
          id: 'test-2',
          name: 'Original Name',
          reference: 'TC-002',
          projects: 0,
          totalSpent: 0.0,
          createdAt: DateTime.now(),
        );

        await clientRepository.addClient(client);
        
        final updatedClient = client.copyWith(
          name: 'Updated Name',
          projects: 5,
          totalSpent: 1000.0,
        );
        
        await clientRepository.updateClient(updatedClient);
        final retrievedClient = await clientRepository.getClientById('test-2');

        expect(retrievedClient!.name, equals('Updated Name'));
        expect(retrievedClient.projects, equals(5));
        expect(retrievedClient.totalSpent, equals(1000.0));
      });

      test('should delete client', () async {
        final client = Client(
          id: 'test-3',
          name: 'To Delete',
          reference: 'TC-003',
          projects: 0,
          totalSpent: 0.0,
          createdAt: DateTime.now(),
        );

        await clientRepository.addClient(client);
        await clientRepository.deleteClient('test-3');
        final retrievedClient = await clientRepository.getClientById('test-3');

        expect(retrievedClient, isNull);
      });

      test('should check reference uniqueness', () async {
        final client = Client(
          id: 'test-4',
          name: 'Test Client',
          reference: 'UNIQUE-001',
          projects: 0,
          totalSpent: 0.0,
          createdAt: DateTime.now(),
        );

        await clientRepository.addClient(client);
        
        final isUnique = await clientRepository.isReferenceUnique('UNIQUE-001');
        final isUniqueExcluding = await clientRepository.isReferenceUnique('UNIQUE-001', excludeId: 'test-4');
        final isNewUnique = await clientRepository.isReferenceUnique('NEW-001');

        expect(isUnique, isFalse);
        expect(isUniqueExcluding, isTrue);
        expect(isNewUnique, isTrue);
      });
    });

    group('Calculation Repository Tests', () {
      test('should add and retrieve calculation', () async {
        final calculation = Calculation(
          id: 'calc-1',
          clientName: 'Test Client',
          clientReference: 'TC-001',
          material: 'steel',
          thickness: 2.0,
          gasType: 'oxygen',
          linearMeters: 10.0,
          cuttingSpeed: 100.0,
          designProvided: true,
          designReference: 'design-1.dwg',
          totalPrice: 150.0,
          createdAt: DateTime.now(),
          status: CalculationStatus.pending,
          // Add required new fields
          cuttingDurationHours: 1.0,
          setupFees: 25.0,
          machineCost: 90.0,
          gasCost: 20.0,
          designCost: 15.0,
        );

        await calculationRepository.addCalculation(calculation);
        final retrieved = await calculationRepository.getCalculationById('calc-1');

        expect(retrieved, isNotNull);
        expect(retrieved!.clientName, equals('Test Client'));
        expect(retrieved.material, equals('steel'));
        expect(retrieved.totalPrice, equals(150.0));
        expect(retrieved.status, equals(CalculationStatus.pending));
      });

      test('should filter calculations by status', () async {
        final calc1 = Calculation(
          id: 'calc-1',
          clientName: 'Client 1',
          clientReference: 'C1',
          material: 'steel',
          thickness: 2.0,
          gasType: 'oxygen',
          linearMeters: 10.0,
          cuttingSpeed: 100.0,
          designProvided: false,
          totalPrice: 100.0,
          createdAt: DateTime.now(),
          status: CalculationStatus.completed,
          // Add required new fields
          cuttingDurationHours: 1.0,
          setupFees: 20.0,
          machineCost: 60.0,
          gasCost: 15.0,
          designCost: 5.0,
        );

        final calc2 = Calculation(
          id: 'calc-2',
          clientName: 'Client 2',
          clientReference: 'C2',
          material: 'aluminum',
          thickness: 1.0,
          gasType: 'air',
          linearMeters: 5.0,
          cuttingSpeed: 150.0,
          designProvided: false,
          totalPrice: 75.0,
          createdAt: DateTime.now(),
          status: CalculationStatus.pending,
          // Add required new fields
          cuttingDurationHours: 0.5,
          setupFees: 15.0,
          machineCost: 45.0,
          gasCost: 10.0,
          designCost: 5.0,
        );

        await calculationRepository.addCalculation(calc1);
        await calculationRepository.addCalculation(calc2);

        final completedCalcs = await calculationRepository.getCalculationsByStatus(CalculationStatus.completed);
        final pendingCalcs = await calculationRepository.getCalculationsByStatus(CalculationStatus.pending);

        expect(completedCalcs.length, equals(1));
        expect(pendingCalcs.length, equals(1));
        expect(completedCalcs.first.id, equals('calc-1'));
        expect(pendingCalcs.first.id, equals('calc-2'));
      });
    });

    group('Configuration Repository Tests', () {
      test('should set and get configuration values', () async {
        await configRepository.setConfigValue('test_key', 'test_value');
        final value = await configRepository.getConfigValue('test_key');

        expect(value, equals('test_value'));
      });

      test('should handle typed configuration values', () async {
        await configRepository.setDefaultThickness(2.5);
        await configRepository.setDefaultCuttingSpeed(120.0);
        await configRepository.setDefaultGasType('nitrogen');

        final thickness = await configRepository.getDefaultThickness();
        final speed = await configRepository.getDefaultCuttingSpeed();
        final gasType = await configRepository.getDefaultGasType();

        expect(thickness, equals(2.5));
        expect(speed, equals(120.0));
        expect(gasType, equals('nitrogen'));
      });

      test('should set and get configuration values', () async {
        await configRepository.setConfigValue('test_key', 'test_value');
        final value = await configRepository.getConfigValue('test_key');
        expect(value, equals('test_value'));

        // Test default values when not set
        final gasType = await configRepository.getDefaultGasType();
        final material = await configRepository.getDefaultMaterial();
        final thickness = await configRepository.getDefaultThickness();

        // These may be null if not configured
        expect(gasType, isA<String?>());
        expect(material, isA<String?>());
        expect(thickness, isA<double>());
      });
    });
  });
}
